# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.0.0.tgz#06e2ab19bdb535385559aabb5ba59729482800f8"
  integrity sha1-BuKrGb21NThVWaq7W6WXKUgoAPg=
  dependencies:
    "@babel/highlight" "^7.0.0"

"@babel/highlight@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/highlight/download/@babel/highlight-7.0.0.tgz#f710c38c8d458e6dd9a201afb637fcb781ce99e4"
  integrity sha1-9xDDjI1Fjm3ZogGvtjf8t4HOmeQ=
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^4.0.0"

"@iamstarkov/listr-update-renderer@0.4.1":
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/@iamstarkov/listr-update-renderer/download/@iamstarkov/listr-update-renderer-0.4.1.tgz#d7c48092a2dcf90fd672b6c8b458649cb350c77e"
  integrity sha1-18SAkqLc+Q/WcrbItFhknLNQx34=
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^2.3.0"
    strip-ansi "^3.0.1"

"@samverschueren/stream-to-observable@^0.3.0":
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.0.tgz#ecdf48d532c58ea477acfcab80348424f8d0662f"
  integrity sha1-7N9I1TLFjqR3rPyrgDSEJPjQZi8=
  dependencies:
    any-observable "^0.3.0"

acorn-jsx@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/acorn-jsx/download/acorn-jsx-3.0.1.tgz#afdf9488fb1ecefc8348f6fb22f464e32a58b36b"
  integrity sha1-r9+UiPsezvyDSPb7IvRk4ypYs2s=
  dependencies:
    acorn "^3.0.4"

acorn-jsx@^5.0.0:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/acorn-jsx/download/acorn-jsx-5.0.1.tgz#32a064fd925429216a09b141102bfdd185fae40e"
  integrity sha1-MqBk/ZJUKSFqCbFBECv90YX65A4=

acorn@^3.0.4:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"
  integrity sha1-ReN/s56No/JbruP/U2niu18iAXo=

acorn@^5.5.0:
  version "5.7.3"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-5.7.3.tgz#67aa231bf8812974b85235a96771eb6bd07ea279"
  integrity sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk=

acorn@^6.0.2:
  version "6.0.4"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-6.0.4.tgz#77377e7353b72ec5104550aa2d2097a2fd40b754"
  integrity sha1-dzd+c1O3LsUQRVCqLSCXov1At1Q=

ajv-keywords@^1.0.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/ajv-keywords/download/ajv-keywords-1.5.1.tgz#314dd0a4b3368fad3dfcdc54ede6171b886daf3c"
  integrity sha1-MU3QpLM2j609/NxU7eYXG4htrzw=

ajv@^4.7.0:
  version "4.11.8"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-4.11.8.tgz#82ffb02b29e662ae53bdc20af15947706739c536"
  integrity sha1-gv+wKynmYq5TvcIK8VlHcGc5xTY=
  dependencies:
    co "^4.6.0"
    json-stable-stringify "^1.0.1"

ajv@^6.5.3, ajv@^6.6.1:
  version "6.6.2"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-6.6.2.tgz#caceccf474bf3fc3ce3b147443711a24063cc30d"
  integrity sha1-ys7M9HS/P8POOxR0Q3EaJAY8ww0=
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ansi-escapes@^1.1.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-1.4.0.tgz#d3a8a83b319aa67793662b13e761c7911422306e"
  integrity sha1-06ioOzGapneTZisT52HHkRQiMG4=

ansi-escapes@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-3.1.0.tgz#f73207bb81207d75fd6c83f125af26eea378ca30"
  integrity sha1-9zIHu4EgfXX9bIPxJa8m7qN4yjA=

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-4.0.0.tgz#70de791edf021404c3fd615aa89118ae0432e5a9"
  integrity sha1-cN55Ht8CFATD/WFaqJEYrgQy5ak=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

any-observable@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/any-observable/download/any-observable-0.3.0.tgz#af933475e5806a67d0d7df090dd5e8bef65d119b"
  integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-union@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

atob@^2.1.1:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

babel-code-frame@^6.22.0:
  version "6.26.0"
  resolved "http://registry.npm.qima-inc.com/babel-code-frame/download/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  integrity sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base@^0.11.1:
  version "0.11.2"
  resolved "http://registry.npm.qima-inc.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.qima-inc.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

buffer-from@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

builtin-modules@^1.0.0, builtin-modules@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/builtin-modules/download/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"
  integrity sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/caller-path/download/caller-path-0.1.0.tgz#94085ef63581ecd3daa92444a8fe94e82577751f"
  integrity sha1-lAhe9jWB7NPaqSREqP6U6CV3dR8=
  dependencies:
    callsites "^0.2.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-0.2.0.tgz#afab96262910a7f33c19a5775825c69f34e350ca"
  integrity sha1-r6uWJikQp/M8GaV3WCXGnzTjUMo=

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

chalk@^1.0.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.3.1, chalk@^2.4.1:
  version "2.4.1"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.1.tgz#18c49ab16a037b6eb0152cc83e3471338215b66e"
  integrity sha1-GMSasWoDe26wFSzIPjRxM4IVtm4=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

circular-json@^0.3.1:
  version "0.3.3"
  resolved "http://registry.npm.qima-inc.com/circular-json/download/circular-json-0.3.3.tgz#815c99ea84f6809529d2f45791bdf82711352d66"
  integrity sha1-gVyZ6oT2gJUp0vRXkb34JxE1LWY=

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://registry.npm.qima-inc.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-cursor@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-1.0.2.tgz#64da3f7d56a54412e59794bd62dc35295e8f2987"
  integrity sha1-ZNo/fValRBLll5S9Ytw1KV6PKYc=
  dependencies:
    restore-cursor "^1.0.1"

cli-cursor@^2.0.0, cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-truncate@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/cli-truncate/download/cli-truncate-0.2.1.tgz#9f15cfbb0705005369216c626ac7d05ab90dd574"
  integrity sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/cli-width/download/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"
  integrity sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.qima-inc.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

commander@^2.12.1, commander@^2.14.1, commander@^2.8.1, commander@^2.9.0:
  version "2.19.0"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-2.19.0.tgz#f6198aa84e5b83c46054b94ddedbfed5ee9ff12a"
  integrity sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=

component-emitter@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/component-emitter/download/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"
  integrity sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.4.6:
  version "1.6.2"
  resolved "http://registry.npm.qima-inc.com/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@5.0.6:
  version "5.0.6"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-5.0.6.tgz#dca6cf680a0bd03589aff684700858c81abeeb39"
  integrity sha1-3KbPaAoL0DWJr/aEcAhYyBq+6zk=
  dependencies:
    is-directory "^0.3.1"
    js-yaml "^3.9.0"
    parse-json "^4.0.0"

cosmiconfig@^5.0.7:
  version "5.0.7"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-5.0.7.tgz#39826b292ee0d78eda137dfa3173bd1c21a43b04"
  integrity sha1-OYJrKS7g147aE336MXO9HCGkOwQ=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.9.0"
    parse-json "^4.0.0"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

d@1:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/d/download/d-1.0.0.tgz#754bb5bfe55451da69a58b94d45f4c5b0462d58f"
  integrity sha1-dUu1v+VUUdpppYuU1F9MWwRi1Y8=
  dependencies:
    es5-ext "^0.10.9"

date-fns@^1.27.2:
  version "1.30.1"
  resolved "http://registry.npm.qima-inc.com/date-fns/download/date-fns-1.30.1.tgz#2e71bf0b119153dbb4cc4e88d9ea5acfb50dc05c"
  integrity sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=

debug@^2.1.1, debug@^2.2.0, debug@^2.3.3:
  version "2.6.9"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0:
  version "3.2.6"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

debug@^4.0.1:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.1.0.tgz#373687bffa678b38b1cd91f861b63850035ddc87"
  integrity sha1-NzaHv/pnizixzZH4YbY4UANd3Ic=
  dependencies:
    ms "^2.1.1"

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-is@~0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/del/download/del-3.0.0.tgz#53ecf699ffcbcb39637691ab13baf160819766e5"
  integrity sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

diff@^3.2.0:
  version "3.5.0"
  resolved "http://registry.npm.qima-inc.com/diff/download/diff-3.5.0.tgz#800c0dd1e0a8bfbc95835c202ad220fe317e5a12"
  integrity sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=

doctrine@^1.2.2:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
  integrity sha1-N53Ocw9hZvds76TmcHoVmwLFpvo=
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

elegant-spinner@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz#db043521c95d7e303fd8f345bedc3349cfb0729e"
  integrity sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=

end-of-stream@^1.1.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/end-of-stream/download/end-of-stream-1.4.1.tgz#ed29634d19baba463b6ce6b80a37213eab71ec43"
  integrity sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=
  dependencies:
    once "^1.4.0"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es5-ext@^0.10.14, es5-ext@^0.10.35, es5-ext@^0.10.9, es5-ext@~0.10.14:
  version "0.10.46"
  resolved "http://registry.npm.qima-inc.com/es5-ext/download/es5-ext-0.10.46.tgz#efd99f67c5a7ec789baa3daa7f79870388f7f572"
  integrity sha1-79mfZ8Wn7Hibqj2qf3mHA4j39XI=
  dependencies:
    es6-iterator "~2.0.3"
    es6-symbol "~3.1.1"
    next-tick "1"

es6-iterator@^2.0.1, es6-iterator@~2.0.1, es6-iterator@~2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/es6-map/download/es6-map-0.1.5.tgz#9136e0503dcc06a301690f0bb14ff4e364e949f0"
  integrity sha1-kTbgUD3MBqMBaQ8LsU/042TpSfA=
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-set@~0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/es6-set/download/es6-set-0.1.5.tgz#d2b3ec5d4d800ced818db538d28974db0a73ccb1"
  integrity sha1-0rPsXU2ADO2BjbU40ol02wpzzLE=
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@3.1.1, es6-symbol@^3.1.1, es6-symbol@~3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/es6-symbol/download/es6-symbol-3.1.1.tgz#bf00ef4fdab6ba1b46ecb7b629b4c7ed5715cc77"
  integrity sha1-vwDvT9q2uhtG7Le2KbTH7VcVzHc=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-weak-map@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/es6-weak-map/download/es6-weak-map-2.0.2.tgz#5e3ab32251ffd1538a1f8e5ffa1357772f92d96f"
  integrity sha1-XjqzIlH/0VOKH45f+hNXdy+S2W8=
  dependencies:
    d "1"
    es5-ext "^0.10.14"
    es6-iterator "^2.0.1"
    es6-symbol "^3.1.1"

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.4, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escope@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.qima-inc.com/escope/download/escope-3.6.0.tgz#e01975e812781a163a6dadfdd80398dc64c889c3"
  integrity sha1-4Bl16BJ4GhY6ba392AOY3GTIicM=
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-4.0.0.tgz#50bf3071e9338bcdc43331794a0cb533f0136172"
  integrity sha1-UL8wcekzi83EMzF5Sgy1M/ATYXI=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-1.3.1.tgz#9a851ba89ee7c460346f97cf8939c7298827e512"
  integrity sha1-moUbqJ7nxGA0b5fPiTnHKYgn5RI=

eslint-visitor-keys@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-1.0.0.tgz#3f3180fb2e291017716acb4c9d6d5b5c34a6a81d"
  integrity sha1-PzGA+y4pEBdxastMnW1bXDSmqB0=

eslint@^2.7.0:
  version "2.13.1"
  resolved "http://registry.npm.qima-inc.com/eslint/download/eslint-2.13.1.tgz#e4cc8fa0f009fb829aaae23855a29360be1f6c11"
  integrity sha1-5MyPoPAJ+4KaquI4VaKTYL4fbBE=
  dependencies:
    chalk "^1.1.3"
    concat-stream "^1.4.6"
    debug "^2.1.1"
    doctrine "^1.2.2"
    es6-map "^0.1.3"
    escope "^3.6.0"
    espree "^3.1.6"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    file-entry-cache "^1.1.1"
    glob "^7.0.3"
    globals "^9.2.0"
    ignore "^3.1.2"
    imurmurhash "^0.1.4"
    inquirer "^0.12.0"
    is-my-json-valid "^2.10.0"
    is-resolvable "^1.0.0"
    js-yaml "^3.5.1"
    json-stable-stringify "^1.0.0"
    levn "^0.3.0"
    lodash "^4.0.0"
    mkdirp "^0.5.0"
    optionator "^0.8.1"
    path-is-absolute "^1.0.0"
    path-is-inside "^1.0.1"
    pluralize "^1.2.1"
    progress "^1.1.8"
    require-uncached "^1.0.2"
    shelljs "^0.6.0"
    strip-json-comments "~1.0.1"
    table "^3.7.8"
    text-table "~0.2.0"
    user-home "^2.0.0"

eslint@^5.4.0:
  version "5.10.0"
  resolved "http://registry.npm.qima-inc.com/eslint/download/eslint-5.10.0.tgz#24adcbe92bf5eb1fc2d2f2b1eebe0c5e0713903a"
  integrity sha1-JK3L6Sv16x/C0vKx7r4MXgcTkDo=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.5.3"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^2.1.0"
    eslint-scope "^4.0.0"
    eslint-utils "^1.3.1"
    eslint-visitor-keys "^1.0.0"
    espree "^5.0.0"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^11.7.0"
    ignore "^4.0.6"
    imurmurhash "^0.1.4"
    inquirer "^6.1.0"
    js-yaml "^3.12.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.5"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    pluralize "^7.0.0"
    progress "^2.0.0"
    regexpp "^2.0.1"
    require-uncached "^1.0.3"
    semver "^5.5.1"
    strip-ansi "^4.0.0"
    strip-json-comments "^2.0.1"
    table "^5.0.2"
    text-table "^0.2.0"

espree@^3.1.6:
  version "3.5.4"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-3.5.4.tgz#b0f447187c8a8bed944b815a660bddf5deb5d1a7"
  integrity sha1-sPRHGHyKi+2US4FaZgvd9d610ac=
  dependencies:
    acorn "^5.5.0"
    acorn-jsx "^3.0.0"

espree@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-5.0.0.tgz#fc7f984b62b36a0f543b13fb9cd7b9f4a7f5b65c"
  integrity sha1-/H+YS2Kzag9UOxP7nNe59Kf1tlw=
  dependencies:
    acorn "^6.0.2"
    acorn-jsx "^5.0.0"
    eslint-visitor-keys "^1.0.0"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/esquery/download/esquery-1.0.1.tgz#406c51658b1f5991a5f9b62b1dc25b00e3e5c708"
  integrity sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg=
  dependencies:
    estraverse "^4.0.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/esrecurse/download/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  integrity sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"
  integrity sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=

esutils@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/esutils/download/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"
  integrity sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=

event-emitter@~0.3.5:
  version "0.3.5"
  resolved "http://registry.npm.qima-inc.com/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

execa@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/exit-hook/download/exit-hook-1.1.1.tgz#f05ca233b48c05d54fff07765df8507e95c02ff8"
  integrity sha1-8FyiM7SMBdVP/wd2XfhQfpXAL/g=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

external-editor@^3.0.0:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/external-editor/download/external-editor-3.0.3.tgz#5866db29a97826dbe4bf3afd24070ead9ea43a27"
  integrity sha1-WGbbKal4Jtvkvzr9JAcOrZ6kOic=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"
  integrity sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"
  integrity sha1-1RQsDK7msRifh9OnYREGT4bIu/I=

fast-levenshtein@~2.0.4:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

figures@^1.3.5, figures@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  integrity sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^1.1.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-1.3.1.tgz#44c61ea607ae4be9c1402f41f44270cbfe334ff8"
  integrity sha1-RMYepgeuS+nBQC9B9EJwy/4zT/g=
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-2.0.0.tgz#c392990c3e684783d838b8c84a45d8a048458361"
  integrity sha1-w5KZDD5oR4PYOLjISkXYoEhFg2E=
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

find-parent-dir@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/find-parent-dir/download/find-parent-dir-0.3.0.tgz#33c44b429ab2b2f0646299c5f9f718f376ff8d54"
  integrity sha1-M8RLQpqysvBkYpnF+fcY83b/jVQ=

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

flat-cache@^1.2.1:
  version "1.3.4"
  resolved "http://registry.npm.qima-inc.com/flat-cache/download/flat-cache-1.3.4.tgz#2c2ef77525cc2929007dfffa1dd314aa9c9dee6f"
  integrity sha1-LC73dSXMKSkAff/6HdMUqpyd7m8=
  dependencies:
    circular-json "^0.3.1"
    graceful-fs "^4.1.2"
    rimraf "~2.6.2"
    write "^0.2.1"

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

front-matter@2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/front-matter/download/front-matter-2.1.2.tgz#f75983b9f2f413be658c93dfd7bd8ce4078f5cdb"
  integrity sha1-91mDufL0E75ljJPf172M5AePXNs=
  dependencies:
    js-yaml "^3.4.6"

fs-extra@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/fs-extra/download/fs-extra-3.0.1.tgz#3794f378c58b342ea7dbbb23095109c4b3b62291"
  integrity sha1-N5TzeMWLNC6n27sjCVEJxLO2IpE=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^3.0.0"
    universalify "^0.1.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

g-status@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/g-status/download/g-status-2.0.2.tgz#270fd32119e8fc9496f066fe5fe88e0a6bc78b97"
  integrity sha1-Jw/TIRno/JSW8Gb+X+iOCmvHi5c=
  dependencies:
    arrify "^1.0.1"
    matcher "^1.0.0"
    simple-git "^1.85.0"

generate-function@^2.0.0:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/generate-function/download/generate-function-2.3.1.tgz#f069617690c10c868e73b8465746764f97c3479f"
  integrity sha1-8GlhdpDBDIaOc7hGV0Z2T5fDR58=
  dependencies:
    is-property "^1.0.2"

generate-object-property@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/generate-object-property/download/generate-object-property-1.2.0.tgz#9c0e1c40308ce804f4783618b937fa88f99d50d0"
  integrity sha1-nA4cQDCM6AT0eDYYuTf6iPmdUNA=
  dependencies:
    is-property "^1.0.0"

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.0.tgz#b877b49a5c16aefac3655f2ed2ea5b684df8d203"
  integrity sha1-uHe0mlwWrvrDZV8u0upbaE340gM=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

glob@^7.0.0, glob@^7.0.3, glob@^7.0.5, glob@^7.1.1, glob@^7.1.2, glob@~7.1.1:
  version "7.1.3"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.1.3.tgz#3960832d3f1574108342dafd3a67b332c0969df1"
  integrity sha1-OWCDLT8VdBCDQtr9OmezMsCWnfE=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.7.0:
  version "11.9.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-11.9.0.tgz#bde236808e987f290768a93d065060d78e6ab249"
  integrity sha1-veI2gI6YfykHaKk9BlBg145qskk=

globals@^9.2.0:
  version "9.18.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"
  integrity sha1-qjiWs+abSH8X4x7SFD1pqOMMLYo=

globby@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globule@^1.0.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/globule/download/globule-1.2.1.tgz#5dffb1b191f22d20797a9369b49eab4e9839696d"
  integrity sha1-Xf+xsZHyLSB5epNptJ6rTpg5aW0=
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.10"
    minimatch "~3.0.2"

gonzales-pe-sl@^4.2.3:
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/gonzales-pe-sl/download/gonzales-pe-sl-4.2.3.tgz#6a868bc380645f141feeb042c6f97fcc71b59fe6"
  integrity sha1-aoaLw4BkXxQf7rBCxvl/zHG1n+Y=
  dependencies:
    minimist "1.1.x"

graceful-fs@^4.1.2, graceful-fs@^4.1.6:
  version "4.1.15"
  resolved "http://registry.npm.qima-inc.com/graceful-fs/download/graceful-fs-4.1.15.tgz#ffb703e1066e8a0eeaa4c8b80ba9253eeefbfb00"
  integrity sha1-/7cD4QZuig7qpMi4C6klPu77+wA=

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

hosted-git-info@^2.1.4:
  version "2.7.1"
  resolved "http://registry.npm.qima-inc.com/hosted-git-info/download/hosted-git-info-2.7.1.tgz#97f236977bd6e125408930ff6de3eec6281ec047"
  integrity sha1-l/I2l3vW4SVAiTD/bePuxigewEc=

husky@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/husky/download/husky-1.3.0.tgz#fbb97b5e52739d945fd86c3bf92def0ea60175c4"
  integrity sha1-+7l7XlJznZRf2Gw7+S3vDqYBdcQ=
  dependencies:
    cosmiconfig "^5.0.7"
    execa "^1.0.0"
    find-up "^3.0.0"
    get-stdin "^6.0.0"
    is-ci "^1.2.1"
    pkg-dir "^3.0.0"
    please-upgrade-node "^3.1.1"
    read-pkg "^4.0.1"
    run-node "^1.0.0"
    slash "^2.0.0"

iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ignore@^3.1.2:
  version "3.3.10"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-3.3.10.tgz#0a97fb876986e8081c631160f8f9f389157f0043"
  integrity sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.3, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.qima-inc.com/inquirer/download/inquirer-0.12.0.tgz#1ef2bfd63504df0bc75785fff8c2c41df12f077e"
  integrity sha1-HvK/1jUE3wvHV4X/+MLEHfEvB34=
  dependencies:
    ansi-escapes "^1.1.0"
    ansi-regex "^2.0.0"
    chalk "^1.0.0"
    cli-cursor "^1.0.1"
    cli-width "^2.0.0"
    figures "^1.3.5"
    lodash "^4.3.0"
    readline2 "^1.0.1"
    run-async "^0.1.0"
    rx-lite "^3.1.2"
    string-width "^1.0.1"
    strip-ansi "^3.0.0"
    through "^2.3.6"

inquirer@^6.1.0:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/inquirer/download/inquirer-6.2.1.tgz#9943fc4882161bdb0b0c9276769c75b32dbfcd52"
  integrity sha1-mUP8SIIWG9sLDJJ2dpx1sy2/zVI=
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.0"
    figures "^2.0.0"
    lodash "^4.17.10"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.1.0"
    string-width "^2.1.0"
    strip-ansi "^5.0.0"
    through "^2.3.6"

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-builtin-module/download/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  integrity sha1-VAVy0096wxGfj3bDDLwbHgN6/74=
  dependencies:
    builtin-modules "^1.0.0"

is-ci@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-glob@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-4.0.0.tgz#9521c76845cc2610a85203ddf080a958c2ffabc0"
  integrity sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A=
  dependencies:
    is-extglob "^2.1.1"

is-my-ip-valid@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-my-ip-valid/download/is-my-ip-valid-1.0.0.tgz#7b351b8e8edd4d3995d4d066680e664d94696824"
  integrity sha1-ezUbjo7dTTmV1NBmaA5mTZRpaCQ=

is-my-json-valid@^2.10.0:
  version "2.19.0"
  resolved "http://registry.npm.qima-inc.com/is-my-json-valid/download/is-my-json-valid-2.19.0.tgz#8fd6e40363cd06b963fa877d444bfb5eddc62175"
  integrity sha1-j9bkA2PNBrlj+od9REv7Xt3GIXU=
  dependencies:
    generate-function "^2.0.0"
    generate-object-property "^1.1.0"
    is-my-ip-valid "^1.0.0"
    jsonpointer "^4.0.0"
    xtend "^4.0.0"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-observable@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-observable/download/is-observable-1.1.0.tgz#b3e986c8f44de950867cab5403f5a3465005975e"
  integrity sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=
  dependencies:
    symbol-observable "^1.1.0"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"
  integrity sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz#5ac48b345ef675339bd6c7a48a912110b241cf52"
  integrity sha1-WsSLNF72dTOb1sekipEhELJBz1I=
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-inside/download/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-plain-object@^2.0.1, is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-promise@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-promise/download/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
  integrity sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=

is-property@^1.0.0, is-property@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"
  integrity sha1-V/4cTkhHTt1lsJkR8msc1Ald2oQ=

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-resolvable/download/is-resolvable-1.1.0.tgz#fb18f87ce1feb925169c9a407c19318a3206ed88"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

jest-get-type@^22.1.0:
  version "22.4.3"
  resolved "http://registry.npm.qima-inc.com/jest-get-type/download/jest-get-type-22.4.3.tgz#e3a8504d8479342dd4420236b322869f18900ce4"
  integrity sha1-46hQTYR5NC3UQgI2syKGnxiQDOQ=

jest-validate@^23.5.0:
  version "23.6.0"
  resolved "http://registry.npm.qima-inc.com/jest-validate/download/jest-validate-23.6.0.tgz#36761f99d1ed33fcd425b4e4c5595d62b6597474"
  integrity sha1-NnYfmdHtM/zUJbTkxVldYrZZdHQ=
  dependencies:
    chalk "^2.0.1"
    jest-get-type "^22.1.0"
    leven "^2.1.0"
    pretty-format "^23.6.0"

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"
  integrity sha1-mGbfOVECEw449/mWvOtlRDIJwls=

js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.12.0, js-yaml@^3.4.6, js-yaml@^3.5.1, js-yaml@^3.5.4, js-yaml@^3.7.0, js-yaml@^3.9.0:
  version "3.12.0"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-3.12.0.tgz#eaed656ec8344f10f527c6bfa1b6e2244de167d1"
  integrity sha1-6u1lbsg0TxD1J8a/obbiJE3hZ9E=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

json-parse-better-errors@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stable-stringify@^1.0.0, json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  integrity sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=
  dependencies:
    jsonify "~0.0.0"

jsonfile@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/jsonfile/download/jsonfile-3.0.1.tgz#a5ecc6f65f53f662c4415c7675a0331d0992ec66"
  integrity sha1-pezG9l9T9mLEQVx2daAzHQmS7GY=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.qima-inc.com/jsonify/download/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"
  integrity sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=

jsonpointer@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/jsonpointer/download/jsonpointer-4.0.1.tgz#4fd92cb34e0e9db3c89c8622ecf51f9b978c6cb9"
  integrity sha1-T9kss04OnbPInIYi7PUfm5eMbLk=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-6.0.2.tgz#01146b36a6218e64e58f3a8d66de5d7fc6f6d051"
  integrity sha1-ARRrNqYhjmTljzqNZt5df8b20FE=

known-css-properties@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/known-css-properties/download/known-css-properties-0.3.0.tgz#a3d135bbfc60ee8c6eacf2f7e7e6f2d4755e49a4"
  integrity sha1-o9E1u/xg7oxurPL35+by1HVeSaQ=

leven@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/leven/download/leven-2.1.0.tgz#c2e7a9f772094dee9d34202ae8acce4687875580"
  integrity sha1-wuep93IJTe6dNCAq6KzORoeHVYA=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lint-staged@^8.1.0:
  version "8.1.0"
  resolved "http://registry.npm.qima-inc.com/lint-staged/download/lint-staged-8.1.0.tgz#dbc3ae2565366d8f20efb9f9799d076da64863f2"
  integrity sha1-28OuJWU2bY8g77n5eZ0HbaZIY/I=
  dependencies:
    "@iamstarkov/listr-update-renderer" "0.4.1"
    chalk "^2.3.1"
    commander "^2.14.1"
    cosmiconfig "5.0.6"
    debug "^3.1.0"
    dedent "^0.7.0"
    del "^3.0.0"
    execa "^1.0.0"
    find-parent-dir "^0.3.0"
    g-status "^2.0.2"
    is-glob "^4.0.0"
    is-windows "^1.0.2"
    jest-validate "^23.5.0"
    listr "^0.14.2"
    lodash "^4.17.5"
    log-symbols "^2.2.0"
    micromatch "^3.1.8"
    npm-which "^3.0.1"
    p-map "^1.1.1"
    path-is-inside "^1.0.2"
    pify "^3.0.0"
    please-upgrade-node "^3.0.2"
    staged-git-files "1.1.2"
    string-argv "^0.0.2"
    stringify-object "^3.2.2"

listr-silent-renderer@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz#924b5a3757153770bf1a8e3fbf74b8bbf3f9242e"
  integrity sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=

listr-update-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz#4ea8368548a7b8aecb7e06d8c95cb45ae2ede6a2"
  integrity sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^2.3.0"
    strip-ansi "^3.0.1"

listr-verbose-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz#f1132167535ea4c1261102b9f28dac7cba1e03db"
  integrity sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=
  dependencies:
    chalk "^2.4.1"
    cli-cursor "^2.1.0"
    date-fns "^1.27.2"
    figures "^2.0.0"

listr@^0.14.2:
  version "0.14.3"
  resolved "http://registry.npm.qima-inc.com/listr/download/listr-0.14.3.tgz#2fea909604e434be464c50bddba0d496928fa586"
  integrity sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    is-observable "^1.1.0"
    is-promise "^2.1.0"
    is-stream "^1.1.0"
    listr-silent-renderer "^1.1.1"
    listr-update-renderer "^0.5.0"
    listr-verbose-renderer "^0.5.0"
    p-map "^2.0.0"
    rxjs "^6.3.3"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

lodash.capitalize@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/lodash.capitalize/download/lodash.capitalize-4.2.1.tgz#f826c9b4e2a8511d84e3aca29db05e1a4f3b72a9"
  integrity sha1-+CbJtOKoUR2E46yinbBeGk87cqk=

lodash.kebabcase@^4.0.0:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz#8489b1cb0d29ff88195cceca448ff6d6cc295c36"
  integrity sha1-hImxyw0p/4gZXM7KRI/21swpXDY=

lodash@^4.0.0, lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.5, lodash@^4.3.0, lodash@~4.17.10:
  version "4.17.11"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.11.tgz#b39ea6229ef607ecd89e2c8df12536891cac9b8d"
  integrity sha1-s56mIp72B+zYniyN8SU2iRysm40=

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-1.0.2.tgz#376ff7b58ea3086a0f09facc74617eca501e1a18"
  integrity sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=
  dependencies:
    chalk "^1.0.0"

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-update@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/log-update/download/log-update-2.3.0.tgz#88328fd7d1ce7938b29283746f0b1bc126b24708"
  integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

matcher@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/matcher/download/matcher-1.1.1.tgz#51d8301e138f840982b338b116bb0c09af62c1c2"
  integrity sha1-UdgwHhOPhAmCszixFrsMCa9iwcI=
  dependencies:
    escape-string-regexp "^1.0.4"

merge@^1.2.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/merge/download/merge-1.2.1.tgz#38bebf80c3220a8a487b6fcfb3941bb11720c145"
  integrity sha1-OL6/gMMiCopIe2/Ps5QbsRcgwUU=

micromatch@^3.1.8:
  version "3.1.10"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
  integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=

minimist@1.1.x:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.1.3.tgz#3bedfd91a92d39016fcfaa1c681e8faa1a1efda8"
  integrity sha1-O+39kaktOQFvz6ocaB6Pqhoe/ag=

mixin-deep@^1.2.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/mixin-deep/download/mixin-deep-1.3.1.tgz#a49e7268dce1a0d9698e45326c5626df3543d0fe"
  integrity sha1-pJ5yaNzhoNlpjkUybFYm3zVD0P4=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@^0.5.0, mkdirp@^0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
  dependencies:
    minimist "0.0.8"

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

mute-stream@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.qima-inc.com/mute-stream/download/mute-stream-0.0.5.tgz#8fbfabb0a98a253d3184331f9e8deb7372fac6c0"
  integrity sha1-j7+rsKmKJT0xhDMfno3rc3L6xsA=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://registry.npm.qima-inc.com/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

next-tick@1:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/next-tick/download/next-tick-1.0.0.tgz#ca86d1fe8828169b0120208e3dc8424b9db8342c"
  integrity sha1-yobR/ogoFpsBICCOPchCS524NCw=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

normalize-package-data@^2.3.2:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/normalize-package-data/download/normalize-package-data-2.4.0.tgz#12f95a307d58352075a04907b84ac8be98ac012f"
  integrity sha1-EvlaMH1YNSB1oEkHuErIvpisAS8=
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

npm-path@^2.0.2:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/npm-path/download/npm-path-2.0.4.tgz#c641347a5ff9d6a09e4d9bce5580c4f505278e64"
  integrity sha1-xkE0el/51qCeTZvOVYDE9QUnjmQ=
  dependencies:
    which "^1.2.10"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-which@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/npm-which/download/npm-which-3.0.1.tgz#9225f26ec3a285c209cae67c3b11a6b4ab7140aa"
  integrity sha1-kiXybsOihcIJyuZ8OxGmtKtxQKo=
  dependencies:
    commander "^2.9.0"
    npm-path "^2.0.2"
    which "^1.2.10"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-1.1.0.tgz#a1f7838f8314c516f05ecefcbc4ccfe04b4ed789"
  integrity sha1-ofeDj4MUxRbwXs78vEzP4EtO14k=

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

optionator@^0.8.1, optionator@^0.8.2:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/optionator/download/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  integrity sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

os-homedir@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"
  integrity sha1-/7xJiDNuDoM94MFox+8VISGqf7M=

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-limit@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-2.0.0.tgz#e624ed54ee8c460a778b3c9f3670496ff8a57aec"
  integrity sha1-5iTtVO6MRgp3izyfNnBJb/ileuw=
  dependencies:
    p-try "^2.0.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-1.2.0.tgz#e4e94f311eabbc8633a1e79908165fca26241b6b"
  integrity sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=

p-map@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-2.0.0.tgz#be18c5a5adeb8e156460651421aceca56c213a50"
  integrity sha1-vhjFpa3rjhVkYGUUIazspWwhOlA=

p-try@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-2.0.0.tgz#85080bb87c64688fa47996fe8f7dfbe8211760b1"
  integrity sha1-hQgLuHxkaI+keZb+j3376CEXYLE=

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

pify@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

please-upgrade-node@^3.0.2, please-upgrade-node@^3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/please-upgrade-node/download/please-upgrade-node-3.1.1.tgz#ed320051dfcc5024fae696712c8288993595e8ac"
  integrity sha1-7TIAUd/MUCT65pZxLIKImTWV6Kw=
  dependencies:
    semver-compare "^1.0.0"

pluralize@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/pluralize/download/pluralize-1.2.1.tgz#d1a21483fd22bb41e58a12fa3421823140897c45"
  integrity sha1-0aIUg/0iu0HlihL6NCGCMUCJfEU=

pluralize@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/pluralize/download/pluralize-7.0.0.tgz#298b89df8b93b0221dbf421ad2b1b1ea23fc6777"
  integrity sha1-KYuJ34uTsCIdv0Ia0rGx6iP8Z3c=

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

pretty-format@^23.6.0:
  version "23.6.0"
  resolved "http://registry.npm.qima-inc.com/pretty-format/download/pretty-format-23.6.0.tgz#5eaac8eeb6b33b987b7fe6097ea6a8a146ab5760"
  integrity sha1-XqrI7razO5h7f+YJfqaooUarV2A=
  dependencies:
    ansi-regex "^3.0.0"
    ansi-styles "^3.2.0"

process-nextick-args@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/process-nextick-args/download/process-nextick-args-2.0.0.tgz#a37d732f4271b4ab1ad070d35508e8290788ffaa"
  integrity sha1-o31zL0JxtKsa0HDTVQjoKQeI/6o=

progress@^1.1.8:
  version "1.1.8"
  resolved "http://registry.npm.qima-inc.com/progress/download/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"
  integrity sha1-4mDHj2Fhzdmw5WzD4Khd4Xx6V74=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

pump@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

read-pkg@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-4.0.1.tgz#963625378f3e1c4d48c85872b5a6ec7d5d093237"
  integrity sha1-ljYlN48+HE1IyFhytabsfV0JMjc=
  dependencies:
    normalize-package-data "^2.3.2"
    parse-json "^4.0.0"
    pify "^3.0.0"

readable-stream@^2.2.2:
  version "2.3.6"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
  integrity sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readline2@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/readline2/download/readline2-1.0.1.tgz#41059608ffc154757b715d9989d199ffbf372e35"
  integrity sha1-QQWWCP/BVHV7cV2ZidGZ/783LjU=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    mute-stream "0.0.5"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexpp@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

require-uncached@^1.0.2, require-uncached@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/require-uncached/download/require-uncached-1.0.3.tgz#4e0d56d6c9662fd31e43011c4b95aa49955421d3"
  integrity sha1-Tg1W1slmL9MeQwEcS5WqSZVUIdM=
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-1.0.1.tgz#26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226"
  integrity sha1-Jsv+k10a7uq7Kbw/5a6wHpPUQiY=

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@^1.3.2:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.9.0.tgz#a14c6fdfa8f92a7df1d996cb7105fa744658ea06"
  integrity sha1-oUxv36j5Kn3x2ZbLcQX6dEZY6gY=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-1.0.1.tgz#34661f46886327fed2991479152252df92daa541"
  integrity sha1-NGYfRohjJ/7SmRR5FSJS35LapUE=
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://registry.npm.qima-inc.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

rimraf@^2.2.8, rimraf@~2.6.2:
  version "2.6.2"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.6.2.tgz#2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36"
  integrity sha1-LtgVDSShbqhlHm1u8PR8QVjOejY=
  dependencies:
    glob "^7.0.5"

run-async@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/run-async/download/run-async-0.1.0.tgz#c8ad4a5e110661e402a7d21b530e009f25f8e389"
  integrity sha1-yK1KXhEGYeQCp9IbUw4AnyX444k=
  dependencies:
    once "^1.3.0"

run-async@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/run-async/download/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
  integrity sha1-A3GrSuC91yDUFm19/aZP96RFpsA=
  dependencies:
    is-promise "^2.1.0"

run-node@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/run-node/download/run-node-1.0.0.tgz#46b50b946a2aa2d4947ae1d886e9856fd9cabe5e"
  integrity sha1-RrULlGoqotSUeuHYhumFb9nKvl4=

rx-lite@^3.1.2:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/rx-lite/download/rx-lite-3.1.2.tgz#19ce502ca572665f3b647b10939f97fd1615f102"
  integrity sha1-Gc5QLKVyZl87ZHsQk5+X/RYV8QI=

rxjs@^6.1.0, rxjs@^6.3.3:
  version "6.3.3"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-6.3.3.tgz#3c6a7fa420e844a81390fb1158a9ec614f4bad55"
  integrity sha1-PGp/pCDoRKgTkPsRWKnsYU9LrVU=
  dependencies:
    tslib "^1.9.0"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-lint@^1.12.1:
  version "1.12.1"
  resolved "http://registry.npm.qima-inc.com/sass-lint/download/sass-lint-1.12.1.tgz#630f69c216aa206b8232fb2aa907bdf3336b6d83"
  integrity sha1-Yw9pwhaqIGuCMvsqqQe98zNrbYM=
  dependencies:
    commander "^2.8.1"
    eslint "^2.7.0"
    front-matter "2.1.2"
    fs-extra "^3.0.1"
    glob "^7.0.0"
    globule "^1.0.0"
    gonzales-pe-sl "^4.2.3"
    js-yaml "^3.5.4"
    known-css-properties "^0.3.0"
    lodash.capitalize "^4.1.0"
    lodash.kebabcase "^4.0.0"
    merge "^1.2.0"
    path-is-absolute "^1.0.0"
    util "^0.10.3"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

"semver@2 || 3 || 4 || 5", semver@^5.3.0, semver@^5.5.0, semver@^5.5.1:
  version "5.6.0"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-5.6.0.tgz#7e74256fbaa49c75aa7c7a205cc22799cac80004"
  integrity sha1-fnQlb7qknHWqfHogXMInmcrIAAQ=

set-value@^0.4.3:
  version "0.4.3"
  resolved "http://registry.npm.qima-inc.com/set-value/download/set-value-0.4.3.tgz#7db08f9d3d22dc7f78e53af3c3bf4666ecdfccf1"
  integrity sha1-fbCPnT0i3H945Trzw79GZuzfzPE=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.1"
    to-object-path "^0.3.0"

set-value@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/set-value/download/set-value-2.0.0.tgz#71ae4a88f0feefbbf52d1ea604f3fb315ebb6274"
  integrity sha1-ca5KiPD+77v1LR6mBPP7MV67YnQ=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shelljs@^0.6.0:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/shelljs/download/shelljs-0.6.1.tgz#ec6211bed1920442088fe0f70b2837232ed2c8a8"
  integrity sha1-7GIRvtGSBEIIj+D3Cyg3Iy7SyKg=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"
  integrity sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=

simple-git@^1.85.0:
  version "1.107.0"
  resolved "http://registry.npm.qima-inc.com/simple-git/download/simple-git-1.107.0.tgz#12cffaf261c14d6f450f7fdb86c21ccee968b383"
  integrity sha1-Es/68mHBTW9FD3/bhsIczulos4M=
  dependencies:
    debug "^4.0.1"

slash@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"
  integrity sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=

slice-ansi@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-2.0.0.tgz#5373bdb8559b45676e8541c66916cdd6251612e7"
  integrity sha1-U3O9uFWbRWduhUHGaRbN1iUWEuc=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

source-map-resolve@^0.5.0:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/source-map-resolve/download/source-map-resolve-0.5.2.tgz#72e2cc34095543e43b2c62b2c4c10d4a9054f259"
  integrity sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=
  dependencies:
    atob "^2.1.1"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.6:
  version "0.5.7"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

spdx-correct@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/spdx-correct/download/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
  integrity sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"
  integrity sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  integrity sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/spdx-license-ids/download/spdx-license-ids-3.0.2.tgz#a59efc09784c2a5bada13cfeaf5c75dd214044d2"
  integrity sha1-pZ78CXhMKlutoTz+r1x13SFARNI=

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

staged-git-files@1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/staged-git-files/download/staged-git-files-1.1.2.tgz#4326d33886dc9ecfa29a6193bf511ba90a46454b"
  integrity sha1-QybTOIbcns+immGTv1EbqQpGRUs=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

string-argv@^0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/string-argv/download/string-argv-0.0.2.tgz#dac30408690c21f3c3630a3ff3a05877bdcbd736"
  integrity sha1-2sMECGkMIfPDYwo/86BYd73L1zY=

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0, string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.2.2:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-5.0.0.tgz#f78f68b5d0866c20b2c9b8c61b5298508dc8756f"
  integrity sha1-949otdCGbCCyybjGG1KYUI3IdW8=
  dependencies:
    ansi-regex "^4.0.0"

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-json-comments@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"
  integrity sha1-PFMZQukIwml8DsNEhYwobHygpgo=

strip-json-comments@~1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-1.0.4.tgz#1e15fbcac97d3ee99bf2d73b4c656b082bbafb91"
  integrity sha1-HhX7ysl9Pumb8tc7TGVrCCu6+5E=

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

symbol-observable@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/symbol-observable/download/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

table@^3.7.8:
  version "3.8.3"
  resolved "http://registry.npm.qima-inc.com/table/download/table-3.8.3.tgz#2bbc542f0fda9861a755d3947fefd8b3f513855f"
  integrity sha1-K7xULw/amGGnVdOUf+/Ys/UThV8=
  dependencies:
    ajv "^4.7.0"
    ajv-keywords "^1.0.0"
    chalk "^1.1.1"
    lodash "^4.0.0"
    slice-ansi "0.0.4"
    string-width "^2.0.0"

table@^5.0.2:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/table/download/table-5.1.1.tgz#92030192f1b7b51b6eeab23ed416862e47b70837"
  integrity sha1-kgMBkvG3tRtu6rI+1BaGLke3CDc=
  dependencies:
    ajv "^6.6.1"
    lodash "^4.17.11"
    slice-ansi "2.0.0"
    string-width "^2.1.1"

text-table@^0.2.0, text-table@~0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

through@^2.3.6:
  version "2.3.8"
  resolved "http://registry.npm.qima-inc.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://registry.npm.qima-inc.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

tslib@^1.8.0, tslib@^1.8.1, tslib@^1.9.0:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.9.3.tgz#d7e4dd79245d85428c4d7e4822a79917954ca286"
  integrity sha1-1+TdeSRdhUKMTX5IIqeZF5VMooY=

tslint@^5.11.0:
  version "5.12.0"
  resolved "http://registry.npm.qima-inc.com/tslint/download/tslint-5.12.0.tgz#47f2dba291ed3d580752d109866fb640768fca36"
  integrity sha1-R/LbopHtPVgHUtEJhm+2QHaPyjY=
  dependencies:
    babel-code-frame "^6.22.0"
    builtin-modules "^1.1.1"
    chalk "^2.3.0"
    commander "^2.12.1"
    diff "^3.2.0"
    glob "^7.1.1"
    js-yaml "^3.7.0"
    minimatch "^3.0.4"
    resolve "^1.3.2"
    semver "^5.3.0"
    tslib "^1.8.0"
    tsutils "^2.27.2"

tsutils@^2.27.2:
  version "2.29.0"
  resolved "http://registry.npm.qima-inc.com/tsutils/download/tsutils-2.29.0.tgz#32b488501467acbedd4b85498673a0812aca0b99"
  integrity sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k=
  dependencies:
    tslib "^1.8.1"

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

union-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/union-value/download/union-value-1.0.0.tgz#5c71c34cb5bad5dcebe3ea0cd08207ba5aa1aea4"
  integrity sha1-XHHDTLW61dzr4+oM0IIHulqhrqQ=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^0.4.3"

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  integrity sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

use@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

user-home@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/user-home/download/user-home-2.0.0.tgz#9c70bfd8169bc1dcbf48604e0f04b8b49cde9e9f"
  integrity sha1-nHC/2Babwdy/SGBODwS4tJzenp8=
  dependencies:
    os-homedir "^1.0.0"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.10.3:
  version "0.10.4"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.10.4.tgz#3aa0125bfe668a4672de58857d3ace27ecb76901"
  integrity sha1-OqASW/5mikZy3liFfTrOJ+y3aQE=
  dependencies:
    inherits "2.0.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

which@^1.2.10, which@^1.2.9:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

wordwrap@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/wordwrap/download/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz#288a04d87eda5c286e060dfe8f135ce8d007f8ba"
  integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/write/download/write-0.2.1.tgz#5fc03828e264cea3fe91455476f7a3c566cb0757"
  integrity sha1-X8A4KOJkzqP+kUVUdvejxWbLB1c=
  dependencies:
    mkdirp "^0.5.1"

xtend@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/xtend/download/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"
  integrity sha1-pcbVMr5lbiPbgg77lDofBJmNY68=
