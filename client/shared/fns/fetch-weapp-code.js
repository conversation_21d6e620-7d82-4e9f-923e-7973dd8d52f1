/**
 * 此文件已废弃，后面不再维护此文件。
 * 内置的方法已经移至react-components，如需使用请参考http://fedoc.qima-inc.com/react-components/#/fetch-weapp-code
 * @deprecated
 * @date 2019/02/27
 */

import * as FWC from '@youzan/react-components/es/components/fetch-weapp-code';
const FETCH_WEAPP_CODE = FWC.default;
const { getWeappType: gwt } = FETCH_WEAPP_CODE;

export const fetchWeappCode = FETCH_WEAPP_CODE;
export const getWeappType = gwt;

/**
 * 获取专享版小程序推广码
 * @param {Object} params: 参数
 * @return Promise
 *
 * @example:
 *  const params = { pagepath: `${pagePath}?alias=${alias}` }
 *  getWeappCode(params).then(res => {})
 */
export function getWeappCode(params) {
  return makeRequest('/v4/shop/api/weappCode', params);
}

/**
 * 无限获取小程序二维码
 * @param {String | Number} id: 店铺id
 * @param {Object} params: 推广码需要携带的参数
 * @param {Boolean} useCommon: 是否为公共版小程序
 * @return Promise
 *
 * @example:
 *  const kdtId = 55;
 *	const params = {
 *     page: 'pages/home/<USER>/index' // 启动路径
 *     alias: pageAlias, // 其他参数
 *  }
 *  const useCommon = true;
 *  const hyaLine = false;
 *  getWeappCodeUltra(kdtId, config, useCommon, hyaLine).then(res => {})
 *
 * 文档: https://doc.qima-inc.com/pages/viewpage.action?pageId=47037959
 */
export function getWeappCodeUltra(id, config, useCommon, hyaLine = false) {
  // 后端泛型丢失，kdtId需要传 string 类型
  id = `${id}`;
  const kdtId = useCommon ? '40419900' : id;

  // 默认请求参数
  const defaultData = {
    isShare: +useCommon, // 0 专享版小程序，1 公共版小程序
    hyaLine, // 是否为透明底色
    kdtId, // 店铺ID, 公共版写死为40419900
    page: 'pages/common/blank-page/index', // 落地页，写死
    params: {
      kdtId, // 店铺ID，必须与外部 kdtId 一致
      guestKdtId: id, // 真正的 kdtId
      // page: '',   // 跳转的小程序路径
    },
  };

  // 实际请求参数，整合config参数, 具体见https://doc.qima-inc.com/pages/viewpage.action?pageId=47037959
  const data = assign({}, defaultData, {
    params: assign({}, config, defaultData.params),
  });

  return makeRequest('/v4/shop/api/weappCodeUltra', { params: JSON.stringify(data) });
}

/**
 * 获取获取店铺小程序使用情况
 * @return Promise 小程序使用情况
 *
 * 文档: http://zanapi.qima-inc.com/site/service/view/204422
 */
export function getHasOrderWeapp() {
  return makeRequest('/v4/shop/weapp/order');
}
