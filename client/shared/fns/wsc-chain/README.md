# 连锁中使用的通用判断逻辑

# base.tsx 通用数据结构的判断方法

## 数组的项展示与否 arrayWrapper

### Example

```
const originList = ['dog', 'cat', 'people'];
const formatList = arrayWrapper({
 '0': false,
}, originList); // -> ['cat', 'people']
```

## 数组的项展示与否 arrayWrapperHOF 高阶方法

### Example

```
const originList = ['dog', 'cat', 'people'];
const formatFirstList = arrayWrapperHOF({
 '0': false,
});
const formatList = formatFirstList(originList); // -> ['cat', 'people']
```

## 数组的项展示与否 arrayColumnWrapper

对原数组产生侵入性，但是更直观，适用对象数组

### Example

```
const originList = [
 {
   a: 123,
 },
 {
   a: 345,
   chainState: false, // 这项需要判断显示与否，true 为显示
 }
]
const formatList = arrayColumnWrapper(originList); // -> [{a: 123}]
```

## 对象的项展示与否 objectWrapper

### Example

```
const originObj = {
 a: 1,
 b: 2,
 c: 3
}
const formatObj = objectWrapper({
 a: false
}, originObj); // -> {b: 2, c: 3}
```

## 对象的项展示与否 objectWrapperHOF 高阶方法
