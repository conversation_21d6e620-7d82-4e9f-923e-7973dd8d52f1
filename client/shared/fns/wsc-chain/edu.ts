import { ReactElement } from 'react';
import {
  isEduBranchStore,
  isEduHqStore,
  isEduSingleStore,
  isEduChainStore,
} from '@youzan/utils-shop';
import { getComponents, IProps } from './base';

/**
 * @description 教育单店展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function EduSingleStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isEduSingleStore);
}

/**
 * @description 教育连锁总部 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function EduHqStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isEduHqStore);
}

/**
 * @description 教育连锁分部展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function EduBranchStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isEduBranchStore);
}

/**
 * @description 教育连锁店铺展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function EduChainStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isEduChainStore);
}
