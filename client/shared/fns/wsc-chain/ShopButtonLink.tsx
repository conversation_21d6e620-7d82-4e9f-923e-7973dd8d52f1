import React, { Component, ReactElement } from 'react';
import { Button } from 'zent';
import omit from 'lodash/omit';

interface IPropsShopButtonLink {
  disabled: boolean;
  buttonType: string;
  appearance: string;
  notAllow: boolean;
  children: ReactElement;
  [key: string]: any;
}

interface IStyle {
  color: string;
  cursor: string;
}

/**
 * @description: 包装按钮/链接，将隐藏、禁用等逻辑跟业务抽离
 * @class ShopButtonLink
 */
export class ShopButtonLink extends Component<IPropsShopButtonLink> {
  static defaultProps = {
    appearance: 'disable', // 区分展示跟隐藏, ''
    buttonType: 'button', // 类型，'button':按钮; 'link':链接
    disabled: false, // 判断当前是否需要隐藏
    notAllow: true, // 区分按钮鼠标手
  };

  render() {
    const { disabled, buttonType, appearance, children, notAllow} = this.props;
    let restProps = omit(this.props, ['disabled', 'buttonType', 'appearance', 'children', 'notAllow']);
    // 判断是否需要隐藏该按钮
    if (disabled && appearance === 'hide') return null;

    // 判断是否为按钮
    const isButton = buttonType === 'button';
    if (isButton) {
      return (
        <Button disabled={disabled} {...restProps}>
          {children}
        </Button>
      );
    }

    const style: Partial<IStyle> = {};
    if (disabled) {
      restProps = omit(restProps, ['href', 'onClick']);
      // 链接文案颜色处理，写在此处是不想多写一个样式文件，后面样式多了可以抽离出去
      style.color = '#999';
      // 判断是否鼠标属否需要处理
      style.cursor = notAllow ? 'not-allowed' : 'text';
    }
    return <a style={style} {...restProps} >{children}</a>;
  }
}
