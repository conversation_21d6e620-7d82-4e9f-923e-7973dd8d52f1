import { ReactElement } from 'react';
import _omit from 'lodash/omit';
import _pickBy from 'lodash/pickBy';
import _keys from 'lodash/keys';
import _remove from 'lodash/remove';
import {
  isEduBranchStore,
  isEduHqStore,
  isEduSingleStore,
  isEduChainStore,
  isWscBranchStore,
  isWscChainStore,
  isWscHqStore,
  isWscSingleStore,
} from '@youzan/utils-shop';

export interface IProps {
  negation: boolean;
  children: ReactElement;
  [key: string]: any;
}

interface IShopWrapper {
  eduSingle: ReactElement;
  eduHq: ReactElement;
  eduBranch: ReactElement;
  wscSingle: ReactElement;
  wscChain: ReactElement;
  wscHq: ReactElement;
  wscBranch: ReactElement;
  defaultCpn: ReactElement;
  [key: string]: any;
}

/**
 * @description 生成新的组件，(可以根据condition自定义壳子)
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @param {boolean} condition: 组件是否展示的条件
 */
export function getComponents(props: IProps, condition: boolean) {
  const { negation = false, children } = props;
  const newCondition = negation ? !condition : condition;
  return newCondition ? children : null;
}

/**
 * @description: 根据不同类型店铺返回不同的组件
 * @param {Object} props -
 * @param {ReactElement} props.eduSingle - 教育单店展示的内容
 * @param {ReactElement} props.eduHq - 教育连锁总部展示的内容
 * @param {ReactElement} props.eduBranch - 教育连锁分部展示的内容
 * @param {ReactElement} props.eduChain - 教育连锁店铺展示的内容
 * @param {ReactElement} props.wscSingle - 微商城单店（含教育）展示的内容
 * @param {ReactElement} props.wscChain - 微商城连锁店铺（含教育）展示的内容
 * @param {ReactElement} props.wscHq - 微商城连锁总部（含教育）展示的内容
 * @param {ReactElement} props.wscBranch - 微商城连锁分部（含教育）展示的内容
 * @param {ReactElement} props.defaultCpn - 默认展示的内容
 * @return {ReactElement | null}
 * @example
 *   <ShopWrapper
 *     eduSingle={<Comp1 />}
 *     eduHq={<Comp2 />}
 *     eduBranch={<Comp3 />}
 *     ...
 *   />
 */
export function ShopWrapper(props: Partial<IShopWrapper>) {
  const { wscSingle, wscChain, wscHq, wscBranch, eduSingle, eduHq, eduBranch, eduChain, defaultCpn } = props;
  if (isEduSingleStore && eduSingle) return eduSingle;
  if (isEduHqStore && eduHq) return eduHq;
  if (isEduBranchStore && eduBranch) return eduBranch;
  if (isEduChainStore && eduChain) return eduChain;
  if (isWscSingleStore && wscSingle) return wscSingle;
  if (isWscChainStore && wscChain) return wscChain;
  if (isWscHqStore && wscHq) return wscHq;
  if (isWscBranchStore && wscBranch) return wscBranch;
  return defaultCpn || null;
}

export interface IArrayColumnParam {
  chainState?: boolean;
}
/**
 * @description 针对对象数组的包装方法
 * @param {Array} array 包含所有情况的数组
 * @return {Array} 对应店铺的数组数据
 * @example
 * const originList = [
 *  {
  *   a: 123,
  * },
  * {
  *   a: 345,
  *   chainState: false, // 这项需要判断显示与否，true 为显示
  * }
 * ]
 * const formatList = arrayColumnWrapper(originList); // -> [{a: 123}]
 */
export function arrayColumnWrapper<T extends IArrayColumnParam>(array: T[]) {
  return array.filter(item => {
    const chainState = item.chainState;
    return (typeof chainState === 'boolean' && chainState) || typeof chainState !== 'boolean';
  }).map(item => {
    return _omit(item, 'chainState');
  });
}

/**
 * @description 针对普通数组的包装
 * @param {Object} props -
 * {
 *  [index]: isInStoreCondition
 * }
 * @param {Boolean} props[index] index 为数组的序号，true 显示
 * @param {Array} array 需要处理的数组
 * @return {Array}
 * @example
 * const originList = ['dog', 'cat', 'people'];
 * const formatList = arrayWrapper({
 *  '0': false,
 * }, originList); // -> ['cat', 'people']
 */
export interface IArrayWrapperParam {
  [key: string]: boolean;
}
export function arrayWrapper<T>(props: IArrayWrapperParam, array: T[]): T[] {
  const offShowKeys = _keys(_pickBy(props, isInStoreCondition => {
    return !isInStoreCondition;
  })).map(item => +item);

  const clonedArray = [...array];
  _remove(clonedArray, function(_, i) {
    return offShowKeys.indexOf(i) > -1;
  });

  return clonedArray;
}

// arrayWrapper 高阶版
export function arrayWrapperHOF<T>(props: IArrayWrapperParam) {
  const offShowKeys = _keys(_pickBy(props, (isInStoreCondition) => {
    return !isInStoreCondition;
  })).map(item => +item);

  return function arrayWrapper(array: T[]): T[] {
    const clonedArray = [...array];
    _remove(clonedArray, function(_, i) {
      return offShowKeys.indexOf(i) > -1;
    });

    return clonedArray;
  };
}

export interface IShowWrapperParam {
  children: ReactElement;
  isInStoreCondition: boolean;
}
/**
 * @description true | false 状态机组件
 * @param {IShowWrapperProps} props
 * @return {ReactElement || null}
 * @example
 * <ShowWrapper
 *  isInStoreCondition={isShow}
 * >
 *  我是需要根据店铺类型进行判断的组件
 * </ShowWrapper>;
 */
export function ShowWrapper(props: IShowWrapperParam): ReactElement | null {
  const { isInStoreCondition = false, children } = props;
  return isInStoreCondition ? children : null;
}

export interface IObjectWrapper {
  [key: string]: boolean;
}
/**
 * @description 对象进行筛选字段
 * @param {Object} props
 * {
 *    [key]: isInStoreCondition
 * }
 *  key 为传入的对象中需要判断的键
 * @param {Object} obj 包含所有情况的对象
 * @return {Object}
 * @example
 * const originObj = {
 *  a: 1,
 *  b: 2,
 *  c: 3
 * }
 * const formatObj = objectWrapper({
 *  a: false
 * }, originObj); // -> {b: 2, c: 3}
 */
export function objectWrapper(props: IObjectWrapper, obj) {
  const offShowKeys = _keys(_pickBy(props, (isInStoreCondition) => {
    return !isInStoreCondition;
  }));

  return _omit(obj, offShowKeys);
}

// objectWrapper 高阶版
export function objectWrapperHOF(props: IObjectWrapper) {
  const offShowKeys = _keys(_pickBy(props, (isInStoreCondition) => {
    return !isInStoreCondition;
  }));

  return function objectWrapper(obj) {
    return _omit(obj, offShowKeys);
  };
}
