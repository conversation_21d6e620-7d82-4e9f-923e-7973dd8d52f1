import { ReactElement } from 'react';
import {
  isPureWscBranchStore,
  isPureWscChainStore,
  isPureWscHqStore,
  isPureWscSingleStore,
  isWscBranchStore,
  isWscChainStore,
  isWscHqStore,
  isWscSingleStore,
} from '@youzan/utils-shop';
import { getComponents, IProps } from './base';

/*=========================(以下包含教育版)=======================*/

/**
 * @description 微商城单店（含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function WscSingleStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isWscSingleStore);
}

/**
 * @description 微商城连锁店铺（含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function WscChainStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isWscChainStore);
}

/**
 * @description 微商城连锁总部（含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function WscHqStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isWscHqStore);
}

/**
 * @description 微商城连锁分部（含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function WscBranchStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isWscBranchStore);
}

/*=========================(以下不包含教育版)=======================*/

/**
 * @description 微商城单店（不含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function PureWscSingleStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isPureWscSingleStore);
}

/**
 * @description 微商城连锁店铺（不含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function PureWscChainStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isPureWscChainStore);
}

/**
 * @description 微商城连锁总部（不含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function PureWscHqStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isPureWscHqStore);
}

/**
 * @description 微商城连锁分部（不含教育） 展示内容
 * @param {Object} props -
 * @param {ReactElement} props.children - 子组件
 * @param {boolean} props.negation - 是否取反 true:判断条件变成`!condition`
 * @return {ReactElement | null}
 */
export function PureWscBranchStoreCpn(props: IProps): ReactElement | null {
  return getComponents(props, isPureWscBranchStore);
}
