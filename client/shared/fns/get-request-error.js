import flatten from 'lodash/flatten';

function extractError(resp) {
  if (!resp) {
    return '未知错误';
  }

  if (typeof resp.msg === 'string') {
    return resp.msg;
  }

  if (typeof resp.name === 'string' && typeof resp.message === 'string') {
    return `${resp.name}: ${resp.message}`;
  }

  if (typeof resp.toJSON === 'function') {
    return resp.toJSON();
  }

  if (resp.toString) {
    return resp.toString();
  }

  return '格式化错误信息出错';
}

export default function getRequestError(...maybeResponse) {
  const msg = flatten(maybeResponse)
    .map(extractError)
    .join('\n');
  return msg;
}
