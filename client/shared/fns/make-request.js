/**
 * 此方法已废弃
 * @deprecated
 * @date 2019/02/27
 */

import ajax from 'zan-pc-ajax';
import { Notify } from 'zent';

/**
 * 发送接口请求方法聚合
 * @param {String} path: 请求路径
 * @param {Object} data: 请求参数
 * @param {String} method: 请求类型，默认为`get`
 */
export const makeRequest = (path, data = {}, method = 'GET') => {
  const options = {
    method,
    url: path,
    data,
    contentType: 'application/json',
  };
  return ajax(options).catch(error => {
    Notify.error(error || '网络错误');
  });
};
