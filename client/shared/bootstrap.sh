#!/bin/sh

# Only install packages with unmatched version,
# cache-loader will not work if the same package is reinstalled.

yyarn='yarn add --registry=http://registry.npm.qima-inc.com --disturl=https://npm.taobao.org/dist'

declare -a packages=()
packages_to_install=''

for pkg in "${packages[@]}"
do
    pkg_version=${pkg##*@}
    pkg_name=${pkg%@*}

    installed_version=$(grep "\"$pkg_name\"" package.json | tr -d ',"' | cut -d ':' -f2)

    if [ "$pkg_version" != "$installed_version" ]; then
      packages_to_install="$packages_to_install $pkg_name@$pkg_version"
    fi
done

if [ ! -z "$packages_to_install" ]; then
  eval "$yyarn $packages_to_install"
fi
