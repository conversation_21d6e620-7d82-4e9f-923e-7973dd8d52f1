import React, { Component } from 'react';
import PropTypes from 'prop-types';

import './style.scss';

export default class Loading extends Component {
  static propTypes = {
    size: PropTypes.number,
    className: PropTypes.string,
  };

  static defaultProps = {
    size: 16,
  };

  render() {
    const size = parseInt(this.props.size, 10);
    const className = this.props.className || '';

    return (
      <i className={`common-inline-loading ${className}`} style={{ width: size, height: size }} />
    );
  }
}
