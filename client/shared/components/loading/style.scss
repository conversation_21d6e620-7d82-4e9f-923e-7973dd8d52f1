.common-inline-loading {
    display: inline-block;
    animation: zenticon-animation-spin 1s infinite linear;
    border: 1px solid #555;
    border-radius: 50%;
    border-top-color: rgba(0, 0, 0, 0) !important;
    box-sizing: border-box;
}

@keyframes zenticon-animation-spin {
  0% {
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
