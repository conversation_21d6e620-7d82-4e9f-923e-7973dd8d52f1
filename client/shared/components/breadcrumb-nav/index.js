import React from 'react';
import { render } from 'react-dom';
import { Breadcrumb } from 'zent';
import cx from 'classnames';

import './style.scss';

const { Item } = Breadcrumb;

const getTreePath = (config, url) => {
  let rlt = [];
  for (let i = 0; i < config.length; i++) {
    const { name, href, children, isHash = true } = config[i];

    if (href === url) {
      rlt.push({ name });
      return rlt;
    }

    if (children instanceof Array) {
      const child = getTreePath(children, url);
      if (child && child.length) {
        rlt.unshift({
          name,
          href: isHash ? `#${href}` : href,
        });
        return rlt.concat(child);
      }
    }
  }

  return [];
};

class BreadcrumbNav {
  constructor({ config, container }) {
    this.config = config;
    this.navContent = document.querySelector(container);
  }

  go(url) {
    const Nav = (
      <Breadcrumb className="zent-breadcrumb-nav">
        {this.config.map(({ href, name, isHash = true, target = '_self' }, index) => (
          <Item
            target={target}
            key={index}
            href={isHash ? `#${href}` : href}
            name={name}
            className={cx({ 'zent-breadcrumb-nav-active': href === url })}
          />
        ))}
      </Breadcrumb>
    );
    render(Nav, this.navContent);
  }

  goDetail(url) {
    const path = getTreePath(this.config, url);

    const Nav = (
      <Breadcrumb>{path.map((items, index) => <Item key={index} {...items} />)}</Breadcrumb>
    );
    render(Nav, this.navContent);
  }

  renderNav(config) {
    const Nav = (
      <Breadcrumb className="zent-breadcrumb-nav">
        {config.map(({ href = '', name, isHash = true, isActive }, index) => (
          <Item
            key={index}
            href={isHash ? `#${href}` : href}
            name={name}
            className={cx({ 'zent-breadcrumb-nav-active': isActive })}
          />
        ))}
      </Breadcrumb>
    );
    render(Nav, this.navContent);
  }
}

export default BreadcrumbNav;
