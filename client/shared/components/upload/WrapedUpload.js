import React from 'react';
import { Sweetalert, Notify } from 'zent';
import { Upload } from '@youzan/react-components';
import getRequestError from 'shared/fns/get-request-error';
import { WHITE_LIST } from './constants';

// uploadTokenType用于指定微商城常用的tokenUrl，也可通过tokenUrl来指定不常用的tokenUrl
const uploadTokenMap = {
  shopImg: `${_global.url.materials}/shop/pubImgUploadToken.json`, // 会在我的文件中显示，图片上传常用接口
  shopAudio: `${_global.url.materials}/shop/pubAudioUploadToken.json`, // 会在我的文件中显示，音频上传常用接口
  storageImg: `${_global.url.materials}/storage/pubImgUploadToken.json`, // 不会在我的文件中显示，如店铺Logo
  storageImgPrivate: `${_global.url.materials}/storage/priImgUploadToken.json`, // 不会在我的文件中显示，如店铺认证
  salesmanImg: `${_global.url.base}/salesman/common/image/getToken.json`, // 分销员上传图片
};

class WrapedUpload extends React.Component {
  componentWillMount() {
    const { uploadTokenType, tokenUrl } = this.props;
    if (!uploadTokenType) {
      if (!tokenUrl) {
        console.error('uploadTokenType和tokenUrl必须指定一个'); // eslint-disable-line
      }
    } else {
      if (tokenUrl) {
        console.log('uploadTokenType和tokenUrl只需定义一个，同时定义以tokenUrl为准'); // eslint-disable-line
      }
      if (!uploadTokenMap[uploadTokenType]) {
        console.error('uploadTokenType不合法'); // eslint-disable-line
      }
    }
  }

  onError(msg) {
    getRequestError(msg);

    if (msg.indexOf('订购') !== -1) {
      Notify.clear();
      Sweetalert.confirm({
        title: '上传失败',
        content: <div style={{ width: 460 }}>{msg}</div>,
        confirmText: '立即订购',
        cancelText: '我再想想',
        onConfirm: () => {
          window.location.href = `${window._global.url.www}/member#chooseService`;
        },
      });
    }
  }

  render() {
    let {
      type,
      maxSize,
      children,
      uploadTokenType,
      fetchUrl,
      tokenUrl,
      showIconTab,
      ...rest
    } = this.props;
    const uploadTokenUrl = uploadTokenMap[uploadTokenType];

    if (maxSize === undefined) {
      maxSize = 3 * 1024 * 1024;
      if (type === 'voice') {
        if (WHITE_LIST.indexOf(window._global.kdtId) !== -1) {
          maxSize = 40 * 1024 * 1024;
        } else {
          maxSize = 6 * 1024 * 1024;
        }
      }
    }

    return (
      <Upload
        // fetchUrl={fetchUrl || `${_global.url.materials}/shop/fetchPubImg.json`}
        fetchUrl={fetchUrl || '/v4/api/iron/materials/shopPubImg.json'}
        tokenUrl={tokenUrl || uploadTokenUrl}
        onError={this.onError}
        type={type}
        maxSize={maxSize}
        showIconTab={showIconTab}
        {...rest}
      >
        {children}
      </Upload>
    );
  }
}

WrapedUpload.defaultProps = {
  uploadTokenType: 'shopImg',
};

export default WrapedUpload;
