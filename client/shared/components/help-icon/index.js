import React from 'react';
import PropTypes from 'prop-types';
import { Pop, Icon } from 'zent';

import './style.scss';

const HelpIcon = ({ help, position, type }) => {
  return help ? (
    <Pop className="common-hint-pop" position={position} trigger="hover" content={help}>
      <Icon className="common-hint-icon help-icon" type={type} />
    </Pop>
  ) : null;
};

HelpIcon.propTypes = {
  help: PropTypes.node,
  position: PropTypes.string,
  type: PropTypes.string,
};

HelpIcon.defaultProps = {
  type: 'help-circle',
  position: 'top-center',
};

export default HelpIcon;
