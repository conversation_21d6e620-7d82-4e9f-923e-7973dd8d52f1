(function() {
  const EVENT = 'onPageHelpLoadSuccess';
  const SENTINEL = undefined;
  let currentId = SENTINEL;

  module.exports = function(id) {
    const pageHelp = window.pageHelp;

    if (pageHelp) {
      currentId = SENTINEL;
      callPageHelp(id);
    } else if (currentId === SENTINEL) {
      currentId = id;
      window.addEventListener(EVENT, handler);
    } else {
      currentId = id;
    }
  };

  function callPageHelp(id) {
    const pageHelp = window.pageHelp;

    try {
      if (pageHelp) {
        const args = Object.prototype.toString.call(id) === '[object Object]' ? id : { id: id };
        // 是否需要加 retail_ 前缀逻辑，默认 false
        args.addRetailPrefix = true;
        // 是否需要加 edu_ 前缀逻辑，默认 false
        args.addEduPrefix = true;
        pageHelp(args);
      }
    } catch (err) {
      console.error(err);
    }
  }

  function handler() {
    window.removeEventListener(EVENT, handler);
    if (currentId !== SENTINEL) {
      callPageHelp(currentId);
    }
  }
})();
