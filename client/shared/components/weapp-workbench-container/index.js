import React, { Component } from 'react';
import cx from 'classnames';
import SecondNav from '../second-nav';
import get from 'lodash/get';
import navsConfig from './config';
import PropTypes from 'prop-types';

// 是否精简版店铺
const isDspShop = +get(window, '_global.dsp_shop_config') === 1;
const dspSourceChannel = +get(window, '_global.dspSourceChannel');
const isBdDspChannel = isDspShop && dspSourceChannel === 3; // 百度
const isKsDspChannel = isDspShop && dspSourceChannel === 1; // 快手
const isTxDspChannel = isDspShop && !isBdDspChannel && !isKsDspChannel; // 广点通
const withoutSecondSidebar = get(window, '_global.withoutSecondSidebar', true);

import './style.scss';

export default class WeappWorkbenchContainer extends Component {
  render() {
    const { active, children } = this.props;
    const config = get(this.props, 'config', navsConfig);

    // 1. 非dsp店铺且2. dsp店铺，是微信广点通来源
    const judgeShowSecondNav = (!isDspShop || isTxDspChannel) && withoutSecondSidebar;
    const showSecondNav = get(this.props, 'showNav', judgeShowSecondNav);

    const contentCls = cx('weapp-workbench__content', {
      'weapp-workbench__content--single': !showSecondNav,
      'weapp-workbench__content--navs': showSecondNav,
    });

    return (
      <div className="weapp-workbench">
        {showSecondNav && (
          <SecondNav className="weapp-workbench__navs" active={active} navs={config} />
        )}
        <div className={contentCls}>{children}</div>
      </div>
    );
  }
}

const navChildPropTypes = PropTypes.shape({
  name: PropTypes.string.isRequired,
  key: PropTypes.string.isRequired,
  url: PropTypes.string.isRequired,
  show: PropTypes.bool.isRequired,
  target: PropTypes.bool
});

const configPropTypes = PropTypes.oneOfType([
  navChildPropTypes,
  PropTypes.shape({
    name: PropTypes.string.isRequired,
    show: PropTypes.bool.isRequired,
    children: PropTypes.arrayOf(navChildPropTypes)
  })
]);

WeappWorkbenchContainer.propTypes = {
  active: PropTypes.string.isRequired,
  children: PropTypes.object,
  config: PropTypes.arrayOf(configPropTypes),
  showNav: PropTypes.bool
}
