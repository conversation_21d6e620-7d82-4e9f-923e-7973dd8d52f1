import { isHqStore, isSepShopMode } from '@youzan/utils-shop';
import get from 'lodash/get';

// 店铺是否授权微信小程序
const isBindWxWeapp = get(_global, 'isBindWxWeapp', false);
// 是否展示老的小程序个人中心
const showOldUserCenter = get(_global, 'showOldUserCenter', false);

// 是否为公共版小程序
const useCommon =
  get(_global, 'has_order_weapp.useCommon', false) &&
  !get(_global, 'has_order_weapp.isValid', true);

export default [
  {
    name: '概况',
    key: 'dashboard',
    url: '/v4/shop/weapp/dashboard',
    show: !useCommon,
  },
  {
    name: '统计',
    key: 'statistics',
    url: isBindWxWeapp ? '/v4/shop/weapp/statistics' : '/v4/shop/weapp/unauth?from=statistics',
    show: true,
  },
  {
    name: '功能',
    show: !useCommon,
    children: [
      {
        name: '好物圈',
        key: 'weapp-goodstuff',
        url: '/v4/shop/weapp/facility/goodStuff',
        show: !isSepShopMode,
      },
      {
        name: '微信搜索',
        key: 'weapp-search',
        url: '/v4/shop/weapp/facility/search',
        show: true,
      },
      {
        name: '附近小程序',
        key: 'weapp-nearby',
        url: '/v4/shop/weapp/facility/nearby',
        show: true,
      },
    ],
  },
  {
    name: '装修',
    show: true,
    children: [
      {
        name: '小程序主页',
        key: 'homepage',
        url: isBindWxWeapp ? `${window._global.url.www}/showcase/feature/list` : `/v4/shop/weapp/unauth?from=homepage`,
        show: true,
        target: isBindWxWeapp
      },
      {
        name: '小程序导航',
        key: 'weapp-navs',
        url: `${window._global.url.www}/showcase/weapp/nav`,
        show: true,
      },
      {
        name: '个人中心',
        key: 'usercenter',
        url: showOldUserCenter
          ? `${window._global.url.www}/showcase/weapp/usercenter`
          : '/v4/shop/weapp/usercenter',
        show: true,
      },
    ],
  },
  {
    name: '设置',
    // 非公共版 且是 (非连锁店铺 或 连锁总部)
    show: !useCommon && (!isSepShopMode || isHqStore),
    children: [
      {
        name: '基础信息',
        key: 'basic-info',
        url: !isBindWxWeapp
          ? '/v4/shop/weapp/unauth?from=basic-info'
          : '/v4/shop/weapp/wx/basic-info',
        show: true,
      },
      {
        name: '基础设置',
        key: 'basic-setting',
        url: isBindWxWeapp ? '/v4/shop/weapp/wx/basic-settings' : '/v4/shop/weapp/unauth?from=basic-setting',
        show: true,
      },
    ],
  },
];
