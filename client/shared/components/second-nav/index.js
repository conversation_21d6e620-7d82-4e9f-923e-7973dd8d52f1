import React, { Component } from "react";
import PropTypes from "prop-types";
import classnames from "classnames";

import "./style.scss";

/**
 * navs数据结构：
 * [
 *   {
 *     "name": "概况",
 *     "key": "dashboard",
 *     "url": "/v4/shop/weapp/dashboard",
 *     "show": true
 *   },
 *   {
 *     "name": "功能",
 *     "children": [
 *        {
 *           "name": "微信搜索",
 *           "key": "",
 *           "url": "/v4/shop/weapp/search",
 *           "show": true
 *        }
 *     ]
 *   }
 * ]
 */
export default class SecondNav extends Component {
  static propTypes = {
    navs: PropTypes.array,
    active: PropTypes.string,
    className: PropTypes.string
  };

  renderChildrenNavs(item = {}, index) {
    const { name, children = [], show } = item;
    const { active } = this.props;

    if (!show) return;

    return (
      <li className="second-nav__navs" key={index}>
        <div className="second-nav__navs-title">{name}</div>
        <ul>
          {children.map((child, index) => {
            const { show, name, key, url, target = false } = child;
            const cls = classnames({
              "second-nav__title": true,
              "second-nav__title--active": key === active
            });

            if (show) {
              return (
                <li className={cls} key={index}>
                  {target ? (
                    <a href={url} target="_blank" rel="noopener noreferrer">
                      {name}
                    </a>
                  ) : (
                    <a href={url}>{name}</a>
                  )}
                </li>
              );
            }
          })}
        </ul>
      </li>
    );
  }

  renderNoChildrenNav(item = {}, index) {
    const { key, show, name, url, target = false } = item;

    if (!show) return;

    const { active } = this.props;
    const cls = classnames({
      "second-nav__title": true,
      "second-nav__title--active": active === key
    });

    return (
      <li className={cls} key={index}>
        {target ? (
          <a href={url} target="_blank" rel="noopener noreferrer">
            {name}
          </a>
        ) : (
          <a href={url}>{name}</a>
        )}
      </li>
    );
  }

  render() {
    const { navs, className } = this.props;

    return (
      <ul className={`second-nav ${className}`}>
        {navs.map((item, index) => {
          if (item.children && item.children.length !== 0) {
            return this.renderChildrenNavs(item, index);
          } else {
            return this.renderNoChildrenNav(item, index);
          }
        })}
      </ul>
    );
  }
}
