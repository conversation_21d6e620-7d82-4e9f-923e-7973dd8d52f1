/**
 * Common components should be bundled in base.js
 *
 * FIXME: zent 7 changes component name
 */

/* eslint-disable */
import {
  <PERSON><PERSON>,
  BlockHeader,
  BlockLoading,
  Breadcrumb,
  Button,
  Checkbox,
  ClampLines,
  CopyButton,
  DatePicker,
  DateRangeQuickPicker,
  Dialog,
  Form,
  Grid,
  Icon,
  InlineLoading,
  Input,
  Notify,
  NumberInput,
  Pagination,
  Pop,
  Radio,
  Select,
  Slider,
  Sweetalert,
  Switch,
  Tabs,
  Tag,
} from 'zent';
/* eslint-enable */

import './page-help';
import './breadcrumb-nav';
