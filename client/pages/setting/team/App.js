/* eslint-disable camelcase */
// 历史原因，改成驼峰要改很多字段，先忽略
import { BusinessHour } from '@youzan/react-components';
import omit from 'lodash/omit';
import find from 'lodash/find';
import filter from 'lodash/filter';
import get from 'lodash/get';
import set from 'lodash/set';
import negate from 'lodash/negate';
import assign from 'lodash/assign';
import isUndefined from 'lodash/isUndefined';
import React, { Component, createContext } from 'react';
import ReactDOM from 'react-dom';
import { Button, BlockLoading, Notify, Dialog } from 'zent';
import getRequestError from 'shared/fns/get-request-error';
import { Loader } from '@youzan/content-loader-react';
import args from '@youzan/utils/url/args';
import { isWscBranchStore, isWscChainStore, isHqStore } from 'shared/fns/wsc-chain';
import {
  isRetailMinimalistBranchStore,
  isRetailMinimalistHqStore,
  isPureWscSingleStore,
  isEduBranchStoreV4,
  isEduHqStoreV4,
  isRetailHqStore,
  isRetailSingleStore,
  isChainStore,
  isRetailShop,
  SHOP_TYPE,
} from '@youzan/utils-shop';
import { cloud, cloudHook } from '@youzan/ranta-cloud-react';

import {
  getBusinessHoursSetting,
  getCommonSetting,
  querySecuredShow,
  querySubsidyShow,
  getPaymentSetting,
  updateBusinessHoursSetting,
  updateCommonSetting,
  setSecuredShow,
  setSubsidyShow,
  updatePaymentSetting,
  getDisplayCarriers,
  setDisplayCarriers,
  getShopConfigs,
  updateShopConfigs,
  getIsShowWepaySetting,
  getSelfPayIsOpen,
  isNewCategoryKdtWhite,
  isLocalDeliveryKdtWhite,
  getShopGoodsDefaultDisplay,
  setShopGoodsDefaultDisplayConfig,
  querySettlement,
  saveSettlement,
  getFastTradeSetting,
  setFastTradeSetting,
  getRetailSelfSettingsForm,
  setRetailSelfSettingsForm,
} from './api';
import { queryIndustryAbilityConfig, setMealTakingConfig } from './containers/meal-taking/help';
import {
  autoBookConfigKeysSnake,
  RETAIL_AUTO_VERIFY,
  AUTO_VERIFY_ENUM,
  isUsedShelfOrderPage,
} from '../team/constants';
import {
  CARD_CARRIER_TYPE,
  CARD_CARRIER_OTHER_TYPE,
  CARD_CARRIER_FREE_TYPE,
  CARD_CARRIER_SUB_FREE_TYPE,
} from './containers/base/components/member-recruit-field/constants';
import { MODE, ONLINE_SHOP_BUSINESS_STATUS } from './containers/business/constants';
import Goods from './containers/goods-form';
import Payment from './containers/payment-form';
import MealTaking from './containers/meal-taking';
import TakeFood from './containers/take-food';
import Packaging from './containers/packaging-form';
import Store from './containers/store-form';
import Wepay from './containers/wepay-form';
import { getMiniProgramConfig } from './containers/base/components/fans-buy-new-field/utils';
import {
  goodsRecommendKeys,
  settingConfigKeys,
  ONLY_FANS_BUY_CHANNEL,
  ONLY_FANS_BUY_CHANNEL_H5,
  ONLY_FANS_BUY_CHANNEL_MINI,
  INVOICE_CUSTOM_SETTING_SWITCH,
  RETAIL_SHELF_ORDER_AFTER_OFF_HOURS,
  tradeConfigKeys,
  packagingConfigKeys,
  GOODS_CREATE_SALE_STATUS,
  IS_SETTLEMENT_ACTIVE,
  COMMON_SWICH,
  FAST_ORDER_ENUM,
} from './constants';
import { collectShopConfigToSave } from './common/collect-shop-config-to-save';
import { list2InvoiceContent, list2InvoiceType } from './common/format-invoice';
import { syncBuyRecord } from './common/sync-common-config';
import {
  scrollToAnchor,
  lazyResolve,
  getOrderExpireTime,
  formatOrderExpireTime,
} from './common/utils';
import LocalDeliveryDialog from './dialog/local-delivery-dialog.js';
import { localDeliveryAbility } from './constants';
import { memberRecruitFieldConfigs } from './containers/base/components/member-recruit-field/constants';
import { diffBeforeRenderHook } from './cloud.js';

const { openDialog, closeDialog } = Dialog;

const dialogId = 'local-delivery_dialog';

const { attractFansV2LimitWeappVersion } = _global;
const miniProgramConfig = getMiniProgramConfig(attractFansV2LimitWeappVersion);

const isQttShop = get(_global, 'isQttShop', false);
const ANCHOR_ANIMATION_CLASS_NAME = 'page-anchor-target';
const ANCHOR_ANIMATION_FADE_OUT_CLASS_NAME = 'anchor-target-fade-out';

const DEFAULT_FAST_MODEL = {
  settingStatus: COMMON_SWICH.ON,
  settingShow: '0',
  type: FAST_ORDER_ENUM.SKU,
};

const DEFAULT_SELF_VERIFY = {
  retailAutoVerify: 0,
  autoVerify: 0,
  orderSecondConfirm: 1,
  selfRefundConfig: 1,
  reserveContactName: '1',
  reserveContactNumber: '0',
  contactNumberRequired: '0',
};

const isShowShopOpenGift = isPureWscSingleStore || isRetailSingleStore || isChainStore;

const flatFormValue = data => {
  if (typeof data !== 'object') {
    return {};
  }
  return Object.keys(data).reduce((res, key) => {
    if (typeof data[key] === 'object') {
      assign(res, data[key]);
    } else {
      res[key] = data[key];
    }
    return res;
  }, {});
};

const isCardDisplayCarrier = carrier =>
  carrier.carrierType === CARD_CARRIER_TYPE || carrier.carrierType === CARD_CARRIER_OTHER_TYPE;

const isFreeMemberCard = carrier =>
  get(carrier, 'carrierType') === CARD_CARRIER_OTHER_TYPE &&
  get(carrier, 'carrierSubType') === CARD_CARRIER_SUB_FREE_TYPE;

export const CloudContext = createContext();

@cloud()
class CommonSetting extends Component {
  ctx;

  /**
   * beforePageRender
   * @desc 页面渲染前触发
   */
  @cloud('beforePageRender', 'hook', {
    allowMultiple: true,
  })
  beforePageRender = cloudHook();

  constructor(props) {
    super(props);
    this.state = {
      isSubmitted: false,
      isCheckedLocalDelivery: false,

      // 原通用设置相关的内容，服务还是有 iron 提供(复合接口暂时没有 dubbo 服务))，暂未修改字段
      commonModel: {
        // 数据默认值
        // 经营设置模块
        multi_store_on: 0, // 是否开启多网点

        // 基础设置模块
        only_fans_buy: {
          no_fans_buy: 0, // 购物门槛   1: 仅粉丝可购买   0: 粉丝非粉丝都可以购买
          [ONLY_FANS_BUY_CHANNEL]: 1, // 仅限粉丝购买的 渠道 1:微信渠道 0:所有渠道
          only_fans_buy_channel_h5: 0, // h5渠道
          only_fans_buy_channel_mini: 0, // 微信小程序渠道
        },
        hide_shopping_cart: 0, // 隐藏购物车  1: 隐藏   0: 展示
        shop_page_cart_display_switch: '0', // 显示加购数量 1:显示 0:隐藏
        shopping_cart_style: '0', // 购物车样式
        show_wsc_web_im: 0, // 联系客服功能是否打开
        is_web_im_in_goods: '1', // 商详页是否展示联系客服
        is_web_im_in_order: '1', // 订单页是否展示联系客服
        customer_blacklist_switch: 1, // 店铺黑名单
        display_staff_watermark: '0', // 店铺后台水印
        hq_senior_admin_can_verify: '0', // 更换店铺负责人配置 0: 总部负责人 1: 总部高级管理员
        // cardEnable: false, // 办卡设置
        cardAlias: null, // 会员卡 alias
        levelSkuId: null, // 等级和商品sku的关联id
        carrierType: 0, // 载体类别 1-付费等级，2-卡 0-未设置
        guideMemberScene: {
          itemDetailSwitch: false,
          readyOrderPageSwitch: false,
        }, // 商详和下单页的引导开关
        // 页面设置
        shop_atmosphere_marketing: '0', // 是否开启店铺营销氛围 '1': 展示   '0' 不展示
        showcase_display_member_price: '1', // 是否开启展示会员价标签 '1': 展示   '0' 不展示
        hide_top_bar: 0, // 是否隐藏店铺顶部导航 1: 不展示    0: 展示
        use_self_logo: 0, // 店铺底部Logo 0: 默认     1: 自定义
        logo_url: '//b.yzcdn.cn/v2/image/wap/common/<EMAIL>', // 自定义 Logo 地址
        background_color: '#f2f2f2', // 微商城默认背景色
        is_display_suffix_name: false, // 页面标题添加统一后缀
        suffix_name: '', // 统一添加的后缀
        use_team_title: 0, // 微信内打开微页面时，使用 ”店铺名称“ 作为页面标题
        show_sold_out_goods: 1, // 展示售罄商品   1: 展示     0: 不展示
        sold_out_goods_flag: '', // 展示售罄商品时，可选择售罄商品的标识
        homepage_gray: { isOpen: false, timeRange: [0, 0] }, // 首页是否置灰，以及置灰时间

        // 商品设置
        customer_reviews: 1, // 商品评价  0 关闭全网  1 开启全网  2 关闭全网评价及买家评价
        goods_recommend: 1, // 更多商品推荐   1 开启    0 关闭
        show_shop_btn: 0, // 商详页店铺按钮   1 展示    0 不展示
        goods_ranking_list: 1, // 店铺榜单信息 1 展示    0 不展示
        show_buy_btn: 1, // 商详页立即购买按钮   1 展示    0 不展示
        is_youzan_secured: '0', // 是否加入了有赞担保     只读
        guarantee_show_style_list: ['0', '3', '1', '2', '4'],
        guarantee_show_style_type: '0', // 有赞担保样式   0 / 3 / 1 / 2 / 4
        buy_record: 0, // 销量成交记录展示  1 展示    0 不展示
        qr_code: 0, // 电脑版商品详情二维码   0 关注后购买    1 直接购买
        evaluation_member_label: 0, // 评价会员标签展示（0:评价展示会员等级 1:评价展示会员名称）

        // 支付相关内容
        show_category_param: 1,
        company_cert: 0, // 是否通过公司认证 1 已通过    0 未通过    和是否需要身份证相关
        is_identity_card_required: 0, // 是否显示身份证信息

        show_in_mjb: 1, // 是否在被买家版收录 已废弃,
        self_pay: false,
        show_service_tags: 1,
        show_data_tags: 1,
        sameCityDelivery: 1,
        localDeliveryKdtWhite: false,
        goods_stock_warning: {
          enabled: '0',
          threshold: 0
        }
      },

      // 支付设置相关的内容
      paymentModel: {
        orderExpireTime: '', // 待付款订单取消时间设置
        trade_user_privacy_info_display: '0', // 是否开启个人信息脱敏
        trade_point_rate_mode: '0', // 积分倍率模式
        expressAutoConfirm: '', // 发货后自动确认收货时间设置
        editPriceType: '', // 未付款订单改价维度   0 整单基础上改价    1 单商品货款基础上改价
        receiverNameCheck: 0, // 是否校验姓名
        isRealNameAuthOpen: 0, // 是否开启实名认证
        realNameAuthMaxTimePerBuyerPerDay: 0, // 每位买家每天最大可校验次数
        limitRealNameAuthMaxTimePerBuyerPerDay: 0, // 是否开启校验
        shopAutoShip: 0,
        apply_refund_after_sale: '', // 买家申请售后时间限制
        autoConfirmReceiveTime: '', // 买家退货后商家自动确认收货时间
        enableDiscount: 0,
        discount: '',
        stockOccupationTime: 10, // 付款扣库存预占时间
        oversoldProcessMode: 1, // 超卖订单处理 0 人工处理 1 系统自动退款
        orderMode: '0',
        selfFetchVerifyCrossShop: '0', // 自提跨店核销
        skuOrder: '1',
        fastOrderModel: DEFAULT_FAST_MODEL,
        retailSelfVerify: DEFAULT_SELF_VERIFY,
        optimalPricingSetting: {},
        wxControlledApplet: 0, // 微信小程序受控标记：0-不受控；1-受控
      },
      // 打包物料退款设置内容
      packagingModel: {
        offlinePackingFeeSupportRefund: '0', // 门店打包费支持退款 0：不支持 1：支持
        onlinePackingFeeSupportRefund: '0', // 网店打包费支持退款 0：不支持 1：支持
      },
      // 店铺配置统一配置数据
      configModel: {
        use_self_text: '0', // 教育店铺底部导航文案 0：校区 1：店铺
        goods_detail_sales: {
          show: 0, // 销量展示 1 展示    0 不展示
          limit: false, // 销量数量限制控制开关
          limit_num: '', // 销量数量限制数值
          add_up_the_sales_of_three_parties: '0', // 是否累加三方销量
        },
        goods_trade_marquee: { show: 1, type: '1,2' }, // 商详页成交记录浮窗:复选
        goods_detail_buy_record: [], // 商详页展示成交记录:复选
        goods_recommend_goods: '1', // 商品推荐开关-商品详情页：0关闭 1开启
        goods_recommend_cart: '1', // 商品推荐开关-购物车：0关闭 1开启
        goods_recommend_pay: '1', // 商品推荐开关-支付成功页：0关闭 1开启
        goods_recommend_order_list: '1', // 商品推荐开关-订单列表页：0关闭 1开启
        goods_recommend_order_detail: '1', // 商品推荐开关-订单详情页：0关闭 1开启
        goods_recommend_order_search: '1', // 商品推荐开关-订单搜索页：0关闭 1开启
        goods_recommend_delivery: '1', // 商品推荐开关-物流详情页：0关闭 1开启
        goods_recommend_refund: '1', // 商品推荐开关-退款详情页：0关闭 1开启
        goods_recommend_free_member: '1', // 商品推荐开关-免费会员中心页：0关闭 1开启
        goods_recommend_paid_member: '1', // 商品推荐开关-付费会员中心页：0关闭 1开启
        wsc_im_mode: '1', // 客服接待模式  1 总部统一接待  0 网店客服接待  2自定义接待
        goods_recommend_for_you: {
          open: 0,
          selectReasons: [],
          customReason: '',
        },
        show_share_title: {
          default: 0,
          label: '',
        },
        show_brand_slogan: {
          default: 0,
          label: '',
        },
        guide_scene_member: {
          open: 1,
        },
        guideMemberScene: {
          itemDetailSwitch: false,
          readyOrderPageSwitch: false,
        }, // 商详和下单页的引导开关
        online_refund_stock_type: '1', // 网店库存回退方式 0 不做退回 1 自动退回 2 手动处理
        offline_refund_stock_type: '1', // 门店库存退回方式，同网店
        cross_store_self_fetch_switch: '0', // 跨店切换自提点 0 关闭 1 开启
        stock_deduct_mode: '0', // 默认库存扣减方式 0 拍下减库存 1 付款减库存
        reserved_stock_mode: '0', // 预留库存方式 0 下单预留库存 1 下单不预留库存
        chain_online_shop_visit_strategy: '1',
        goods_poster_style: 1, // 海报样式 1：默认样式 2:促销样式
        goods_label: 1, // 商品标签 0：关闭   1：开启
        open_arrive_time: 1, // 商品最快配送/自提时间 0：关闭   1：开启
        goods_weapp_card_style: 1,
        goods_weapp_card_img_rules: '1,2', // 小程序分享封面图处理规则
        show_poster_personal_info: '1',
        show_category_param: 1, // 类目参数展示与否
        goods_activity_tags_preposition: 1, // 领券促销入口置顶
        goods_sales_reminder: '1', // 商品开售提醒
        goods_supply_reminder: '0', // 商品补货提醒
        // 评价排序（0:智能推荐排序 1:评价产生时间排序）
        goods_evaluation_sort_for_pdp: 0,
        evaluate_tags_switch: 1, // 猜你想说开关（0关闭，1开启）
        // H5页面分享标题
        h5_item_share_title: {
          default: 0,
          prefix: '',
        },
        // H5页面分享描述
        h5_item_share_desc: 0,
        goods_use_ai_category: 0,
        open_goods_makeup_order: '1', // 是否开启商详加购推荐 0关闭 1开启
        goods_share_page_style: '0', // 商品分享打开页配置 0基础样式 1沉浸样式

        physical_store_setting: [0],
        physical_store_list_type: '0',
        physical_store_use: '0',
        free_trial_settings: [], // 预约礼显示设置
        is_support_local_switch: '1', // 是否开启推荐可配送店铺
        order_change_dispatch_limit: '0', // 订单改派限制: 默认 0(只能改派到有供货的门店/仓库)
        online_diff_sku_exchange: '1', // 网店订单跨 sku 换货: 默认 1(开启)
        goods_detail_navigation_bar_style: '0', // 商品详情顶部导航效果 0普通(默认) 1沉浸式,
        order_evaluation_expire_days: 15,
      },

      /** 额外商品设置 */
      goodsModel: {},
      loading: true,
      successInited: false,
      saving: false,

      // 是否有支付设置表单
      isShowWepaySetting: false,
      isEnablePackaging: false, //  是否开启店铺能力
      /** 堂食外带设置 */
      mealTakingModel: {},
      /** 是否展示堂食外带设置 */
      isEnableMealTaking: false,
      /** 是否展示取餐叫号设置 */
      isEnableTakeFood: false,
      /** 大餐饮行业是否开启 */
      isEnableIndustrySetting: false,
      /** 语音提醒 */
      cdbSet: 1,
      beforeShopSettingsPageRenderPayload: {
        formList: [
          {
            key: 'memberRecruit',
            type: 'object',
            fieldConfigs: memberRecruitFieldConfigs,
          },
        ],
      },
    };
    this.disabledKeys = {}; // 表单disable数据
    // 未使用的展示载体数据缓存
    this.unusedDisplayCarriers = [];
  }

  componentDidMount() {
    this.fetchData();
    // 性能埋点
    window.mark?.log();
  }

  showLocalDeliveryDialog() {
    openDialog({
      title: '提示',
      children: <LocalDeliveryDialog />,
      dialogId,
      footer: (
        <div>
          <Button
            onClick={() => {
              closeDialog(dialogId);
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => {
              closeDialog(dialogId);
              this.setState({ isCheckedLocalDelivery: true });
              setTimeout(() => {
                this.triggerSave();
              }, 100);
            }}
          >
            确认关闭
          </Button>
        </div>
      ),
    });
  }

  /**
   * 注册哪些key是disable的，并保存其映射
   * HACK!!!
   * 用于处理两个用户同时打开表单，保存时另一用户已提前保存并修改了此用户disable的值，因此对应字段被覆盖的问题
   * 逻辑：核心为render时registry disable的表单字段，保存前渲染一个新的表单获取最新的表单数据，对disbale的表单字段做新旧对比，如果有变更则用新的数据
   * 涉及变量/函数：
   * this.disabledKeys：表单disable数据
   * makeRegistryDisable：注册disable的工厂函数，group是表单的key，registry有做防抖处理
   * getLatestData：渲染新的表单，获取最新数据
   * updateDisableFormValues：依据注册的disable的key更新disable的数据
   */
  makeRegistryDisable(group) {
    const disabledKeys = this.disabledKeys[group] || {};
    const registryDisable = (key, status) => {
      if (typeof key === 'string') {
        disabledKeys[key] = status;
      } else {
        key.forEach(childKey => {
          disabledKeys[childKey] = status;
        });
      }
      this.disabledKeys[group] = disabledKeys;
    };
    return registryDisable;
  }

  setRef(key, ref) {
    this.ref = this.ref || {};
    this.ref[key] = ref;
  }

  // 页面滚动到固定位置
  scrollToHashForm(retryTimes = 0) {
    const { hash } = window.location;
    const hashName = (hash || '').replace(/^#/, '');
    let dom;
    const name = args.get('anchor', window.location.href);
    switch (true) {
      // 添加锚点跳转逻辑
      case !!name:
        scrollToAnchor(name);
        return;
      case /^(meal-taking|take-food|payment|goods|trade|poster-card|weapp-sharecard|weapp-sharetitle|h5-sharecard|goods-reminders|page|detail-sales|shop-watermark|user-privacy-display)$/.test(
        hashName
      ): {
          dom = document.getElementById(hashName);
          break;
        }
      default:
    }

    setTimeout(() => {
      if (dom) {
        try {
          if (dom.scrollIntoView) {
            dom.scrollIntoView({
              behavior: 'smooth',
            });
            dom.classList.add(ANCHOR_ANIMATION_CLASS_NAME);

            setTimeout(() => {
              dom.classList.add(ANCHOR_ANIMATION_FADE_OUT_CLASS_NAME);
            }, 1500);
          }
        } catch (err) {
          const offsetTop = dom ? dom.offsetTop : 0;
          try {
            document.body.scrollTop = offsetTop;
            if (document.documentElement) {
              document.documentElement.scrollTop = offsetTop;
            }
          } catch (err) {
            console.error(err); // eslint-disable-line
          }
        }
      } else if (retryTimes <= 5) {
        this.scrollToHashForm(retryTimes + 1);
      }
    }, 500); // 留时间图片这些加载完，跳到对应的位置
  }

  // 设置微信/小程序自有支付时，是否联系客服的选择
  selfPayDisableConfig(common, selfPayRes) {
    const { configs } = selfPayRes;
    // 当微信和小程序其中一个有自有支付时，将禁止客服入口取消选择
    if (+configs.weixin_pay_origin === 1 || +configs.wx_applet_origin === 1) {
      common.self_pay = true;
      common.is_web_im_in_goods = '1';
      common.is_web_im_in_order = '1';
    } else {
      common.self_pay = false;
    }
  }

  // 判断配置是否为json对象，是则转为json
  parseShopConfig(shopConfigData) {
    const { configs } = shopConfigData;
    function isJson(str) {
      if (typeof str === 'string') {
        try {
          const obj = JSON.parse(str);
          return typeof obj === 'object' && obj;
        } catch (e) {
          return false;
        }
      }
    }
    Object.keys(configs).forEach(c => {
      const v = configs[c];
      if (isJson(v)) configs[c] = JSON.parse(v);
    });
  }

  fetchData(loading = true) {
    this.setState({ loading });
    const allShopConfigKeys = goodsRecommendKeys.concat(
      settingConfigKeys,
      tradeConfigKeys,
      packagingConfigKeys
    );

    Promise.all([
      getShopConfigs({
        configs: JSON.stringify([
          ONLY_FANS_BUY_CHANNEL,
          ONLY_FANS_BUY_CHANNEL_H5,
          ONLY_FANS_BUY_CHANNEL_MINI,
          INVOICE_CUSTOM_SETTING_SWITCH,
          RETAIL_SHELF_ORDER_AFTER_OFF_HOURS,
        ]),
      }),
      getCommonSetting(),
      getDisplayCarriers(),
      getBusinessHoursSetting(),
      getPaymentSetting(),
      getShopConfigs({ configs: JSON.stringify(allShopConfigKeys) }),
      getIsShowWepaySetting(),
      getSelfPayIsOpen(),
      isNewCategoryKdtWhite(),
      isLocalDeliveryKdtWhite(),
      lazyResolve(() => getShopGoodsDefaultDisplay(), isRetailMinimalistHqStore || isEduHqStoreV4),
      querySecuredShow().catch(() => ({})),
      querySubsidyShow().catch(() => ({})),
      queryIndustryAbilityConfig(),
      lazyResolve(querySettlement, IS_SETTLEMENT_ACTIVE),
      getFastTradeSetting().catch(() => ({})),
      getRetailSelfSettingsForm().catch(() => ({})),
    ])
      .then(
        ([
          shopConfigsData,
          common,
          displayCarriers,
          businessHours,
          payment,
          shopConfigData,
          wepayRes,
          selfPayRes,
          categoryKdtWhite,
          localDeliveryKdtWhite,
          goodsDefaultDisplay = GOODS_CREATE_SALE_STATUS.Off,
          guaranteeShow,
          subsidyShow,
          industryAbilityConfig,
          settlement,
          fastOrderSetting,
          retailSelfSetting,
        ]) => {
          const cardDisplayCarrier = find(displayCarriers, isCardDisplayCarrier);
          this.unusedDisplayCarriers = filter(displayCarriers, negate(isCardDisplayCarrier));
          const othersState = {};
          const recordSales = syncBuyRecord(common);
          this.selfPayDisableConfig(common, selfPayRes);
          this.parseShopConfig(shopConfigData);
          if (wepayRes) {
            othersState.isShowWepaySetting =
              wepayRes.isShowWepaySetting &&
              !isQttShop &&
              !isWscChainStore &&
              getWepayAbility('baseSetting', 'commonSettings');
          }
          this.prevLocalDeliveryCap = +get(shopConfigData, 'configs.local_delivery', 1);
          // jira处理 ONLINE-852599 兼容客服入口页面 商品详情页配置
          const web_im_in_goods_config = get(shopConfigData, 'configs.web_im_in_goods_config');
          if (web_im_in_goods_config && web_im_in_goods_config.label === undefined) {
            web_im_in_goods_config.label = '';
          }
          const onlyFansBuyChannel = get(shopConfigsData, 'configs.only_fans_buy_channel', 1);
          let onlyFansBuyChannelH5 = get(shopConfigsData, 'configs.only_fans_buy_channel_h5', 0);
          const defaultSelfPointRule = get(shopConfigsData, 'configs.default_self_point_rule', '0');
          const isInvoiceCustomSettingSwitchEnable =
            get(shopConfigsData, 'configs.invoice_custom_setting_switch', '0') === '1';
          let onlyFansBuyChannelMini = get(
            shopConfigsData,
            'configs.only_fans_buy_channel_mini',
            0
          );
          const apply_refund_after_sale = get(
            shopConfigData,
            'configs.apply_refund_after_sale',
            ''
          );

          // 因各种原因导致不能开启小程序渠道后，需要处理异常数据
          if (miniProgramConfig.disabled) {
            onlyFansBuyChannelMini = 0;
          }
          // 历史数据默认值
          if (+onlyFansBuyChannel === 1 && !onlyFansBuyChannelH5 && !onlyFansBuyChannelMini) {
            onlyFansBuyChannelH5 = '1';
          }
          const guideMemberScene = get(cardDisplayCarrier, 'scene');
          const {
            retailMinappShelfOrderSetting = '{}',
            autoVerify,
            retailAutoVerify,
            orderSecondConfirm,
            selfRefundConfig,
            cdbSet,
          } = retailSelfSetting || {};
          const formatretailMinappShelfOrderSetting = JSON.parse(retailMinappShelfOrderSetting);
          const {
            reserve_contact_name: reserveContactName = '1',
            reserve_contact_number: reserveContactNumber = '0',
            contact_number_required: contactNumberRequired = '0',
          } = formatretailMinappShelfOrderSetting;

          // 新增这个字段是因为原来的单选改成了多选，这段逻辑在前端处理
          // 原来的值：为0点单宝订单必选，为1则表示全部选择
          const retailAutoVerifyFormat = retailAutoVerify
            ? [RETAIL_AUTO_VERIFY.Retail, RETAIL_AUTO_VERIFY.All]
            : [RETAIL_AUTO_VERIFY.Retail];
          this.ctx.cloud
            .invoke('beforePageRender', this.state.beforeShopSettingsPageRenderPayload)
            .then(cloudResult => {
              this.setState({
                beforeShopSettingsPageRenderPayload: diffBeforeRenderHook({
                  cloudSettingsList: cloudResult,
                  payloadSettingsList: this.state.beforeShopSettingsPageRenderPayload,
                }),
              });
            });

          this.setState(
            {
              commonModel: {
                ...common,
                sold_out_goods_flag: get(shopConfigData, 'configs.sold_out_goods_flag', ''),
                homepage_gray: get(shopConfigData, 'configs.homepage_gray', {
                  isOpen: false,
                  timeRange: [0, 0],
                }),
                only_fans_buy: {
                  [ONLY_FANS_BUY_CHANNEL]: onlyFansBuyChannel,
                  only_fans_buy_channel_mini: onlyFansBuyChannelMini,
                  only_fans_buy_channel_h5: onlyFansBuyChannelH5,
                  no_fans_buy: common.no_fans_buy,
                },
                // 办卡设置
                // cardEnable: !!cardDisplayCarrier,
                cardAlias: get(cardDisplayCarrier, 'bizValue'),
                levelSkuId: get(cardDisplayCarrier, 'levelSkuId'),
                carrierType: isFreeMemberCard(cardDisplayCarrier)
                  ? CARD_CARRIER_FREE_TYPE
                  : get(cardDisplayCarrier, 'carrierType'),
                guideMemberScene,
                // 历史遗留问题，命名和意义不服，取一下反
                hide_shopping_cart: Number(!common.hide_shopping_cart),
                shop_page_cart_display_switch: shopConfigData.shop_page_cart_display_switch || '0',
                // 新版营业时间
                businessHours: parseBusinessHoursModel(businessHours),
                retail_shelf_order_after_off_hours: get(
                  shopConfigsData,
                  'configs.retail_shelf_order_after_off_hours',
                  '1'
                ),
                sameCityDelivery: +get(shopConfigData, 'configs.local_delivery', 1),
                sameCityDeliveryGuide: +get(
                  shopConfigData,
                  'configs.local_delivery_ordering_guide',
                  0
                ),
                localDeliveryKdtWhite,
                categoryKdtWhite,
                // 营销氛围
                shop_atmosphere_marketing: get(
                  shopConfigData,
                  'configs.shop_atmosphere_marketing',
                  0
                ),
                // 会员价标签
                showcase_display_member_price: get(
                  shopConfigData,
                  'configs.showcase_display_member_price',
                  '1'
                ),
                // 网店开启状态
                shop_operation_status: get(shopConfigData, 'configs.shop_operation_status', 2),
                guarantee_show_style_list: get(guaranteeShow, 'securedShowIds', [
                  0,
                  3,
                  1,
                  2,
                  4,
                ]).map(res => res.toString()),
                guarantee_show_style_type: get(guaranteeShow, 'yzSecuredShowId', '0').toString(),
                subsidy_show_style_list: get(subsidyShow, 'canShowIds', ['1', '2', '0']),
                subsidy_show_style_type: get(subsidyShow, 'showId', '1'),
                settlement,
              },
              paymentModel: {
                ...this.state.paymentModel,
                ...payment,
                apply_refund_after_sale,
                houseNumberRequired: get(
                  shopConfigData,
                  'configs.house_number_required',
                  undefined
                ),
                default_self_point_rule: defaultSelfPointRule,
                isInvoiceCustomSettingSwitchEnable,
                // 根据时间单位转换成分，默认返回分payment.orderExpireTime
                orderExpireTime: getOrderExpireTime(
                  payment.orderExpireTime,
                  payment.orderExpireTimeUnit
                ),
                fastOrderModel: {
                  ...DEFAULT_FAST_MODEL,
                  ...fastOrderSetting,
                  settingShow: get(
                    shopConfigData,
                    'configs.payment_setting_order_display_activity',
                    '0'
                  ),
                },
                retailSelfVerify: {
                  autoVerify,
                  retailAutoVerify,
                  retailAutoVerifyFormat,
                  orderSecondConfirm,
                  selfRefundConfig,
                  reserveContactName,
                  reserveContactNumber,
                  contactNumberRequired,
                },
              },
              cdbSet,
              mealTakingModel: {
                ...this.state.mealTakingModel,
                ...industryAbilityConfig,
              },
              isEnableMealTaking: +get(industryAbilityConfig, 'meal_taking_industry_setting') === 1,
              isEnableTakeFood: +get(industryAbilityConfig, 'take_food_inform') === 1,
              // isEnablePackaging: get(retailShopConfig, '[0].value') === 1,
              isEnablePackaging: false, // 行业入口能力跳转，暂时移除打包物料费退款设置
              // 大餐饮行业能力
              isEnableIndustrySetting: !isQttShop && (isRetailHqStore || isRetailSingleStore),
              // 打包物料配置
              packagingModel: {
                offlinePackingFeeSupportRefund: get(
                  shopConfigData,
                  'configs.offline_packing_fee_support_refund',
                  0
                ),
                onlinePackingFeeSupportRefund: get(
                  shopConfigData,
                  'configs.online_packing_fee_support_refund',
                  0
                ),
              },
              configModel: {
                ...this.state.configModel,
                ...recordSales,
                ...shopConfigData.configs,
                guideMemberScene,
              },
              goodsModel: {
                goodsDefaultDisplayConfig: { defaultDisplay: goodsDefaultDisplay },
              },
              loading: false,
              successInited: true,
              ...othersState,
            },
            () => {
              this.scrollToHashForm();
            }
          );
        }
      )
      .catch(err => {
        this.setState({
          loading: false,
        });
        if (typeof err === 'string') {
          Notify.error(err || '网络错误，请稍候再试!');
        }
      });
  }

  getLatestData() {
    return new Promise(resolve => {
      try {
        const docu = document.createElement('div');
        let times = 0;
        let timer = null;
        const myRef = React.createRef();
        const getRef = () => {
          if (times > 4) {
            resolve(undefined);
            return;
          }
          times++;
          // loading之后才会渲染form，才会有ref，因此有ref说明数据已经准备完成
          if (myRef && myRef.current && myRef.current.ref) {
            clearTimeout(timer);
            const ref = myRef.current.ref;

            resolve({
              storeData: ref.store.getFormValues(),
              goodsData: ref.goods.getFormValues(),
              paymentData: ref.payment.getFormValues(),
            });
          } else {
            timer = setTimeout(getRef, 500);
          }
        };
        ReactDOM.render(<CommonSetting ref={myRef} />, docu, getRef);
      } catch (error) {
        resolve(undefined);
      }
    });
  }

  triggerSave = () => {
    const {
      isShowWepaySetting,
      isCheckedLocalDelivery,
      isEnablePackaging,
      commonModel: { localDeliveryKdtWhite },
    } = this.state;
    const storeData = this.ref.store.getFormValues();
    // 微商城单店（不包含教育）、白名单或者具备店铺能力，关闭同城能力时提醒
    // 还需要判断本次是不是改动到啦
    if (
      isPureWscSingleStore &&
      (localDeliveryKdtWhite || localDeliveryAbility) &&
      !+storeData.sameCityDelivery &&
      +storeData.sameCityDelivery !== this.prevLocalDeliveryCap &&
      !isCheckedLocalDelivery
    ) {
      return this.showLocalDeliveryDialog();
    }

    this.setState(
      {
        isSubmitted: true,
      },
      () => {
        // FIXME: 群团团版没有店铺和商品设置表单，跳过这两个表单校验
        // FIXME: 这段代码比较黑，页面表单结构重构掉之后可以简化。
        const list = isQttShop ? ['payment'] : ['store', 'goods', 'payment'];
        if (isShowWepaySetting) {
          list.push('wepay');
        }
        if (isEnablePackaging) {
          list.push('packaging');
        }

        Promise.all(
          list.map(key => {
            const form = this.ref[key];
            return new Promise(resolve => {
              form.validateForm(true, () => {
                form.asyncValidateForm(resolve, resolve);
              });
            });
          })
        ).then(() => {
          const hasError = list.some(key => {
            const form = this.ref[key];
            const valid = form.isValid();

            if (valid) {
              return false;
            } else {
              form.submit(); // 滚动到错误处
              return true;
            }
          });

          if (!hasError) {
            return this.getLatestData().then(this.saveData.bind(this));
          }
        });
      }
    );
  };

  processPaymentData(paymentData) {
    // voice_setting 是前端定义的，包含语音提醒设置相关字段
    // 用设置好的值覆盖接口取过来的值
    if (paymentData.voice_setting) {
      assign(paymentData, paymentData.voice_setting);
      delete paymentData.voice_setting;
    }

    if (paymentData.wait_confirm_setting) {
      assign(paymentData, paymentData.wait_confirm_setting);
      delete paymentData.wait_confirm_setting;
    }
  }
  /**
   * @description 修改data的引用，更新数据
   */
  updateDisableFormValues(latestData, { storeData, goodsData, paymentData }) {
    const getKeys = (keysMap = {}) => Object.keys(keysMap).filter(key => keysMap[key]);
    try {
      const disabledKeys = this.disabledKeys || {};
      const {
        storeData: latestStoreData,
        goodsData: latestGoodsData,
        paymentData: latestPaymentData,
      } = latestData;
      getKeys(disabledKeys.store).forEach(key => {
        set(storeData, key, get(latestStoreData, key));
      });
      getKeys(disabledKeys.payment).forEach(key => {
        set(paymentData, key, get(latestPaymentData, key));
      });
      getKeys(disabledKeys.goods).forEach(key => {
        set(goodsData, key, get(latestGoodsData, key));
      });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error(error);
    }
  }

  saveData(latestData) {
    const {
      commonModel,
      paymentModel,
      isShowWepaySetting,
      isEnablePackaging,
      isEnableMealTaking,
      isEnableIndustrySetting,
    } = this.state;

    const localDeliveryKdtWhite = commonModel.localDeliveryKdtWhite;
    const storeData = this.ref.store.getFormValues();
    const goodsData = this.ref.goods.getFormValues();
    const paymentData = this.ref.payment.getFormValues();
    // 转一下字符串
    autoBookConfigKeysSnake.forEach(key => {
      if (paymentData[key] !== undefined) {
        paymentData[key] = `${+paymentData[key]}`;
      }
    });
    const packagingData = isEnablePackaging ? this.ref.packaging.getFormValues() : {};
    const mealTakingData =
      isEnableIndustrySetting && isEnableMealTaking ? this.ref?.mealTaking?.getFormValues() : {};
    if (latestData) {
      this.updateDisableFormValues(latestData, { storeData, goodsData, paymentData });
    }
    const sameCityDelivery = +storeData.sameCityDelivery;
    this.processPaymentData(paymentData);

    if (isShowWepaySetting) {
      const wepayData = this.ref.wepay.getFormValues();

      // 设置微信支付优惠字段
      paymentData.weixinPaymentDiscount = wepayData.weixinPaymentDiscount;
    }

    paymentData.localDeliveryPosition = +paymentData.localDeliveryPosition;

    if (isRetailShop) {
      delete paymentData.localDeliveryPosition;
    }
    // 当前 form 值
    const common = {
      ...commonModel,
      ...flatFormValue(omit(storeData, ['businessHours', 'wsc_im_mode', 'settlementTime'])),
      ...flatFormValue(omit(goodsData, ['goodsRecommends', 'goodsDefaultDisplayConfig'])),
    };

    let businessHours = formatBusinessHoursModel(
      storeData.businessHours || {
        mode: 2,
        businessHoursSetting: {
          businessHoursMode: 1,
        },
      }
    ); // 教育微课堂版本没有这个选项，按默认处理

    // 营业时间不传t状态停用状态
    businessHours = omit(businessHours, ['shopOperationStatus']);
    // 用于表单联动修改数据，当上传的数据表单提交的数据没有该field时，自动设置为默认值
    const defaultPayment = {
      isRealNameAuthOpen: 0,
      limitRealNameAuthMaxTimePerBuyerPerDay: 0,
      realNameAuthMaxTimePerBuyerPerDay: 1,
    };
    const fastOrderData = { ...paymentModel.fastOrderModel, ...paymentData.fastOrderModel };
    let payment = {
      ...omit(paymentModel, ['fastOrderModel']),
      ...defaultPayment,
      ...flatFormValue(
        omit(paymentData, [
          'invoiceContent',
          'invoiceTimeLimitIsOpen',
          'invoiceTimeLimit',
          'invoiceType',
          'fastOrderModel',
        ])
      ),
      // 根据时间单位转换成分，默认返回分paymentData.orderExpireTime
      orderExpireTime: formatOrderExpireTime(
        paymentData.orderExpireTime,
        paymentData.orderExpireTimeUnit
      ),
      orderExpireTimeUnit: paymentData.orderExpireTimeUnit,
      invoiceContent: list2InvoiceContent(paymentData.invoiceContent),
      invoiceTimeLimit: paymentData.invoiceTimeLimitIsOpen && paymentData.invoiceTimeLimit,
      invoiceType: list2InvoiceType(paymentData.invoiceType),
    };

    const payment_setting_buyer_message = paymentData.payment_setting_buyer_message;

    // 兼容数据
    common.display_suffix_name = common.is_display_suffix_name;
    common.display_footbar = common.is_display_footbar;
    common.is_identity_card_required =
      payment.is_identity_card_required === undefined // 如果是 0 也是可能的
        ? common.is_identity_card_required
        : payment.is_identity_card_required;
    delete payment.is_identity_card_required;

    // 商品推荐
    const { goodsRecommends } = goodsData;
    // 去除门店推荐为空情况
    if (goodsRecommends && !isUndefined(goodsRecommends.goods_recommend)) {
      common.goods_recommend = goodsRecommends.goods_recommend;
    }

    const guideMemberData = storeData.guide_member || [];
    common.guide_member = {
      cardEnable: !!guideMemberData.length,
      guideScene: guideMemberData.includes('guideScene'),
      scene: {
        itemDetailSwitch: guideMemberData.includes('goodsDetail'),
        readyOrderPageSwitch: guideMemberData.includes('tradeBuy'),
        shelfOrderPageSwitch: guideMemberData.includes('orderBao'),
        cartPageSwitch: guideMemberData.includes('cart'),
      },
    };
    const shopConfigItems = collectShopConfigToSave(
      goodsData,
      storeData,
      common,
      payment,
      packagingData
    );

    // 连锁店铺不需要保存收货人姓名校验
    if (get(_global, ['shopInfo', 'shopType']) === SHOP_TYPE.RETAIL) {
      tradeConfigKeys.push('receiverNameCheck');
    }

    payment = omit(payment, tradeConfigKeys);

    // 连锁总部才保存
    if (isHqStore) {
      Object.assign(shopConfigItems, {
        wsc_im_mode: storeData.wsc_im_mode,
      });
    }
    // eslint-disable-next-line
    const { only_fans_buy: onlyFansBuy, sold_out_goods_flag, ...rest } = common;
    const shopDataConfig = {
      ...shopConfigItems,
      only_fans_buy_channel: onlyFansBuy.only_fans_buy_channel,
      sold_out_goods_flag,
      only_fans_buy_article: '0',
    };

    // 判断form是否存在 是否校验门牌号会存在店铺配置中
    // shopDataConfig.house_number_required = paymentData.localDeliveryPosition
    //   ? paymentData.houseNumberRequired
    //   : '0';

    if (isPureWscSingleStore) {
      shopDataConfig.only_fans_buy_channel_mini = String(onlyFansBuy.only_fans_buy_channel_mini);
      shopDataConfig.only_fans_buy_channel_h5 = String(onlyFansBuy.only_fans_buy_channel_h5);
      shopDataConfig.payment_setting_order_display_activity = String(fastOrderData.settingShow);
    }

    // 是否生成公众号文章，新版本&老版本判断
    if (
      isPureWscSingleStore &&
      ((+onlyFansBuy.only_fans_buy_channel === 0 && !miniProgramConfig.disabled) ||
        (+onlyFansBuy.only_fans_buy_channel === 1 && +onlyFansBuy.only_fans_buy_channel_mini === 1))
    ) {
      shopDataConfig.only_fans_buy_article = '1';
    }

    // 微商城单店（不包含教育） 、白名单或者是有店铺能力就传这个参数
    if (isPureWscSingleStore && (localDeliveryKdtWhite || localDeliveryAbility)) {
      shopDataConfig.local_delivery = String(sameCityDelivery);
    }

    shopDataConfig.payment_setting_buyer_message = JSON.stringify(payment_setting_buyer_message);

    const { retail_shelf_order_after_off_hours: retailShelfOrderAfterOffHours } = storeData;
    if (retailShelfOrderAfterOffHours !== undefined) {
      shopDataConfig.retail_shelf_order_after_off_hours = retailShelfOrderAfterOffHours;
    } else {
      // 休息模式下，零售点单宝非营业时间预约下单关闭
      shopDataConfig.retail_shelf_order_after_off_hours = '0';
    }

    const shopConfigData = {
      configs: JSON.stringify(shopDataConfig),
    };
    // 零售自提传参
    const {
      // autoVerify,
      // retailAutoVerifyFormat,
      orderSecondConfirm,
      // selfRefundConfig,
      // reserveContactName,
      // reserveContactNumber,
      // contactNumberRequired,
    } = paymentData;

    // 人工发货，retailAutoVerify 需写死为 零售订单通用，后端设计问题改动成本大
    // let retailAutoVerify = null;
    // if (autoVerify === AUTO_VERIFY_ENUM.Manual) {
    //   retailAutoVerify = RETAIL_AUTO_VERIFY.Retail;
    // } else {
    //   // 自动发货
    //   if (isUsedShelfOrderPage) {
    //     retailAutoVerify = retailAutoVerifyFormat?.includes(RETAIL_AUTO_VERIFY.All)
    //       ? RETAIL_AUTO_VERIFY.All
    //       : RETAIL_AUTO_VERIFY.Retail;
    //   } else {
    //     // 没使用过的商家不依赖 retailAutoVerifyFormat 这个字段
    //     retailAutoVerify = RETAIL_AUTO_VERIFY.All;
    //   }
    // }
    const retailSelfData = {
      // autoVerify,
      // retailAutoVerify,
      orderSecondConfirm,
      // selfRefundConfig,
      cdbSet: this.state.cdbSet,
      // retailMinappShelfOrderSetting: {
      // reserveContactName,
      // reserveContactNumber,
      // contactNumberRequired,
      // },
    };

    this.updateData(
      {
        ...rest,
        no_fans_buy: onlyFansBuy.no_fans_buy,
      },
      businessHours,
      payment,
      shopConfigData,
      goodsData,
      mealTakingData,
      storeData.settlementTime,
      fastOrderData,
      retailSelfData
    );
  }

  updateData(
    { carrierType, cardAlias, guide_member, levelSkuId, ...commonData },
    businessHoursData,
    paymentData,
    shopConfigData,
    goodsData,
    mealTakingData,
    settlementTime,
    fastOrderData,
    retailSelfData
  ) {
    this.setState({
      saving: true,
    });
    // cardAlias 为空，表示关闭办卡设置，传空数组
    const displayCarriers = guide_member.cardEnable
      ? [
        {
          bizValue: cardAlias,
          carrierType,
          levelSkuId: carrierType === CARD_CARRIER_OTHER_TYPE ? levelSkuId : null,
          ...(carrierType === CARD_CARRIER_FREE_TYPE
            ? {
              carrierType: CARD_CARRIER_OTHER_TYPE,
              carrierSubType: CARD_CARRIER_SUB_FREE_TYPE,
            }
            : {}),
          scene: guide_member.scene,
        },
      ]
      : [];

    const goodsDefaultDisplayConfig = { ...goodsData.goodsDefaultDisplayConfig };
    // 非自定义新建商品初始状态时，不传自定义配置
    if (goodsDefaultDisplayConfig.defaultDisplay !== GOODS_CREATE_SALE_STATUS.Custom) {
      delete goodsDefaultDisplayConfig.subKdtDisplayConfigParam;
    }

    Promise.all([
      setDisplayCarriers({ displayCarriers }),
      updateCommonSetting(commonData),
      setSecuredShow({
        yzSecuredShowId: Number(commonData?.guarantee_show_style_type) || 0,
      }).catch(() => { }),
      setSubsidyShow({
        showId: commonData?.subsidy_show_style_type || '1',
      }).catch(() => { }),
      updateBusinessHoursSetting(businessHoursData),
      updatePaymentSetting(paymentData),
      updateShopConfigs(shopConfigData),
      lazyResolve(
        () => setShopGoodsDefaultDisplayConfig(goodsDefaultDisplayConfig),
        typeof goodsDefaultDisplayConfig.defaultDisplay !== 'undefined' &&
        (isRetailMinimalistHqStore || isEduHqStoreV4)
      ),
      setMealTakingConfig(mealTakingData),
      lazyResolve(() => saveSettlement({ settlementTime }), IS_SETTLEMENT_ACTIVE),
      setFastTradeSetting(fastOrderData),
      setRetailSelfSettingsForm(retailSelfData),
    ])
      // eslint-disable-next-line no-unused-vars
      .then(([carriers, common, securedShow, businessHours, payment, fastOrder, retailSelf]) => {
        if (!carriers) {
          return Promise.reject('保存办卡设置失败');
        }

        if (!securedShow) {
          return Promise.reject('保存有赞放心购样式失败');
        }

        if (!businessHours) {
          return Promise.reject('保存店铺营业时间失败');
        }

        if (!fastOrder) {
          return Promise.reject('保存极速下单失败');
        }

        Notify.success('保存成功');
        window.location.reload();
      })
      .catch(err => {
        Notify.error(getRequestError(err));
      })
      .finally(() => {
        this.setState({
          saving: false,
        });
      });
  }

  // 用来做跨组件的数据交互，value是{key, value}形式，key为commonModel某一字段
  onComponentsRefresh = value => {
    const { commonModel } = this.state;
    this.setState({
      commonModel: {
        ...commonModel,
        ...value,
      },
    });
  };

  render() {
    const {
      loading,
      successInited,
      commonModel,
      isSubmitted,
      configModel,
      goodsModel,
      isShowWepaySetting,
      packagingModel,
      isEnablePackaging,
      isEnableMealTaking,
      isEnableTakeFood,
      isEnableIndustrySetting,
      cdbSet,
      beforeShopSettingsPageRenderPayload,
    } = this.state;

    const sameCityDelivery = commonModel.sameCityDelivery;
    const paymentModel = {
      ...this.state.paymentModel,
      // 这两个字段作为支付设置的信息，实际在 common 接口里面，o(╯□╰)o
      is_identity_card_required: commonModel.is_identity_card_required,
      company_cert: commonModel.company_cert,
      cityCapIsEnable: sameCityDelivery,
    };
    const mealTakingModel = {
      ...this.state.mealTakingModel,
    };
    let content = null;
    let hiddenConfig = {};
    if (isQttShop) {
      // 群团团店铺只保留订单设置
      hiddenConfig = {
        Store: true,
        Goods: true,
        Payment: false,
      };
    }

    if (!loading) {
      if (successInited) {
        content = (
          <CloudContext.Provider
            value={{
              beforeShopSettingsPageRenderPayload,
            }}
          >
            <div className="common-setting">
              <Store
                ref={this.setRef.bind(this, 'store')}
                hidden={hiddenConfig.Store}
                model={commonModel}
                isSubmitted={isSubmitted}
                configModel={configModel}
                disabled={isWscBranchStore || isRetailMinimalistBranchStore || isEduBranchStoreV4}
                onRefresh={this.onComponentsRefresh}
                registryDisable={this.makeRegistryDisable('store')}
              />
              <Goods
                ref={this.setRef.bind(this, 'goods')}
                hidden={hiddenConfig.Goods}
                model={commonModel}
                configModel={configModel}
                goodsModel={goodsModel}
                disabled={isWscBranchStore || isRetailMinimalistBranchStore || isEduBranchStoreV4}
                onRefresh={this.onComponentsRefresh}
                registryDisable={this.makeRegistryDisable('goods')}
              />
              <Payment
                ref={this.setRef.bind(this, 'payment')}
                hidden={hiddenConfig.Payment}
                model={paymentModel}
                configModel={configModel}
                registryDisable={this.makeRegistryDisable('payment')}
              />

              {isEnableIndustrySetting && isEnableMealTaking && isRetailSingleStore && (
                <MealTaking
                  ref={this.setRef.bind(this, 'mealTaking')}
                  model={mealTakingModel}
                  configModel={configModel}
                  registryDisable={this.makeRegistryDisable('meal-taking')}
                />
              )}
              {isEnableIndustrySetting && isEnableTakeFood && (
                <TakeFood
                  cdbSet={cdbSet}
                  cdbSetChange={cdbSet => {
                    this.setState({
                      cdbSet,
                    });
                  }}
                  ref={this.setRef.bind(this, 'TakeFood')}
                />
              )}
              {isShowWepaySetting && (
                <Wepay ref={this.setRef.bind(this, 'wepay')} model={paymentModel} />
              )}
              {isEnablePackaging && (
                <Packaging ref={this.setRef.bind(this, 'packaging')} model={packagingModel} />
              )}
              {isShowShopOpenGift && (
                <>
                  <Loader
                    url="/v4/assets/output"
                    content="security/shop-open-gift"
                    props={{ pageSource: 'setting' }}
                  />
                  {/* 开店礼包二期弹窗 */}
                  <Loader
                    url="/v4/assets/output"
                    content="security/guarantee-subsidy-ad-dialog"
                    props={{
                      sceneNo: 'SHOP_SETTING_POPUP',
                    }}
                  />
                </>
              )}
              <div className="common-setting__save">
                <Button type="primary" onClick={this.triggerSave} loading={this.state.saving}>
                  保存
                </Button>
              </div>
            </div>
          </CloudContext.Provider>
        );
      } else {
        content = (
          <div className="common-setting load-error">
            <div className="reload-wrap">
              数据加载失败，
              <a
                href="javascript:;"
                onClick={() => {
                  this.fetchData();
                }}
              >
                点击重试
              </a>
            </div>
          </div>
        );
      }
    } else {
      content = <div className="common-setting" />;
    }

    return <BlockLoading loading={loading}>{content}</BlockLoading>;
  }
}

function getWepayAbility(abilityKey, namespaceId) {
  if (!_global.abilities) {
    return true;
  }

  return get(_global.abilities, `${abilityKey}#${namespaceId}.available`, true);
}

function formatBusinessHoursModel(clientData) {
  const {
    mode = null,
    businessHoursSetting = {},
    suspendSetting = {},
    shopOperationStatus,
  } = clientData;

  if (mode === MODE.SUSPEND || +shopOperationStatus === ONLINE_SHOP_BUSINESS_STATUS.DISABLED) {
    return {
      mode: MODE.SUSPEND,
      suspendSetting,
    };
  } else if (
    mode === MODE.BUSINESS_HOURS ||
    +shopOperationStatus === ONLINE_SHOP_BUSINESS_STATUS.RUNNING
  ) {
    const {
      businessHoursMode,
      dailyBusinessHours = [],
      weeklyBusinessHours = [],
    } = businessHoursSetting;

    if (businessHoursMode === BusinessHour.Mode.FULLTIME) {
      return {
        mode: MODE.BUSINESS_HOURS,
        businessHoursSetting: {
          businessHoursMode,
        },
      };
    } else if (businessHoursMode === BusinessHour.Mode.DAILY_REPEAT) {
      return {
        mode: MODE.BUSINESS_HOURS,
        businessHoursSetting: {
          businessHoursMode,
          dailyBusinessHours: dailyBusinessHours.map(formatTimeRange),
        },
      };
    } else if (businessHoursMode === BusinessHour.Mode.WEEKLY_REPEAT) {
      return {
        mode: MODE.BUSINESS_HOURS,
        businessHoursSetting: {
          businessHoursMode,
          weeklyBusinessHours: weeklyBusinessHours.map(({ days, times }) => {
            return {
              weekdays: days,
              timeRanges: times.map(formatTimeRange),
            };
          }),
        },
      };
    }
  }

  return clientData;
}

function formatTimeRange({ start, end }) {
  return {
    startTime: BusinessHour.formatTime(start),
    endTime: BusinessHour.formatTime(end),
  };
}

function parseBusinessHoursModel(serverData) {
  const { mode, businessHoursSetting = {}, suspendSetting = {} } = serverData;
  const {
    dailyBusinessHours = [],
    weeklyBusinessHours = [],
    businessHoursMode = BusinessHour.Mode.FULLTIME,
  } = businessHoursSetting;

  businessHoursSetting.dailyBusinessHours = dailyBusinessHours.map(parseTimeRange);
  businessHoursSetting.weeklyBusinessHours = weeklyBusinessHours.map(({ weekdays, timeRanges }) => {
    return BusinessHour.makeWeekTimeRange(weekdays, (timeRanges || []).map(parseTimeRange));
  });
  businessHoursSetting.businessHoursMode = businessHoursMode;

  return {
    mode,
    businessHoursSetting,
    suspendSetting,
  };
}

function parseTimeRange({ startTime, endTime }) {
  const [start, end] = BusinessHour.parseTimeRange(startTime, endTime);
  return BusinessHour.makeTimeRange(start, end);
}

export default CommonSetting;
