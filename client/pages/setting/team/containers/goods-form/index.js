import React, { Component } from 'react';
import pick from 'lodash/pick';
import { Form } from '@zent/compat';
import GoodsSetting from '../goods';
import BlockWrapper from '../../components/block-wrapper';
import { goodsRecommendKeys, settingConfigKeys } from '../../constants';

const { createForm } = Form;

class GoodsForm extends Component {
  render() {
    const {
      hidden,
      model,
      configModel,
      goodsModel,
      zentForm,
      disabled,
      onRefresh,
      registryDisable,
    } = this.props;

    const goodsConfigModel = pick(model, [
      'customer_reviews',
      'goods_recommend',
      'show_shop_btn',
      'show_buy_btn',
      'is_youzan_secured',
      'guarantee_show_style_list',
      'guarantee_show_style_type',
      'subsidy_show_style_list',
      'subsidy_show_style_type',
      'buy_record',
      'qr_code',
      'hide_shopping_cart',
      'goods_recommend_for_you',
      'goods_poster_style',
      'goods_label',
      'open_arrive_time',
      'goods_weapp_card_style',
      'goods_weapp_card_img_rules',
      'show_poster_personal_info',
      'goods_sales_reminder',
      'goods_supply_reminder',
      'show_category_param',
      'goods_activity_tags_preposition',
      'categoryKdtWhite',
      'show_share_title',
      'show_brand_slogan',
      'goods_use_ai_category',
      'open_goods_makeup_order',
      'goods_share_page_style',
      'goods_detail_navigation_bar_style',
      'goods_shop_sign',
      'order_evaluation_expire_days',
      'goods_stock_warning'
    ]);
    const showQrCode = `${(_global.teamStatus || {}).weixinServer}` === '1';
    const extraModel = pick(configModel, goodsRecommendKeys.concat(settingConfigKeys));

    return (
      <BlockWrapper title="商品设置" className="goods-setting" id="goods" hidden={hidden}>
        <Form horizontal>
          <GoodsSetting
            form={zentForm}
            {...goodsConfigModel}
            {...goodsModel}
            {...extraModel}
            showQrCode={showQrCode}
            disabled={disabled}
            registryDisable={registryDisable}
            onRefresh={onRefresh}
          />
        </Form>
      </BlockWrapper>
    );
  }
}

export default createForm({ scrollToError: true })(GoodsForm);
