import React, { Component } from 'react';
import { Form } from '@zent/compat';
import { Radio, Pop, Icon } from 'zent';
import args from '@youzan/utils/url/args';
import get from 'lodash/get';
import pick from 'lodash/pick';
import cx from 'classnames';
import { Loader } from '@youzan/content-loader-react';
import GoodsRecommendField from './field/GoodsRecommendField';
import RecommendForYouField from './field/RecommendForYouField';
import GoodsDetailSalesField from './field/GoodsDetailSalesField';
import GoodsSharePageField from './field/GoodsSharePageField';
import GoodsUmpCountdownField from './field/GoodsUmpCountdownField';
import ShowEvaluationField from './field/ShowEvaluationField';
import BuyBtnConfigField from './BuyBtnConfigField';
import GoodsReminderField from './field/goods-reminder';
import GoodsFavorableRateField from './field/GoodsFavorableRateField';
import GoodsBuyLimitTypeField from './field/goods-buy-limit-type';
import GoodsVideoDanmakuSwitchField from './field/goods-video-danmaku-switch';
import GoodsDetailBuyRecordField from './field/goods-detail-buy-record';
import WelikeEntryField from './field/WelikeEntryField';
import OrderEvaluationExpireField from './field/OrderEvaluationExpireField';
import GoodsSelectionStrategyField from './field/goods-selection-strategy-field';

import {
  isBranchStore,
  isHqStore,
  isSingleStore,
  isPureWscSingleStore,
  isRetailMinimalistHqStore,
  isRetailMinimalistShop,
  isRetailHqStore,
  isRetailSingleStore,
  isChainStore,
  isEduChainStoreV4,
  isEduHqStoreV4,
  isAdvancedVersion,
  isUnifiedChainStoreSolution,
  isUnifiedShop,
  isUnifiedHqStore,
  isWscSingleStore,
  isRetailOnlineBranchStore,
} from '@youzan/utils-shop';
import { checkAbilityValid } from '@youzan/shop-ability';
import GoodsPosterStyleField from './field/GoodsPosterStyleField';
import GoodsTagField from './field/GoodsTagField';
import GoodsStockWarningField from './field/GoodsStockWarningField';
import GoodsDeliveryField from './field/GoodsDeliveryField';
import GoodsWeappCardStyleField from './field/GoodsWeappCardStyleField';
import GoodsCreateSaleStatusField from './field/goods-create-sale-status';
import ShopRankingListHelpDesc from './field/ShopRankingListHelp';
import H5GoodsShareField from './field/H5GoodsShareField';
import GoodsAtmosphereTagsField from './field/goods-atmosphere-tags-field';
import {
  valid,
  validAllChinese,
  validAllEnglish,
  getHashQuery,
  validAllCnEn,
  validStrLength,
} from '../../common/utils';
import {
  isWholesaleScence,
  goodsRecommendKeys,
  goodsReminderKeys,
  isDspShop,
  FROM_GOOD_LIST_OPEN_RECOMMEND,
  showGoodsPoster2,
  COUNTDOWN_SHOW,
  SUPPORT_GOODS_RECOMMEND_FOR_YOU,
  SUPPORT_SETTING_MORE_PRODUCT_RECOMMENDATION,
  SUPPORT_GOODS_SHOP_SIGN,
  GOODS_SHOP_SIGN_VALID,
  IMMERSIVE_MAP,
  GUARANTEE_SHOW_STYLE_LIST_MAP,
  SUBSIDY_SHOW_STYLE_LIST_MAP,
} from '../../constants';
import { ArthurContainer, useArthurModel } from '@youzan/arthur-scheduler-react';

import './style.scss';
import getCategoryRender from './dialog/CategoryRender';
import discountTopTips from './dialog/discounttoptips';
import HotSkuTips from './dialog/hotskutips';
import GuideText from './dialog/GuideDialog';
import MakeupOrderTips from './dialog/makeup-order';

const { isShowH5ItemShare = 1, isShowEstimatedPrice, productMasterDataManageability } = _global;

const goodsFilter = [
  'goods_recommend_goods',
  'goods_recommend_cart',
  'goods_recommend_delivery',
  'goods_recommend_refund',
  '',
];

const baseFilter = ['goods_recommend_free_member', 'goods_recommend_paid_member'];

const { FormRadioGroupField, Field } = Form;

const popContent = (
  <p className="setting-pop-content">
    根据《电商法》要求，经营者应当建立健全信用评价制度，未为消费者提供评价的途径涉嫌违规罚款。为保障商家和消费者权益，此功能下线。
  </p>
);
// 从商品列表页跳转过来，闪烁"为你推荐"
const fromGoodListOpenRecommend =
  isPureWscSingleStore &&
  +getHashQuery('recommend') === 1 &&
  !localStorage.getItem(FROM_GOOD_LIST_OPEN_RECOMMEND);

const maxSaleNum = 999999999;

/* eslint-disable camelcase */
class GoodsForm extends Component {
  state = {
    isShowBrandTip: false,
  };
  componentDidMount() {
    setTimeout(() => {
      if (+args.get('isGoodsLabel', window.location.href)) {
        document.getElementById('goods_label').scrollIntoView(true);
      }
    }, 0);
  }

  renderGuarantee() {
    const { is_youzan_secured, guarantee_show_style_type, guarantee_show_style_list } = this.props;

    if (isDspShop && !_global.inGuaranteeWhiteList) {
      return null;
    }

    return (
      <div>
        {!is_youzan_secured || `${is_youzan_secured}` === '0' ? (
          <Loader
            url="//store.youzan.com/v4/assets/output"
            content="guarantee/join-bar"
            props={{
              fromBiz: 'stroeGeneral',
            }}
            style={{ marginBottom: '24px' }}
          />
        ) : !_global.hideGuarantee ? (
          <FormRadioGroupField
            label="有赞放心购："
            name="guarantee_show_style_type"
            value={guarantee_show_style_type}
            className="danbao-field-content"
          >
            {guarantee_show_style_list.map(id => {
              const currentOption = GUARANTEE_SHOW_STYLE_LIST_MAP[id];
              return (
                <Radio value={id} className="danbao-style-item" key={id}>
                  {currentOption?.img && <img src={currentOption?.img} className="danbao-style" />}
                  {currentOption?.content}
                  {currentOption?.desc && <div className="help-desc">{currentOption?.desc}</div>}
                </Radio>
              );
            })}
            <div className="help-desc">
              样式仅做示例，具体权益展示项受商品类型等因素影响，以商品详情页展示为准。
            </div>
          </FormRadioGroupField>
        ) : null}
      </div>
    );
  }

  renderSubsidy() {
    const { subsidy_show_style_type, subsidy_show_style_list } = this.props;
    return subsidy_show_style_list.length ? (
      <FormRadioGroupField
        label="退货包运费标识："
        name="subsidy_show_style_type"
        value={subsidy_show_style_type}
        className="subsidy-field-content"
      >
        {subsidy_show_style_list.map(id => {
          const currentOption = SUBSIDY_SHOW_STYLE_LIST_MAP[id];
          return (
            <Radio value={id} className="subsidy-style-item" key={id}>
              {currentOption?.content}
              {currentOption?.desc && <div className="help-desc">{currentOption?.desc}</div>}
            </Radio>
          );
        })}
      </FormRadioGroupField>
    ) : null;
  }

  refreshChange(key, e) {
    const props = this.props;
    const value = e.target.value;

    props.onRefresh({ [key]: value });
  }

  isGoodsShareOpenConfig() {
    if (isPureWscSingleStore) {
      return true;
    }
    if (isRetailSingleStore || isUnifiedHqStore) {
      return productMasterDataManageability.abilityStatus === 1;
    }
    return false;
  }
  handleChangeBgColor(value) {
    if (
      value === IMMERSIVE_MAP.immersive &&
      _global?.shopAbilityInfo?.navigation_bar_immersion_ability?.abilityStatus === 2
    ) {
      this.setState({
        isShowBrandTip: true,
      });
      return IMMERSIVE_MAP.normal;
    } else {
      return value;
    }
  }
  handleJumpPage() {
    window.open('https://www.youzan.com/v4/subscribe/pc-order/software/choose#/');
  }
  getShopSignContent() {
    const version = isWscSingleStore ? '2.153.5' : '3.108.6';
    return (
      <>
        <span>
          小程序版本需升级到V{version}以上版本。
          <Pop
            trigger="hover"
            position="bottom-left"
            content={
              <img
                width="490"
                src="https://img01.yzcdn.cn/upload_files/2023/09/19/FpPh9Ecvvf80Az5D_OiCH5WD5h58.png"
              />
            }
          >
            <a className="example-link">查看示例</a>
          </Pop>
        </span>
      </>
    );
  }

  render() {
    const {
      customer_reviews,
      // 评价排序
      goods_evaluation_sort_for_pdp,
      show_shop_btn,
      show_buy_btn,
      buy_btn_config,
      qr_code,
      showQrCode,
      goods_recommend,
      goods_recommend_for_you,
      goods_ranking_list,
      form,
      disabled,
      goods_detail_buy_record,
      goods_detail_sales,
      hide_shopping_cart,
      onRefresh,
      evaluation_member_label,
      goods_poster_style,
      goods_label,
      open_arrive_time,
      goods_weapp_card_style,
      goods_weapp_card_img_rules,
      goods_ump_countdown,
      show_poster_personal_info,
      goods_share_page_style,
      hasBaseAbility,
      hasGoodsAbility,
      registryDisable,
      goods_favorable_rate,
      goods_buy_limit_count_type,
      goods_video_danmaku_switch,
      goods_detail_buy_record_min,
      goodsDefaultDisplayConfig,
      show_category_param,
      categoryKdtWhite,
      goods_activity_tags_preposition,
      show_share_title,
      show_brand_slogan,
      goods_expect_price_show,
      show_hot_sku,
      h5_item_share_title,
      h5_item_share_desc,
      welike_entry_switch_config,
      goods_use_ai_category,
      shop_evaluation,
      open_goods_makeup_order,
      evaluate_tags_switch,
      goods_shop_sign,
      goods_detail_navigation_bar_style,
      order_evaluation_expire_days,
      show_item_label,
      default_sku_selected_rule,
      goods_stock_warning,
    } = this.props;
    const { isShowBrandTip } = this.state;
    const recommondKeys = pick(this.props, goodsRecommendKeys);
    if (!hasBaseAbility) {
      baseFilter.map(key => (recommondKeys[key] = '0'));
    }
    if (!hasGoodsAbility) {
      goodsFilter.map(key => (recommondKeys[key] = '0'));
    }
    const reminderKeys = pick(this.props, goodsReminderKeys);

    const formValue = get(form, 'getFormValues', () => {})();
    const {
      customer_reviews: formCustomerReviews = 1,
      evaluation_member_label: formEvaluationMemberLabel,
      goods_evaluation_sort_for_pdp: formGoodsEvaluationSortForPdp = 0,
      evaluate_tags_switch: formEvaluateTagsSwitch = 1,
    } = formValue;

    // 商品评价选中的值
    // todo: 需要后端刷数据，但是后端业务边界在撕扯，前端先处理，值为2的时候，当作0处理
    const customerReviewsValue = +customer_reviews === 2 ? 0 : customer_reviews;
    const buyBtnDisabled = disabled || (hide_shopping_cart === 0 && show_buy_btn === '1');
    const configDisabled = disabled;

    // 商品评价提示文案
    let customerReviewsNoticeText = '全网评价将在商品详情页对买家进行展示';
    if (+formCustomerReviews === 0)
      customerReviewsNoticeText = '仅隐藏商品详情页的评价展示，买家依然可以对订单进行评价';
    if (isWholesaleScence) {
      customerReviewsNoticeText = '批发商品暂不支持评价';
    }

    let evaluationMemberLabelText = '展示评价时将会展示出评价者的会员等级';
    if (formEvaluationMemberLabel === 1) {
      evaluationMemberLabelText = '展示评价时将会展示出评价者的会员名称';
    }
    // 商品评价排序提示文案
    const goodsEvaluationSortText =
      +formGoodsEvaluationSortForPdp === 0
        ? '系统按照多维度权重排序，无价值评价将会下沉'
        : '按照评价产生的时间先后顺序排序，评价越新，排序越前';
    const evaluateTagsSwitchText =
      +formEvaluateTagsSwitch === 1 ? (
        <span>
          开启后，评价发布页将会根据商品的类目推荐评价关键词，帮助买家更好的发布评价。
          <Pop
            trigger="hover"
            className="evaluate-tags-switch-pop"
            position="bottom-left"
            content={
              <img
                width="254"
                src="https://img01.yzcdn.cn/upload_files/2023/03/08/FhTqZE_Ji7ykApe12uaBSobQLI3V.png"
              />
            }
          >
            <a className="example-link">查看示例</a>
          </Pop>
        </span>
      ) : (
        '关闭后，评价发布页将不会展示推荐关键词'
      );

    registryDisable(
      [
        'goods_recommend_for_you',
        'goods_ranking_list',
        'customer_reviews',
        'evaluation_member_label',
        'show_shop_btn',
        'goods_detail_sales',
        'goods_detail_buy_record',
        'qr_code',
        'goods_poster_style',
        'goods_label',
        'goods_stock_warning',
        'open_arrive_time',
        'show_poster_personal_info',
        'goods_weapp_card_style',
        'goods_weapp_card_img_rules',
        'goods_evaluation_sort_for_pdp',
      ],
      disabled
    );
    if (!disabled) {
      registryDisable('evaluation_member_label', isBranchStore);
    }
    return (
      <div>
        {SUPPORT_GOODS_RECOMMEND_FOR_YOU && (
          <Field
            label="为你推荐："
            name="goods_recommend_for_you"
            form={form}
            disabled={disabled}
            originStat={goods_recommend_for_you}
            component={RecommendForYouField}
            value={goods_recommend_for_you}
            flag={fromGoodListOpenRecommend}
            className={cx({ goods_recommend_blink: fromGoodListOpenRecommend })}
          />
        )}
        {!!_global.isInGoodsRankingList && (
          <FormRadioGroupField
            label="商品榜单信息："
            name="goods_ranking_list"
            value={goods_ranking_list}
            helpDesc={<ShopRankingListHelpDesc />}
            disabled={disabled}
          >
            <Radio value="1">展示</Radio>
            <Radio value="0">不展示</Radio>
          </FormRadioGroupField>
        )}
        <ArthurContainer name="goodsSetting">
          <FormRadioGroupField
            label="商品评价："
            name="customer_reviews"
            value={customerReviewsValue}
            helpDesc={customerReviewsNoticeText}
            disabled={disabled}
          >
            <Radio value={1}>展示全网评价</Radio>
            <Radio value={0}>隐藏全网评价</Radio>
            <Radio value={2} disabled className="disabled-radio">
              隐藏全网评价及关闭买家评价（已下线）
              <Pop trigger="hover" content={popContent}>
                <Icon className="setting-help-icon" type="help-circle" />
              </Pop>
            </Radio>
          </FormRadioGroupField>

          {/* 开启了全网评价、显示好评率 */}
          {!!formCustomerReviews && (
            <Field
              name="goods_favorable_rate"
              component={GoodsFavorableRateField}
              disabled={disabled}
              value={goods_favorable_rate}
              helpDesc="仅展示评价时，好评率逻辑才生效，不设置好评率数据，不影响评价展示。"
              validations={{
                valueValid(values, value) {
                  const { rate } = value;
                  return rate !== '' && rate >= 0 && rate <= 100;
                },
              }}
              validationErrors={{
                valueValid: '请填写 0-100 之间的数字',
              }}
            />
          )}
          {/* OrderEvaluationExpireField */}
          <Field
            label="商品可评价时间： "
            name="order_evaluation_expire_days"
            component={OrderEvaluationExpireField}
            value={order_evaluation_expire_days}
          />
          {(isPureWscSingleStore || isRetailOnlineBranchStore || isRetailSingleStore) && (
            <Field
              label="店铺评价： "
              name="shop_evaluation"
              component={ShowEvaluationField}
              value={shop_evaluation}
            />
          )}
          {/* 开启了全网评价 */}
          {!!formCustomerReviews && (
            <FormRadioGroupField
              label="评价会员标签展示："
              name="evaluation_member_label"
              value={+evaluation_member_label}
              helpDesc={evaluationMemberLabelText}
              disabled={disabled || isBranchStore}
            >
              <Radio value={0}>评价展示会员等级</Radio>
              <Radio value={1}>评价展示会员名称</Radio>
            </FormRadioGroupField>
          )}
          {/* 开启了全网评价 */}
          {!!formCustomerReviews && (
            <FormRadioGroupField
              label="评价排序："
              name="goods_evaluation_sort_for_pdp"
              value={+goods_evaluation_sort_for_pdp}
              helpDesc={goodsEvaluationSortText}
              disabled={disabled}
            >
              <Radio value={0}>智能推荐排序</Radio>
              <Radio value={1}>评价产生时间排序</Radio>
            </FormRadioGroupField>
          )}
          {isPureWscSingleStore && (
            <FormRadioGroupField
              label="猜你想说："
              name="evaluate_tags_switch"
              value={+evaluate_tags_switch}
              helpDesc={evaluateTagsSwitchText}
              disabled={disabled}
            >
              <Radio value={1}>开启</Radio>
              <Radio value={0}>关闭</Radio>
            </FormRadioGroupField>
          )}
        </ArthurContainer>
        {SUPPORT_SETTING_MORE_PRODUCT_RECOMMENDATION && (
          <Field
            label="更多商品推荐："
            name="goodsRecommends"
            component={GoodsRecommendField}
            registryDisable={registryDisable}
            value={{
              goods_recommend,
              ...recommondKeys,
            }}
            validations={{
              goodsRecommendValid(values, value) {
                if (
                  value.goods_recommend === 1 &&
                  goodsRecommendKeys.filter(field => value[field] === '1').length === 0
                ) {
                  return false;
                }
                return true;
              },
            }}
            validationErrors={{
              goodsRecommendValid: '请选择商品推荐的展示页面',
            }}
          />
        )}
        <FormRadioGroupField
          label="商详页店铺按钮："
          name="show_shop_btn"
          value={show_shop_btn}
          helpDesc="展示后，买家可以点击该按钮快速回到店铺主页。"
          disabled={disabled}
        >
          <Radio value="1">展示</Radio>
          <Radio value="0">不展示</Radio>
        </FormRadioGroupField>
        <ArthurContainer name="goodsSetting">
          <Field
            label="商详页立即购买："
            name="buy_btn_config"
            registryDisable={registryDisable}
            component={BuyBtnConfigField}
            disabled={buyBtnDisabled}
            configDisabled={configDisabled}
            value={{
              show_buy_btn,
              buy_btn_config,
            }}
            onRefresh={onRefresh}
            validations={{
              requiredValid(values, value) {
                const { show_buy_btn: show, buy_btn_config: config } = value;
                return valid({ model: show, config }, !config.label);
              },
              // goodsBuyBtnValid(values, value) {
              //   const { show_buy_btn: show, buy_btn_config: config } = value;
              //   const chinese = validAllChinese(config.label);
              //   const english = validAllEnglish(config.label);
              //   const label_length = config.label.length;
              //   if (english) {
              //     return valid({ model: show, config }, label_length > 12);
              //   } else if (chinese) {
              //     return valid({ model: show, config }, label_length > 6);
              //   } else {
              //     return valid({ model: show, config }, false);
              //   }
              // },
              // chineseValid(values, value) {
              //   const { show_buy_btn: show, buy_btn_config: config } = value;
              //   const chinese = validAllChinese(config.label);
              //   const english = validAllEnglish(config.label);
              //   if (chinese) {
              //     return valid({ model: show, config }, !chinese);
              //   } else if (english) {
              //     return valid({ model: show, config }, !english);
              //   } else {
              //     return valid({ model: show, config }, true);
              //   }
              // },
              goodsBuyBtnValidEnglish(values, value) {
                const { show_buy_btn: show, buy_btn_config: config } = value;
                const english = validAllEnglish(config.label);
                const label_length = config.label.length;
                if (english) {
                  return valid({ model: show, config }, label_length > 12);
                } else {
                  return valid({ model: show, config }, false);
                }
              },
              goodsBuyBtnValidChinese(values, value) {
                const { show_buy_btn: show, buy_btn_config: config } = value;
                const chinese = validAllChinese(config.label);
                const label_length = config.label.length;
                if (chinese) {
                  return valid({ model: show, config }, label_length > 6);
                } else {
                  return valid({ model: show, config }, false);
                }
              },
              chineseEnglishMashup(values, value) {
                const { show_buy_btn: show, buy_btn_config: config } = value;
                const labelLength = validStrLength(config.label);
                const label = config.label;
                if (show === '1' && config.default === 1) {
                  return validAllCnEn(label) && labelLength <= 12;
                }
                return true;
              },
            }}
            validationErrors={{
              requiredValid: '请输入自定义名称',
              goodsBuyBtnValid: '最多可输入6个中文字或12个英文字',
              chineseValid: '仅支持中文或英文',
              chineseEnglishMashup:
                '最多不能超过12个字符（1个汉字=2个字符），支持空格，不支持其他标点符号',
              goodsBuyBtnValidEnglish: '最多可输入12个字母，支持空格，不支持其他标点符号',
              goodsBuyBtnValidChinese: '最多可输入6个中文字，支持空格，不支持其他标点符号',
            }}
          />
          {this.renderGuarantee()}
          {this.renderSubsidy()}
          {!isSingleStore && (
            <GoodsBuyLimitTypeField
              label="限购计数规则："
              name="goods_buy_limit_count_type"
              value={goods_buy_limit_count_type}
              disabled={!isHqStore || disabled}
            />
          )}
          <div id="detail-sales">
            <Field
              label="商详页销量："
              name="goods_detail_sales"
              component={GoodsDetailSalesField}
              disabled={disabled || (isChainStore && isBranchStore)}
              value={goods_detail_sales}
              validations={{
                detailSalesValid(values, value) {
                  const { show, limit, limit_num: num } = value;
                  return !(+show === 1 && limit && +num <= 0);
                },
                numberValid(values, value) {
                  const { show, limit, limit_num: num } = value;
                  return !(+show === 1 && limit && !/^[0-9]*$/.test(num));
                },
                numberLimit(values, value) {
                  const { show, limit, limit_num: num } = value;
                  return !(+show === 1 && limit && (+num <= 0 || +num > maxSaleNum));
                },
              }}
              validationErrors={{
                detailSalesValid: '请填写展示销量限制件数',
                numberValid: '展示销量限制件数仅限数字',
                numberLimit: `请填写 1-${maxSaleNum} 之间的数字`,
              }}
            />
          </div>
          {!!_global.isInGoodsVideoDanmakuAllowList && (
            <GoodsVideoDanmakuSwitchField
              label="商详页展示弹幕："
              name="goods_video_danmaku_switch"
              value={goods_video_danmaku_switch}
              disabled={disabled}
            />
          )}

          <GoodsDetailBuyRecordField
            label="商详页购买记录："
            name="goods_detail_buy_record"
            value={goods_detail_buy_record}
            disabled={disabled}
            goodsDetailBuyRecordMin={goods_detail_buy_record_min}
            form={form}
          />

          {showQrCode && (
            <FormRadioGroupField
              label="电脑版商品详情："
              name="qr_code"
              value={qr_code}
              disabled={disabled}
            >
              <Radio value={0}>显示“关注后购买”二维码</Radio>
              <Radio value={1}>显示“直接购买”二维码</Radio>
            </FormRadioGroupField>
          )}
          {showGoodsPoster2 && (
            <div id="poster-card">
              <GoodsPosterStyleField value={goods_poster_style} disabled={disabled} />
            </div>
          )}
          <FormRadioGroupField
            label="商品海报分享人："
            name="show_poster_personal_info"
            value={show_poster_personal_info}
            helpDesc={
              <>
                该设置仅对系统海报生效，自定义海报不受影响。
                <br />
                微信小程序版本需升级至v2.55以上。
              </>
            }
            disabled={disabled}
          >
            <Radio value="1">展示</Radio>
            <Radio value="0">不展示</Radio>
          </FormRadioGroupField>
          {this.isGoodsShareOpenConfig() && (
            <GoodsSharePageField disabled={disabled} value={goods_share_page_style} />
          )}
          <GoodsWeappCardStyleField
            value={{
              goods_weapp_card_style,
              goods_weapp_card_img_rules,
              show_share_title,
              show_brand_slogan,
            }}
            disabled={disabled}
          />
          {!!+isShowH5ItemShare && (
            <div id="h5-sharecard">
              <H5GoodsShareField
                value={{ h5_item_share_title, h5_item_share_desc }}
                disabled={disabled}
              />
            </div>
          )}
          <div id="goods-reminders">
            <GoodsReminderField
              label="商品提醒："
              name="goodsReminders"
              registryDisable={registryDisable}
              value={{ ...reminderKeys }}
              disabled={disabled}
            />
          </div>
          <FormRadioGroupField
            label="商品发布智能类目："
            name="goods_use_ai_category"
            value={goods_use_ai_category}
            helpDesc="开启自动填写后，如通过商品图片和标题识别出商品类目则自动填写。未开启则仅做推荐。"
            disabled={disabled}
          >
            <Radio value="0">自动填写</Radio>
            <Radio value="1">不自动填写，仅做推荐</Radio>
          </FormRadioGroupField>
          <FormRadioGroupField
            label="商品详情页面展示类目参数："
            name="show_category_param"
            value={show_category_param}
            helpDesc={getCategoryRender(form, categoryKdtWhite)}
            disabled={disabled || isBranchStore}
          >
            <Radio value="1">展示</Radio>
            <Radio value="0">不展示</Radio>
          </FormRadioGroupField>
          {isShowEstimatedPrice && (
            <FormRadioGroupField
              label="商品预估价："
              name="goods_expect_price_show"
              className="goods-expect-price-field"
              value={goods_expect_price_show}
              helpDesc={
                <div style={{ display: 'inline-flex' }}>
                  <GuideText
                    image="https://img01.yzcdn.cn/upload_files/2021/08/23/Fln435qG0Clnt0jRwtJ6_e_8IIlz.png"
                    ext={
                      <a
                        style={{ marginLeft: 4 }}
                        target="_blank"
                        href="https://bbs.youzan.com/forum.php?mod=viewthread&tid=683287"
                        rel="noreferrer"
                      >
                        预估到手价规则
                      </a>
                    }
                  >
                    开启后商品预估价（或购买多件时预估单价）展示在“商品详情页”、“购物车”，放大价格差异刺激消费者下单。
                  </GuideText>
                </div>
              }
              disabled={disabled || isBranchStore}
            >
              <Radio value="1">展示</Radio>
              <Radio value="0">不展示</Radio>
            </FormRadioGroupField>
          )}
          {isPureWscSingleStore && (
            <Field
              label="大家喜欢入口："
              name="welike_entry_switch_config"
              component={WelikeEntryField}
              registryDisable={registryDisable}
              value={welike_entry_switch_config}
              validations={{
                welikeEntryValid(values, value) {
                  if (value.enable && value.pageList.length === 0) {
                    return '请选择大家喜欢入口的展示页面';
                  }
                  return true;
                },
              }}
            />
          )}
          {isPureWscSingleStore && (
            <FormRadioGroupField
              label="商详加购后推荐："
              name="open_goods_makeup_order"
              value={open_goods_makeup_order}
              helpDesc={<MakeupOrderTips />}
              disabled={disabled}
            >
              <Radio value="1">开启</Radio>
              <Radio value="0">关闭</Radio>
            </FormRadioGroupField>
          )}
          {(isRetailMinimalistHqStore || isEduHqStoreV4) && (
            <GoodsCreateSaleStatusField
              label="新建商品初始状态："
              name="goodsDefaultDisplayConfig"
              value={goodsDefaultDisplayConfig}
              disabled={disabled}
              form={form}
            />
          )}
          {!(isRetailMinimalistShop || isEduChainStoreV4) &&
            (isRetailHqStore || isRetailSingleStore) && (
              <div id="goods_label">
                <GoodsTagField value={goods_label} disabled={disabled} />
              </div>
            )}

              <div id="goods_stock_warning">
                <Field
                  label="商品库存紧张配置："
                  name="goods_stock_warning"
                  component={GoodsStockWarningField}
                  disabled={disabled}
                  value={goods_stock_warning}
                  validations={{
                    thresholdValid(values, value) {
                      const { enabled, threshold } = value || {};
                      if (enabled === '1' && (!threshold || threshold <= 0)) {
                        return false;
                      }
                      return true;
                    },
                    numberValid(values, value) {
                      const { enabled, threshold } = value || {};
                      if (enabled === '1' && threshold && !/^[0-9]+$/.test(threshold)) {
                        return false;
                      }
                      return true;
                    },
                  }}
                  validationErrors={{
                    thresholdValid: '开启时剩余库存必填且必须大于0',
                    numberValid: '剩余库存只能输入数字',
                  }}
                />
              </div>
          {(isUnifiedShop || isRetailSingleStore) && (
            <div>
              <GoodsDeliveryField value={open_arrive_time} disabled={disabled || isBranchStore} />
            </div>
          )}
          {SUPPORT_GOODS_SHOP_SIGN ? (
            <FormRadioGroupField
              label="商详页店招信息："
              name="goods_shop_sign"
              value={GOODS_SHOP_SIGN_VALID ? goods_shop_sign : '1'}
              helpDesc={this.getShopSignContent()}
              disabled={disabled || isBranchStore || !GOODS_SHOP_SIGN_VALID}
            >
              <Radio value="1">展示</Radio>
              <Radio value="0">隐藏</Radio>
            </FormRadioGroupField>
          ) : null}
          {checkAbilityValid('navigation_bar_immersion_ability') &&
            (isSingleStore || isHqStore) &&
            !!_global.inGoodsDetailNavigationBarStyle && (
              <div
                style={{
                  position: 'relative',
                }}
              >
                <FormRadioGroupField
                  label="商品详情顶部导航效果："
                  name="goods_detail_navigation_bar_style"
                  value={goods_detail_navigation_bar_style}
                  disabled={disabled}
                  normalize={this.handleChangeBgColor.bind(this)}
                >
                  <Radio value="1">沉浸式</Radio>
                  <Radio value="0">普通</Radio>
                </FormRadioGroupField>
                {isShowBrandTip && (
                  <div className="brand-tip-container">
                    <div className="brand-tip-contianer-text">
                      升级店铺版本后，解锁沉浸式顶部导
                      <br />
                      航效果
                    </div>
                    <div
                      className="brand-tip-contianer-button"
                      onClick={() => this.handleJumpPage()}
                    >
                      了解更多
                    </div>
                  </div>
                )}
              </div>
            )}
          {/* 活动倒计时 */}
          <Field
            label="商品详情页展示活动倒计时："
            name="goods_ump_countdown"
            component={GoodsUmpCountdownField}
            disabled={disabled}
            value={goods_ump_countdown}
            validations={{
              limitHoursValid(values, value) {
                const { show, limit_hours: hours } = value;
                return !(+show === COUNTDOWN_SHOW.on && +hours <= 0);
              },
              limitMaxHoursValid(values, value) {
                const { show, limit_hours: hours } = value;
                return !(+show === COUNTDOWN_SHOW.on && +hours >= 24);
              },
              numberValid(values, value) {
                const { show, limit_hours: hours } = value;
                return !(+show === COUNTDOWN_SHOW.on && !/^[0-9]*$/.test(hours));
              },
            }}
            validationErrors={{
              limitHoursValid: '请填写大于0的倒计时小时数',
              limitMaxHoursValid: '输入值最大不超过24小时',
              numberValid: '活动倒计时仅限数字',
            }}
          />
          {
            <FormRadioGroupField
              label="领券促销入口："
              name="goods_activity_tags_preposition"
              value={goods_activity_tags_preposition}
              helpDesc={discountTopTips()}
              disabled={disabled}
            >
              <Radio value="1">前置展示</Radio>
              <Radio value="0">不前置展示</Radio>
            </FormRadioGroupField>
          }
          {(!isAdvancedVersion || !isUnifiedChainStoreSolution) && (
            <FormRadioGroupField
              label="商品规格标签："
              name="show_hot_sku"
              value={show_hot_sku}
              helpDesc={<HotSkuTips />}
              disabled={disabled || isBranchStore}
            >
              <Radio value="1">展示热销标签</Radio>
              <Radio value="0">不展示热销标签</Radio>
            </FormRadioGroupField>
          )}
          {(isRetailSingleStore || isHqStore || isPureWscSingleStore) && (
            <Field
              label="商品多规格选中策略："
              name="default_sku_selected_rule"
              component={GoodsSelectionStrategyField}
              disabled={disabled}
              value={default_sku_selected_rule}
            />
          )}
          <div>
            <GoodsAtmosphereTagsField value={show_item_label} />
          </div>
        </ArthurContainer>
      </div>
    );
  }
}
/* eslint-enable camelcase */

const GoodsFormWrapper = props => {
  const { model: goodsModel } = useArthurModel('goodsSetting');
  const { model: baseModel } = useArthurModel('baseSetting');
  return (
    <GoodsForm
      {...props}
      hasBaseAbility={baseModel ? baseModel.available : true}
      hasGoodsAbility={goodsModel ? goodsModel.available : true}
    />
  );
};

export default GoodsFormWrapper;
