# 商品库存紧张配置功能

## 功能描述

新增了"商品库存紧张配置"功能，允许商家设置当商品库存低于指定数量时显示"库存紧张"提示。

## 实现文件

### 主要组件文件
- `GoodsStockWarningField.js` - 主要的配置组件
- `GoodsStockWarningDialog.js` - 示例对话框组件
- `GoodsStockWarningField.spec.js` - 单元测试文件

### 修改的文件
- `index.js` - 主配置页面，添加了新的配置项
- `style.scss` - 添加了相关样式

## 功能特性

### 1. 开关配置
- 支持开启/关闭商品库存紧张配置
- 默认状态：关闭
- 默认阈值：0

### 2. 阈值设置
- 当开启配置时，显示"剩余库存小于 [输入框] 件显示"
- 输入框只能输入数字
- 当开启时，阈值为必填项且必须大于0
- 当关闭时，自动重置阈值为0

### 3. 表单验证
- **thresholdValid**: 开启时剩余库存必填且必须大于0
- **numberValid**: 剩余库存只能输入数字

### 4. 显示条件
只在以下条件下显示此配置项：
```javascript
!(isRetailMinimalistShop || isEduChainStoreV4) &&
(isRetailHqStore || isRetailSingleStore)
```

## 数据结构

```javascript
goods_stock_warning: {
  enabled: '0' | '1',  // 开启状态：'0'关闭，'1'开启
  threshold: number    // 库存阈值
}
```

## 使用示例

```javascript
// 关闭状态
{
  enabled: '0',
  threshold: 0
}

// 开启状态，库存小于10件时显示
{
  enabled: '1',
  threshold: 10
}
```

## UI展示

- 标签：商品库存紧张配置
- 选项：开启 / 关闭
- 当开启时显示：剩余库存小于 [10] 件显示
- 帮助文本：开启后，当商品剩余库存低于设定值，则会在商品下方显示"库存紧张"。查看示例

## 样式类名

- `.stock-warning-threshold` - 阈值输入区域容器
- `.threshold-input` - 阈值输入框
- `.no-error` - 无错误状态的样式类

## 注意事项

1. 组件使用了 `Form.getControlGroup` 包装，确保与表单系统正确集成
2. 支持禁用状态，当整个表单被禁用时，此组件也会被禁用
3. 包含完整的错误处理和验证逻辑
4. 支持查看示例功能（需要配置示例图片）
