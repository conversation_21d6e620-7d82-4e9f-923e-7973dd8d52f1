$continer-height: 480px;
$left-image-width: 280px;

.example-dialog__container {
  &.countdown {
    width: 203px;
    height: 344px;

    .content-wrap {
      margin: 0;

      .content-image {
        margin: 0;

        img {
          width: 100%;
          height: auto;
          margin-top: 14px;
        }
      }
    }
  }

  .content-wrap {
    display: flex;
    margin: 11px 12px;

    .content-image {
      width: $left-image-width;
      height: $continer-height;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.goods_recommend_blink {
  animation: blink 1s 2 steps(1);
}

@keyframes blink {
  50% {
    opacity: 0;
  }
}

.recommend-for-you-pop {
  .select-reason,
  .custom-reason {
    display: flex;
  }

  .select-reason {
    .select-reason-checkbox {
      margin-bottom: 10px;
    }
  }

  .custom-reason {
    // height: 120px;

    &-input {
      flex: 1;

      textarea {
        height: 100%;
      }
    }
  }

  .pop-error-desc {
    color: var(--theme-error-4, #df4545);
    padding-left: 70px;
  }

  .operate-area {
    float: right;
    margin: 15px 0;
  }
}

.poster-help {
  font-size: 12px;
  line-height: 18px;
}

.goods-trade-marquee {
  margin-top: 5px;

  .checkboxes {
    display: block;
    margin-top: 15px;
  }

  .zent-checkbox-wrap {
    margin-right: 24px;
  }

  .zent-form__help-desc {
    margin-top: 8px;
  }
}

.goods-trade-marquee-help-field-pop {
  .pop-content {
    // width: 422px;
    // height: 364px;
    margin: 16px;

    img {
      width: 204px;
      height: 282px;
    }
  }

  .zent-pop-v2-inner {
    padding: 0 !important;
  }
}

.goods-trade-marquee-help-field {
  .demo-text {
    margin-left: 4px;
  }

  .weapp-notice {
    margin-top: 8px;
    line-height: 1.4;
  }
}

.shop-ranking-list {
  .zent-pop-v2-inner {
    padding: 16px !important;
    width: 400px;

    .content-wrap {
      display: flex;
      align-content: center;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: space-around;

      .text {
        font-size: 13px;
        color: #323233;
        margin-bottom: 12px;
      }

      img {
        width: 200px;
      }

      .left-img {
        margin-right: 12px;
      }
    }
  }
}

.goods-poster {
  &-field {
    &__option {
      margin-right: 15px;
    }

    .zent-pop-v2-wrapper {
      vertical-align: middle;
    }
  }

  &-pop {
    margin-left: -7px;
  }

  &-demo {
    box-sizing: border-box;
    width: 264px;
    padding: 12px;
    text-align: center;

    &__img {
      display: block;
      width: 100%;
      margin: 0 auto;
    }

    &__desc {
      margin-top: 12px;
      font-size: 12px;
      color: #969799;
      line-height: 18px;
    }
  }
}

.link {
  margin: 5px;
}

.stock-warning-threshold {
  margin: 8px 0;
  display: flex;
  align-items: center;

  .threshold-input {
    text-align: center;
  }
}
