import React from 'react';
import { Radio, Input, Dialog } from 'zent';
import { Form } from '@zent/compat';
import assign from 'lodash/assign';
import cx from 'classnames';

import GoodsStockWarningDialog from './dialog/GoodsStockWarningDialog';
import './style.scss';

const { RadioGroup } = Radio;
const { openDialog, closeDialog } = Dialog;
const dialogId = 'GOODS_STOCK_WARNING';

const showDialog = () => {
  openDialog({
    dialogId,
    title: '查看示例',
    children: <GoodsStockWarningDialog />,
    className: 'goods-stock-warning-dialog',
    onClose() {
      closeDialog(dialogId);
    },
  });
};

const GoodsStockWarningField = props => {
  const { value, disabled, onChange, validationErrors, errors } = props;
  const { enabled = '0', threshold = 0 } = value || {};

  const handleRadioChange = e => {
    const newEnabled = e.target.value;
    const newValue = assign({}, value, {
      enabled: newEnabled,
      // 当关闭时，重置阈值为0
      threshold: newEnabled === '0' ? 0 : threshold,
    });
    onChange(newValue, { merge: true });
  };

  const handleThresholdChange = e => {
    const newThreshold = e.target.value;
    const newValue = assign({}, value, {
      threshold: newThreshold,
    });
    onChange(newValue, { merge: true });
  };

  const hasError = key => {
    return errors && validationErrors && errors.includes(validationErrors[key]);
  };

  return (
    <div>
      <RadioGroup
        value={enabled}
        onChange={handleRadioChange}
        disabled={disabled}
      >
        <Radio value="1">开启</Radio>
        <Radio value="0">关闭</Radio>
      </RadioGroup>

      {enabled === '1' && (
        <div className="stock-warning-threshold">
          <span>剩余库存小于</span>
          <Input
            value={threshold}
            className={cx('threshold-input', {
              'no-error': !hasError('thresholdValid') && !hasError('numberValid'),
            })}
            style={{ width: '80px', margin: '0 8px' }}
            disabled={disabled}
            onChange={handleThresholdChange}
            placeholder="0"
          />
          <span>件显示</span>
        </div>
      )}

      <p className="zent-form__help-desc" style={{ margin: '8px 0' }}>
        开启后，当商品剩余库存低于设定值，则会在商品下方显示"库存紧张"。
        <a href="javascript:;" className="example-link" onClick={showDialog}>
          查看示例
        </a>
      </p>
    </div>
  );
};

export default Form.getControlGroup(GoodsStockWarningField);
