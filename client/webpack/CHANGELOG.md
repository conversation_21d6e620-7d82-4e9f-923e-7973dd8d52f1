## release-2.6.0

* `prod` 模式增加上传 sourcemap 到内网存储系统，可以给像 sentry 这类的系统排查问题使用
* npm 白名单回滚 `p-limit`，原因是这个包的代码没有做 ES6 转码，会导致打包失败；替代方案是使用 `@youzan/utils` 里面的实现
* `es-guard` 独立发包，替换了使用的地方，功能无变化

## release-2.5.2

* 增加代码中又拍云链接校验
* npm 白名单增加 `p-limit`

## release-2.5.1

* 升级 `webpack` 到 `4.43.0`

## release-2.5.0

* 支持 Jest 单测

## release-2.4.0
* 打包增加 http CDN 资源检查
* 升级 superman-cdn，上传前会检查打包结果中是否含有 http CDN 资源

## release-2.3.1

* 允许安装 `lodash-es`，配合 TypeScript 使用

## release-2.3.0

* js/jsx 支持 optional chaining 和 nullish-coalescing-operator
* 关闭 `@babel/preset-env` 的 `modules` 参数

## release-2.2.2

* 处理 `@youzan/retail-form` 和 `@youzan/retail-components` 之间的 `peerDependencies` 依赖关系

## release-2.2.1

* 升级 `babel-plugin-zent`，兼容 zent 7 正式版

## release-2.2.0

* prod 模式支持指定页面打包，这个功能可以方便对页面大小做分析，参数同 `dev` 命令

## release-2.1.0

* 增加 `big.js` 白名单
* 增加页面名称检查，屏蔽广告相关关键字作为页面名称

## release-2.0.1

* 修复在 `.ts` 文件中 `<string>foobar` 解析错误的问题
* 增加跨页面引用代码的检查

## release-2.0.0(shared 要求版本 >= 2.0.0)

__[2.0.0 升级指南](https://doc.qima-inc.com/pages/viewpage.action?pageId=238242175)__

Breaking changes:
* 最低 `node` 版本升级到 10
* `.scss` 文件不再支持 CSS Modules，这个我们从来没有“官方支持”过，以前能用是 `css-loader` 的 bug，详见 https://doc.qima-inc.com/pages/viewpage.action?pageId=236596110
* packages.json 的 `dependency` 内的包不再自动打到 dll，现在 vendor 和 base 的默认内容是固定的；同时打包时候会输出 vendor 和 base 包含的内容
* 删除 `postcss-loader`，不再支持任何形式的自定义 `postcss` 插件
* 更加友好的 HMR 支持，包括 react hooks 支持，但是 zan-proxy 的配置需要更新
* prod 模式删除了 `CaseSensitivePathsPlugin`，这个插件性能损耗很大
* 使用 `dart-sass` 替换 `node-sass`，`dart-sass` 是官方的参考实现，更新迭代最及时而且不再有 `node-sass` 的二进制包安装问题，一些已知问题 https://doc.qima-inc.com/pages/viewpage.action?pageId=236597097
* CSS 处理流程修改为先 extract 然后再做压缩，之前是单文件做压缩然后 extract 到一个文件中。导致的结果是无缓存情况下打包会快一些，有缓存情况下打包会慢一些，前者影响大一些。
* 禁用 base64 图片，https://doc.qima-inc.com/pages/viewpage.action?pageId=237124991
* 打包输出文件内如果有 ES6 语法打包会中止

新功能:
* dev 模式下检测到 webpack 无法从错误中恢复的时候会提供一个选项是否重启 webpack，不必 CTRL+C 再手动重启了
* dev server 端口随机选取，不再固定，可以同时调试多个项目
* 增加了 `react-hot-loader` 的版本检测功能
* 不同版本的缓存会分开存放，避免不必要的缓存刷新问题

改进:
* JavaScript 使用 babel-loader 内置的缓存，不再依赖 `cache-loader`
* CSS 热更新直接使用 `mini-css-extract-plugin` 内置的能力，不再依赖 `css-hot-loader`
* 修复了在 fastbuild 平台打包时缓存失效导致打包时间变长的问题
* 升级所有依赖到最新版
* 缓存不再存储在项目目录内，fastbuild 每次打包前会清洗 git 仓库，删除所有不是 git 管理的文件，默认缓存目录：`~/.fe-build-cache`，同时兼容打包平台的 `FASTBUILD_CACHE_DIR` 环境变量。

## release-1.7.7

* 升级 `@youzan/superman-cdn`

## release-1.7.6

* 修复了 `@babel/runtime` 和 `tslib` 被打进自定义 DLL 的问题

## release-1.7.5

* 这个版本有问题，不要使用

## release-1.7.4

* 包白名单增加 `html-entities`
* 修复某些文件打包时找不到 `react-hot-loader` 的问题

## release-1.7.3

* 增加 `shared/decorate-components` 以及 `pages` 两个 `alias`

## release-1.7.2

* 修复 chunk 名字过长导致打包失败的问题

## release-1.7.1

* 打包增加 30 分钟超时（Dll和App各 30 分钟），主要是为了处理极端情况进程一直不退出的问题
* 不再将 `client/node_modules` 放到模块查找的第一位

## release-1.7.0

* 修复数组形式的 entry 导致 module id 自增的问题
* 增加 `NamedChunksPlugin` 稳定 chunk id

## release-1.6.0

* 包白名单增加：html2canvas, jspdf 以及 d3 相关的包
* 支持通过 `overrideBasePackages` 以及 `overrideVendorPackages` 覆盖默认的包

## release-1.5.0

* production 模式打包使用模块路径的哈希值作为 id，避免不同机器因为 id 不一样导致最后的文件哈希不一致
* 修复打包 dll 时候错误被吞掉的问题

## release-1.4.0

* 升级 `babel` 以及 `webpack` 相关依赖版本，如果打包报错 TS18003，请在项目里放一个占位的空 `.ts` 文件，比如 `constants/dummy.ts`，这是 `tsc` 的问题，绕不过去
* 打包报错的时候会有更详细的报错
* 打包失败的时候会自动清除缓存
* 禁用 `ts-loader` 的一切打包优化
* 删除了 `thread-loader`，带来的问题比收益更多

## release-1.3.0 (需要 `shared` 版本 >= release-1.2.0)

* 将 `design` 组件移到单独的 submodule

## release-1.2.0 (需要 `shared` 版本 >= release-1.1.0)

* 开启打包缓存支持，提高打包效率
* 关闭 `fork-ts-checker-webpack-plugin` 的 `tslint` 检查
* 修复动态加载代码时 `publicPath` 错误的问题

## release-1.1.0

* 支持 babel 新版的 `decorator` 提案
* 增加 `transform-runtime` 插件
* 修复 CSS 文件 hash 值和 JS 相关联的问题

## release-1.0.0

* 初始版本
