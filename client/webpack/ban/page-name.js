const logSymbols = require('log-symbols');
const log = require('../utils/log');

// 由于我们有部分业务名字里有【广告】字眼，这些非常容易被 adblock 拦截，导致页面空白
const ADBLOCK_BLACK_LIST = [
  /\badverti[s|z]ing/i,
  /\badverti[s|z]e?ment/i,
  /\badverti[zs]er?/i,
  /\bads\b/i,
];

module.exports = function banPageName(pageNames) {
  const blocked = pageNames.reduce((acc, name) => {
    if (ADBLOCK_BLACK_LIST.some(r => r.test(name))) {
      acc.push(name);
    }

    return acc;
  }, []);

  if (blocked.length > 0) {
    log.panic(
      [
        `${logSymbols.error} 以下页面名称会被 adblock 拦截导致页面空白，请使用别的名字`,
        ...blocked,
      ].join('\n  ')
    );
  }
};
