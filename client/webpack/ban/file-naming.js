const fs = require('fs');
const path = require('path');
const chalk = require('chalk');
const log = require('../utils/log');
const { DIRS_TO_CHECK, DIRS_TO_SKIP } = require('./source-dirs');

const LOWERCASE_NUMBER_HYPHEN = `小写字母、数字以及 - `;
const ERRORS = {
  ext: '文件名后缀只能由小写字母以及数字构成',
  js_camel: `非 React 组件文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成`,
  js_index: `index.jsx? 文件名必须小写`,
  js: `js 文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成，如果是 React 组件也可以驼峰(首字母大写)`,
  ts: `ts 文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成，如果是 React 组件请用 tsx 后缀`,
  ts_index: `index.ts 文件名必须小写`,
  tsx: `tsx 文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成，如果是 React 组件也可以驼峰(首字母大写)`,
  tsx_camel: `非 React 组件文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成，同时请把后缀改成 ts`,
  tsx_index: `index.tsx 文件名必须小写`,
  style_camel: `非 React 组件的样式文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成`,
  style: `样式文件名只能由${LOWERCASE_NUMBER_HYPHEN}构成，如果是 React 组件对应的样式也可以和组件名称保持一致`,
  dir: `文件夹名字只能由${LOWERCASE_NUMBER_HYPHEN}构成`,
};

function formatErrorMessage(err, parent, basename, extname = '') {
  return (
    chalk.red(`[convention] ${chalk.black.bgRed.underline(basename + extname)}: ${err}`) +
    chalk.grey(`\n\t     in ${parent}\n`)
  );
}

const FILE_RULES = {
  js: jsFileRule,
  jsx: jsFileRule,

  ts: tsFileRule,
  tsx: tsxFileRule,

  css: styleFileRule,
  scss: styleFileRule,
};

function fallbackFileRule(parent, basename, extname) {
  return true;
}

function tsFileRule(parent, basename, extname) {
  const originName = basename;
  basename = basename.replace(/\.(spec|d)$/, '');

  if (isLowerCaseOrHyphen(basename)) {
    return true;
  }

  if (basename.toLowerCase() === 'index') {
    throw new Error(formatErrorMessage(ERRORS.ts_index, parent, originName, extname));
  }

  throw new Error(formatErrorMessage(ERRORS.ts, parent, originName, extname));
}

function tsxFileRule(parent, basename, extname) {
  const originName = basename;
  basename = basename.replace(/\.spec$/, '');
  if (isLowerCaseOrHyphen(basename)) {
    return true;
  }

  if (isCamelCase(basename)) {
    const content = fs.readFileSync(path.join(parent, `${originName}${extname}`), {
      encoding: 'utf-8',
    });

    if (hasReactImported(content)) {
      return true;
    }

    if (basename.toLowerCase() === 'index') {
      throw new Error(formatErrorMessage(ERRORS.tsx_index, parent, originName, extname));
    }

    throw new Error(formatErrorMessage(ERRORS.tsx_camel, parent, originName, extname));
  }

  throw new Error(formatErrorMessage(ERRORS.tsx, parent, originName, extname));
}

function jsFileRule(parent, basename, extname) {
  const originName = basename;
  basename = basename.replace(/\.spec$/, '');
  if (isLowerCaseOrHyphen(basename)) {
    return true;
  }

  if (isCamelCase(basename)) {
    const content = fs.readFileSync(path.join(parent, `${originName}${extname}`), {
      encoding: 'utf-8',
    });

    if (hasReactImported(content)) {
      return true;
    }

    if (basename.toLowerCase() === 'index') {
      throw new Error(formatErrorMessage(ERRORS.js_index, parent, originName, extname));
    }

    throw new Error(formatErrorMessage(ERRORS.js_camel, parent, originName, extname));
  }

  throw new Error(formatErrorMessage(ERRORS.js, parent, originName, extname));
}

function styleFileRule(parent, basename, extname) {
  if (isScssFilename(basename) || isSassMixinFileName(basename)) {
    return true;
  }

  if (isCamelCase(basename)) {
    const existsJsFileWithSameName = ['js', 'jsx', 'ts', 'tsx'].some(ext =>
      fs.existsSync(path.join(parent, `${basename}.${ext}`))
    );

    if (existsJsFileWithSameName) {
      return true;
    }

    throw new Error(formatErrorMessage(ERRORS.style_camel, parent, basename, extname));
  }

  throw new Error(formatErrorMessage(ERRORS.style, parent, basename, extname));
}

function isLowerCase(str) {
  return /^[a-z][0-9a-z]*$/.test(str);
}

function isLowerCaseOrHyphen(str) {
  return /^[a-z][0-9a-z-]*$/.test(str);
}

function isSassMixinFileName(str) {
  return /^_[a-z][0-9a-z-]*$/.test(str);
}

function isScssFilename(str) {
  return /^[a-z][0-9a-z-]*(\.global)?(\.m)?$/.test(str);
}

function isCamelCase(str) {
  return /^[A-Z][a-zA-Z0-9]*$/.test(str);
}

function hasReactImported(code) {
  return /import.+from\s+'react';/.test(code) || /import.+from\s+"react";/.test(code);
}

function isTestDirectoryName(str) {
  return /^__mocks__$/.test(str);
}

function checkFileName(parent, name) {
  if (name[0] === '.') {
    return true;
  }

  const extname = path.extname(name);
  const basename = path.basename(name, extname);

  const extnameNoDot = extname[0] === '.' ? extname.slice(1) : extname;
  if (extnameNoDot && !isLowerCase(extnameNoDot)) {
    throw new Error(formatErrorMessage(ERRORS.ext, parent, basename, extname));
  }
  const checker = FILE_RULES[extnameNoDot] || fallbackFileRule;
  return checker(parent, basename, extname);
}

function checkDirectoryName(parent, name) {
  if (!isLowerCaseOrHyphen(name) && !isTestDirectoryName(name)) {
    throw new Error(formatErrorMessage(ERRORS.dir, parent, name));
  }

  return true;
}

function checkNaming(root, options = {}) {
  const dirEntries = fs.readdirSync(root);
  const dirs = [];

  if (dirEntries.length === 0) {
    return true;
  }

  dirEntries.forEach(function step(entry) {
    const fullpath = path.join(root, entry);

    const stat = fs.statSync(fullpath);

    if (options.verbose) {
      log.info(path.join(root, entry));
    }

    if (stat.isDirectory() && DIRS_TO_SKIP.indexOf(entry) === -1) {
      dirs.push(entry);
      return checkDirectoryName(root, entry);
    } else if (stat.isFile()) {
      return checkFileName(root, entry);
    }
  });

  dirs.forEach(d => checkNaming(path.join(root, d), options));
}

function run(options) {
  for (let i = 0; i < DIRS_TO_CHECK.length; i++) {
    const dir = DIRS_TO_CHECK[i];
    try {
      if (fs.existsSync(dir)) {
        checkNaming(dir, options);
      }
    } catch (ex) {
      log.panic(ex.message);
    }
  }
}

module.exports = run;
