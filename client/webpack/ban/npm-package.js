const chalk = require('chalk');
const logSymbols = require('log-symbols');
const helper = require('../utils/helper');
const extractCommonDependencies = require('../utils/extract-common-dependencies');
const log = require('../utils/log');

const YOUZAN_PACKAGE = [/^zan-.+$/, /^@youzan\/.+$/, /^@zent\/.+$/, 'vant', 'captain-ui', 'zent'];

const WHITELIST = [
  // Youzan packages
  ...YOUZAN_PACKAGE,

  '@babel/runtime',

  // React
  'react',
  'react-dom',
  'classnames',
  'prop-types',
  'react-transition-group',

  // Redux
  'redux',
  'react-redux',
  'redux-thunk',
  'redux-promise-middleware',

  // These two should be in devDependencies
  // 'redux-logger',
  // 'redux-devtools-extension',

  // Router
  'react-router',
  'history',
  'react-router-redux',

  // dnd
  'react-dnd',
  'react-dnd-html5-backend',

  // Vue
  'vue',

  // echarts
  'echarts-for-react',
  'echarts',

  // d3 and d3 third package
  /^d3(-\w+)?$/,

  // 3rd party react components
  'video-react',
  'react-markdown',
  'react-window', // efficiently rendering large lists

  // Utilities
  'lodash',
  'lodash-es',
  'date-fns',
  'iscroll',
  'uuid', // use nanoid if you can
  'nanoid',
  'downloadjs', // download files
  'qrcode.react', // QRCode Component
  'html-to-image',
  'filesize', // human readable size

  'tslib', // typescript runtime library

  'html2canvas',
  'html-entities',
  'jspdf',
  'big.js',

  'cross-storage',
];

const SPECIAL_MESSAGES = {
  'redux-logger': '请安装到 devDependencies',
  'redux-devtools-extension': '请安装到 devDependencies',
};
const DEFAULT_MESSAGE = `是不是应该安装到 devDependencies？申诉请找${chalk.bold('李晨')}`;

// FIXME: fails on 1.x, 1.2
// This should be enough if no one deliberately tries to break the rules.
const LOCKED_VERSION_REGEXP = /^[^*^~><=]+$/;

/**
 * 这里只扫描了业务代理里用到的包；由于一些包的依赖是写在 peerDependencies 里的，
 * 而且有可能业务代码里并没有直接引用的地方，这里特殊处理了，会去检查写了这个 peerDependencies 的包有没有被用到。
 */
const PEER_DEPENDENCY_MAP = {
  echarts: ['echarts-for-react'],
  '@youzan/retail-form': ['@youzan/retail-components'],
};

function matchPacakge(packageName, pkg) {
  if (helper.isRegExp(pkg)) {
    return pkg.test(packageName);
  }

  return pkg === packageName;
}

function banNpmPackage(packageName) {
  const allow = WHITELIST.some(pkg => matchPacakge(packageName, pkg));

  if (!allow) {
    const msg = SPECIAL_MESSAGES[packageName] || DEFAULT_MESSAGE;
    log.error(
      `${logSymbols.error} [convention] 不允许在 dependencies 里使用 npm 包 '${packageName}'，${msg}\n`
    );
  }

  return !allow;
}

function isNpmPackageVersionLocked(packageName, version) {
  const isYouzanPackage = YOUZAN_PACKAGE.some(pkg => matchPacakge(packageName, pkg));

  if (!isYouzanPackage) {
    return true;
  }

  if (LOCKED_VERSION_REGEXP.test(version)) {
    return true;
  }

  log.error(`${logSymbols.error} [convention] 内部包必须写死版本号 ${packageName}: ${version}\n`);

  return false;
}

function checkUnusedPackage() {
  const moduleRefCountMap = extractCommonDependencies();
  const modulesWithNoRef = [];
  for (const [mod, ref] of moduleRefCountMap.entries()) {
    if (ref.count <= 0) {
      if (
        mod in PEER_DEPENDENCY_MAP &&
        PEER_DEPENDENCY_MAP[mod].some(m => {
          const ref = moduleRefCountMap.get(m);
          return ref && ref.count > 0;
        })
      ) {
        continue;
      }

      modulesWithNoRef.push(mod);
    }
  }
  if (modulesWithNoRef.length > 0) {
    log.panic(
      `${logSymbols.error} ` +
        '\n[convention] client 下安装了没有使用的包，请检查并清理\n  ' +
        modulesWithNoRef.join('\n  ') +
        '\n'
    );
  }
}

function checkNpmPackages() {
  const npmPackages = helper.getAppDependencies();
  const somePackageBanned = npmPackages.some(banNpmPackage);
  if (somePackageBanned) {
    log.panic();
  }

  const versionedNpmPackages = helper.getAppVersionedDependencies();
  const packageNotLocked = Object.keys(versionedNpmPackages).some(
    pkg => !isNpmPackageVersionLocked(pkg, versionedNpmPackages[pkg])
  );
  if (packageNotLocked) {
    log.panic();
  }

  // 警告没有使用的包
  checkUnusedPackage();
}

module.exports = checkNpmPackages;
