const compareVersions = require('compare-versions');
const logSymbols = require('log-symbols');
const log = require('../utils/log');

const REACT_HOT_LOADER_MAX_VERSION = '5.0.0';
const REACT_HOT_LOADER_MINI_VERSION = '4.12.7';
const REACT_HOT_LOADER_NOT_INSTALLED = `请在 client 目录下以 devDependency 的形式安装 react-hot-loader
react-hot-loader 版本要求：^${REACT_HOT_LOADER_MINI_VERSION}`;

function checkReactHotLoaderVersion() {
  let pkg;
  try {
    pkg = require('react-hot-loader/package.json');
  } catch (ex) {
    log.panic(`${logSymbols.error} ` + REACT_HOT_LOADER_NOT_INSTALLED);
  }

  if (
    compareVersions(pkg.version, REACT_HOT_LOADER_MINI_VERSION) < 0 ||
    compareVersions(pkg.version, REACT_HOT_LOADER_MAX_VERSION) >= 0
  ) {
    const ver = pkg.version;
    log.panic(
      `${logSymbols.error} react-hot-loader 版本要求：^${REACT_HOT_LOADER_MINI_VERSION}，当前安装的版本：${ver}`
    );
  }
}

function checkReactHotDom() {
  let reactDOMPkg;
  try {
    reactDOMPkg = require('react-dom/package.json');
  } catch (ex) {
    log.panic(`${logSymbols.error} react-dom not installed`);
  }

  if (compareVersions(reactDOMPkg.version, '16.8.0') >= 0) {
    try {
      require('@hot-loader/react-dom/package.json');
    } catch (ex) {
      const ver = reactDOMPkg.version;
      const name = '@hot-loader/react-dom';
      log.panic(
        `${logSymbols.error} 当前 react 版本支持 hooks，代码热更新需要 ${name}，请在 client 目录下执行 yarn add ${name}@${ver} -D`
      );
    }
  }
}

module.exports = function() {
  checkReactHotLoaderVersion();
  checkReactHotDom();
};
