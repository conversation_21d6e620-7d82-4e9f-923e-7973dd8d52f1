const path = require('path');
const glob = require('glob');

const DIRS_TO_SKIP = ['node_modules', 'bower_components'];

const DIRS_TO_CHECK = (function() {
  const root = path.resolve(__dirname, '../../');
  return glob
    .sync('*/', {
      cwd: root,
      ignore: [...DIRS_TO_SKIP, 'webpack'],
    })
    .filter(p => !p.startsWith('.'))
    .map(p => path.resolve(root, p));
})();

module.exports = {
  DIRS_TO_CHECK,
  DIRS_TO_SKIP,
};
