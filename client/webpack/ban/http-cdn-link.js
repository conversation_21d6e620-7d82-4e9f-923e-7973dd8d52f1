const ch = require('child_process');
const logSymbols = require('log-symbols');
const log = require('../utils/log');
const { DIRS_TO_CHECK, DIRS_TO_SKIP } = require('./source-dirs');

const HTTP_CDN_LINK_GREP = `http://[a-zA-Z0-9]+\\.yzcdn\\.cn/`;

function banHttpCdnLink() {
  findFilesWithHttpCdnLink(files => {
    if (files.length) {
      log.panic(
        [
          `${logSymbols.error} [convention] 禁止使用 http 协议的 cdn 链接, 请改为 https`,
          ...files,
        ].join('\n  ')
      );
    }
  });
}

function findFilesWithHttpCdnLink(callback) {
  ch.exec(buildGrepCommand(), (error, stdout, stderr) => {
    if (stderr) {
      log.panic(`Error while checking http cdn link: ${stderr}`);
    }

    let files = [];
    if (!error) {
      files = stdout.trim().split('\n');
    }

    callback(files);
  });
}

function buildGrepCommand() {
  const exclude = DIRS_TO_SKIP.map(d => `--exclude-dir ${d}`).join(' ');
  const pages = DIRS_TO_CHECK.join(' ');

  return `grep -RlE ${exclude} '${HTTP_CDN_LINK_GREP}' ${pages}`;
}

module.exports = {
  banHttpCdnLink,
  findFilesWithHttpCdnLink,
};
