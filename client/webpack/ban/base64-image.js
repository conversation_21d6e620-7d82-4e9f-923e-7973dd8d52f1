const ch = require('child_process');
const logSymbols = require('log-symbols');
const log = require('../utils/log');
const { DIRS_TO_CHECK, DIRS_TO_SKIP } = require('./source-dirs');

// These two must be in sync
// 20 because http://proger.i-forge.net/%D0%9A%D0%BE%D0%BC%D0%BF%D1%8C%D1%8E%D1%82%D0%B5%D1%80/[20121112]%20The%20smallest%20transparent%20pixel.html
const BASE64_IMAGE_REGEXP = /data:image\/([a-zA-Z][a-zA-Z0-9.-]+);base64,([a-zA-Z0-9+/=]{20,})['")]/gi;
const BASE64_IMAGE_GREP = `data:image/[a-zA-Z][a-zA-Z0-9.-]+;base64,[a-zA-Z0-9+/=]{20,}[\\'")]`;

function banBase64Image() {
  findFilesWithBase64Image(files => {
    if (files.length) {
      log.panic(
        [
          `${logSymbols.error} [convention] 不允许使用 base64 图片，请用 svg 替换或者上传图片到 CDN 后使用。
迁移文档：https://doc.qima-inc.com/pages/viewpage.action?pageId=237124991`,
          ...files,
        ].join('\n  ')
      );
    }
  });
}

function findFilesWithBase64Image(callback) {
  ch.exec(buildGrepCommand(), (error, stdout, stderr) => {
    if (stderr) {
      log.panic(`Error while checking base64 image: ${stderr}`);
    }

    let files = [];
    if (!error) {
      files = stdout.trim().split('\n');
    }

    callback(files);
  });
}

function buildGrepCommand() {
  const exclude = DIRS_TO_SKIP.map(d => `--exclude-dir ${d}`).join(' ');
  const pages = DIRS_TO_CHECK.join(' ');

  // $' is a special syntax which enables ANSI-C string processing
  // https://www.gnu.org/software/bash/manual/html_node/ANSI_002dC-Quoting.html#ANSI_002dC-Quoting
  return `grep -RlE ${exclude} $'${BASE64_IMAGE_GREP}' ${pages}`;
}

module.exports = {
  banBase64Image,
  findFilesWithBase64Image,
  BASE64_IMAGE_REGEXP,
};
