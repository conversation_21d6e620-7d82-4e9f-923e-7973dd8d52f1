const ch = require('child_process');
const logSymbols = require('log-symbols');

const { DIRS_TO_CHECK, DIRS_TO_SKIP } = require('./source-dirs');
const log = require('../utils/log');

const UPYUN_CDN_LINK_GREP_REGEX = `(https?:)?//su\\.yzcdn\\.cn/`;

function banUpyunCdnLink() {
  findFilesWithUpyunCdnLink(files => {
    if (files.length) {
      log.panic(
        [
          `${logSymbols.error} [convention] 禁止在代码中硬编码 su.yzcdn.cn 链接，请改为 b.yzcdn.cn 或 img.yzcdn.cn`,
          ...files,
        ].join('\n  ')
      );
    }
  });
}

function findFilesWithUpyunCdnLink(callback) {
  ch.exec(genGrepCommand(), (error, stdout, stderr) => {
    if (stderr) {
      log.panic(`Error while checking upyun cdn link: ${stderr}`);
    }

    let files = [];
    if (!error) {
      files = stdout.trim().split('\n');
    }

    callback(files);
  });
}

function genGrepCommand() {
  const exclude = DIRS_TO_SKIP.map(d => `--exclude-dir ${d}`).join(' ');
  const pages = DIRS_TO_CHECK.join(' ');

  return `grep -RlE ${exclude} "${UPYUN_CDN_LINK_GREP_REGEX}" ${pages}`;
}

module.exports = {
  banUpyunCdnLink,
  findFilesWithUpyunCdnLink,
};
