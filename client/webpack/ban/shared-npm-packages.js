const fs = require('fs');
const path = require('path');
const logSymbols = require('log-symbols');
const log = require('../utils/log');

function checkSharedNpmPackages() {
  const source = getSourcePackages();
  const installedPackages = getInstalledPackages();

  const incorrectPackages = [];

  Object.keys(source).forEach(pkg => {
    if (source[pkg] !== installedPackages[pkg]) {
      incorrectPackages.push(
        `  ${pkg}: expected ${source[pkg]}, but got ${installedPackages[pkg]}`
      );
    }
  });

  if (Object.keys(incorrectPackages).length) {
    log.panic(
      `${logSymbols.error} [convention] 如需修改 client/shared 依赖的包，请修改 client/shared/bootstrap.sh\n` +
        incorrectPackages.join('\n')
    );
  }
}

function getSourcePackages() {
  const bootstrapFilePath = path.resolve(__dirname, '../../shared/bootstrap.sh');

  try {
    const content = fs.readFileSync(bootstrapFilePath, { encoding: 'utf-8' });
    const lines = content.split('\n');

    return lines.reduce((packages, line) => {
      line = line.trim();
      const match = /^"(.+)@(.+)"$/.exec(line);
      if (match) {
        packages[match[1]] = match[2];
      }

      return packages;
    }, {});
  } catch (ex) {
    return {};
  }
}

function getInstalledPackages() {
  const pkg = require('../../package.json');
  return pkg.dependencies || {};
}

module.exports = checkSharedNpmPackages;
