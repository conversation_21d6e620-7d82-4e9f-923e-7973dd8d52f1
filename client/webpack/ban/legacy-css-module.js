const glob = require('glob');
const fs = require('fs');
const logSymbols = require('log-symbols');
const log = require('../utils/log');
const { DIRS_TO_CHECK, DIRS_TO_SKIP } = require('./source-dirs');

function banCSSModulesInScss() {
  const scssFiles = DIRS_TO_CHECK.reduce((mods, root) => {
    const cssMods = glob.sync('**/*.scss', {
      cwd: root,
      ignore: [...DIRS_TO_SKIP, '**/*.m.scss'],
      absolute: true,
    });
    cssMods.forEach(m => mods.push(m));
    return mods;
  }, []);

  const invalidScssFiles = [];
  let processed = 0;
  scssFiles.forEach(file => {
    fs.readFile(file, 'utf-8', (err, data) => {
      if (err) {
        log.error(`${err.message}\n${err.stack}\n`);
        return;
      }

      if (data.indexOf(':local') !== -1 || data.indexOf(':global') !== -1) {
        invalidScssFiles.push(file);
      }

      processed += 1;

      if (processed === scssFiles.length && invalidScssFiles.length) {
        log.panic(
          [
            `${logSymbols.error} ` +
              `[convention] .scss 文件不支持 CSS Modules，以前之所以能用是因为 css-loader 有 bug，请参考这两个文档迁移
https://doc.qima-inc.com/pages/viewpage.action?pageId=236596110
https://doc.qima-inc.com/pages/viewpage.action?pageId=236597097`,
            ...invalidScssFiles,
          ].join('\n  ')
        );
      }
    });
  });
}

module.exports = banCSSModulesInScss;
