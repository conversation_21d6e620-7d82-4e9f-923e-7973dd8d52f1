const glob = require('glob');
const logSymbols = require('log-symbols');
const log = require('../utils/log');
const { DIRS_TO_CHECK, DIRS_TO_SKIP } = require('./source-dirs');

function warnGlobalCSSModule() {
  const globalCSSModules = DIRS_TO_CHECK.reduce((mods, root) => {
    const cssMods = glob.sync('**/*.global.m.scss', {
      cwd: root,
      ignore: DIRS_TO_SKIP,
      absolute: true,
    });
    cssMods.forEach(m => mods.push(m));
    return mods;
  }, []);

  if (globalCSSModules.length) {
    log.warn(
      [
        `
${logSymbols.warning} [convention] 推荐使用 .m.scss 替代 .global.m.scss，.global.m.scss 文件只是为了兼容 css-loader 老版本行为而存在的
这个文档有详细说明：https://doc.qima-inc.com/pages/viewpage.action?pageId=236596110`,
        ...globalCSSModules,
      ].join('\n  ') + '\n\n'
    );
  }
}

module.exports = warnGlobalCSSModule;
