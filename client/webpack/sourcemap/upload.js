const logSymbols = require('log-symbols');
const { upload } = require('@youzan/sentry-sourcemap-uploader');
const { isDev, getAppName, getOutputPath, isMasterBranch } = require('../utils/helper');
const log = require('../utils/log');

function run() {
  const localAssetDir = getOutputPath();
  const projectName = getAppName();

  log.info(`${logSymbols.info} 开始上传 sourcemap 文件`);

  upload({
    dir: localAssetDir,
    // 必填，sentry 对应的项目名称，需与 sentry 的项目名称完全一致，否则无法获取到对应 sourcemap
    projectName,
  });
}

if (!isDev() && isMasterBranch()) {
  run();
}
