const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');
const logSymbols = require('log-symbols');
const cdn = require('@youzan/superman-cdn');

const log = require('../utils/log');
const { findFilesWithBase64Image, BASE64_IMAGE_REGEXP } = require('../ban/base64-image');

let TEMP_DIR;
const IMG_CACHE = new Map();

function fixBase64Image() {
  extractBase64Images(images => {
    log.info(`${logSymbols.info} 提取的 base64 图片: ${TEMP_DIR}`);
    uploadImages(TEMP_DIR, cdnMap => {
      replaceBase64ImagesInFile(images, cdnMap);
    });
  });
}

function extractBase64Images(done) {
  findFilesWithBase64Image(files => {
    if (files.length === 0) {
      log.log(`${logSymbols.info} No base64 images found`);
      return;
    }

    let progress = 0;
    const images = new Map();
    const onProgress = imgs => {
      imgs.forEach(img => {
        if (!images.has(img.fp)) {
          images.set(img.fp, []);
        }

        images.get(img.fp).push(img);
      });

      progress += 1;
      if (progress === files.length) {
        done(images);
      }
    };
    files.forEach(f => writeBase64ImageInFile(f, onProgress));
  });
}

function replaceBase64ImagesInFile(images, cdnMap) {
  let progress = 0;

  images.forEach((base64Images, fp) => {
    fs.readFile(fp, { encoding: 'utf-8' }, (err, data) => {
      if (err) {
        log.panic(err);
      }

      base64Images.sort((a, b) => {
        return a.loc.start - b.loc.start;
      });

      // Here we are using relative locations.
      // File content is changed after each replacement, so are absolute locations,
      // but relative locations remain the same.
      // We can replace all base64 images in *one* pass by using relative locations.
      //
      // Another way is to start from end, but I prefer using relative locations.
      transformLocationToRelative(base64Images);

      base64Images.reduce((prevEnd, img) => {
        const replacement = cdnMap.get(img.hash);
        const { loc } = img;
        data = replaceSliceInString(
          data,

          // transform back to absolute location
          {
            start: prevEnd + loc.start,
            end: prevEnd + loc.end,
          },

          replacement
        );
        return prevEnd + loc.start + replacement.length;
      }, 0);

      fs.writeFile(fp, data, err => {
        if (err) {
          log.panic(err);
        }

        log.success(`${logSymbols.success} ${fp}`);

        progress += 1;
        if (progress === images.size) {
          log.success(`${logSymbols.info} 替换完成，请检查本地文件的修改是否正确。`);
        }
      });
    });
  });
}

function uploadImages(src, done) {
  cdn
    .upload({
      filePath: src,
      cdnPath: '/fix-base64',
    })
    .then(({ success, repeat }) => {
      const cdnMap = [].concat(success, repeat).reduce((map, entry) => {
        const [local, remote] = Object.entries(entry)[0];

        // We only need files in qiniu
        if (remote.indexOf('//su.yzcdn.cn/') === -1) {
          const imgHash = getImageHashFromFilePath(local);
          map.set(imgHash, remote);
        }

        return map;
      }, new Map());

      done(cdnMap);
    })
    .catch(err => {
      log.panic(`${logSymbols.error} 上传图片失败\n${JSON.stringify(err, null, 2)}`);
    });
}

function findBase64ImageInString(str, fp) {
  const images = [];
  for (;;) {
    const match = BASE64_IMAGE_REGEXP.exec(str);
    if (match === null) {
      break;
    }

    const [full, ext, data] = match;
    const start = match.index;
    images.push({
      fp,

      // [start, end)
      loc: {
        start,

        // trim trailing '|"|)
        end: start + full.length - 1,
      },

      ext,
      data,
      hash: hashImage(data),
    });
  }

  return images;
}

function writeBase64ImageInFile(filepath, done) {
  fs.readFile(filepath, { encoding: 'utf-8' }, (err, data) => {
    if (err) {
      log.panic(err);
    }

    const images = findBase64ImageInString(data, filepath);
    let progress = 0;
    const onWrite = () => {
      progress += 1;

      if (progress === images.length) {
        done(images);
      }
    };
    images.forEach(img => {
      writeImageFile(img, onWrite);
    });
  });
}

function writeImageFile(img, done) {
  if (!TEMP_DIR) {
    TEMP_DIR = fs.mkdtempSync(path.join(os.tmpdir(), 'base64-img-'));
  }

  if (IMG_CACHE.has(img.hash)) {
    done();
    return;
  }

  const imgPath = path.join(TEMP_DIR, `${img.hash}.${img.ext}`);
  fs.writeFile(imgPath, img.data, { encoding: 'base64' }, err => {
    if (err) {
      log.panic(err);
    }

    IMG_CACHE.set(img.hash, true);
    done();
  });
}

function hashImage(img) {
  const hash = crypto.createHash('sha256');
  hash.update(img);
  return hash.digest('hex');
}

function getImageHashFromFilePath(filepath) {
  const name = path.basename(filepath);
  const nameEndIndex = name.indexOf('.');
  return name.slice(0, nameEndIndex);
}

function transformLocationToRelative(sortedImages) {
  sortedImages.reduce((prevEnd, img) => {
    const { loc } = img;
    img.loc = {
      start: loc.start - prevEnd,
      end: loc.end - prevEnd,
    };

    return loc.end;
  }, 0);
}

function replaceSliceInString(str, loc, replacement) {
  return str.slice(0, loc.start) + replacement + str.slice(loc.end);
}

function main() {
  fixBase64Image();
}

main();
