const path = require('path');
const fs = require('fs');
const glob = require('glob');
const helper = require('../utils/helper');
const log = require('../utils/log');

function main() {
  const localAssetDir = helper.getOutputPath();
  const cdnAssetDir = helper.getAppName();
  const jsHash = {};
  const cssHash = {};

  glob(
    '**/*',
    {
      cwd: localAssetDir,
      nodir: true,
      ignore: ['**/*.map', '**/*.json'],
    },
    (err, assets) => {
      if (err) {
        log.panic(`Failed to glob assets for hash ${err}`);
      }

      assets.forEach(f => {
        const cdnPath = path.join(cdnAssetDir, f);
        const lastUnderscoreIndex = f.lastIndexOf('_');
        if (lastUnderscoreIndex === -1) {
          log.panic(`Failed to normalize asset name: ${f}`);
        }
        const name = f.slice(0, lastUnderscoreIndex);

        if (/\.css$/.test(f)) {
          cssHash[name] = cdnPath;
        }

        if (/\.js$/.test(f)) {
          jsHash[name] = cdnPath;
        }
      });

      saveHashToFile(jsHash, 'js');
      saveHashToFile(cssHash, 'css');
    }
  );
}

function sortHash(hash) {
  const entries = Object.entries(hash);
  entries.sort((a, b) => (a[0] > b[0] ? 1 : a[0] < b[0] ? -1 : 0));
  return entries.reduce((sortedHash, [key, value]) => {
    sortedHash[key] = value;
    return sortedHash;
  }, {});
}

function saveHashToFile(hash, type) {
  const configPath = path.resolve(__dirname, `../../../config/version_${type}.json`);
  const hashJsonString = JSON.stringify(sortHash(hash), null, 2);
  fs.writeFileSync(configPath, hashJsonString);
}

main();
