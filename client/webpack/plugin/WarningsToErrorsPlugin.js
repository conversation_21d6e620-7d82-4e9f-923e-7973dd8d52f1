/**
 * Treat webpack build warnings as errors.
 *
 * Adapted from
 * https://github.com/taehwanno/warnings-to-errors-webpack-plugin/blob/master/index.js
 *
 * Removed support for webpack < 4
 */
class WarningsToErrorsPlugin {
  apply(compiler) {
    compiler.hooks.shouldEmit.tap('WarningsToErrorsPlugin', this.handleHook);
  }

  handleHook(compilation) {
    if (compilation.warnings.length > 0) {
      compilation.errors = compilation.errors.concat(compilation.warnings);
      compilation.warnings = [];
    }

    compilation.children.forEach(child => {
      if (child.warnings.length > 0) {
        child.errors = child.errors.concat(child.warnings);
        child.warnings = [];
      }
    });
  }
}

module.exports = WarningsToErrorsPlugin;
