const {
  util: { createHash },
} = require('webpack');

const PLUGIN = 'InvalidateCssContentHashPlugin';
const MODULE_TYPE = 'css/mini-extract';

class InvalidateCssContentHashPlugin {
  constructor(options) {
    const { why } = options;
    this.options = {
      why,
    };
  }

  apply(compiler) {
    compiler.hooks.thisCompilation.tap(PLUGIN, compilation => {
      const salt = JSON.stringify({
        why: this.options.why,
      });

      compilation.hooks.contentHash.tap(PLUGIN, chunk => {
        const { outputOptions } = compilation;
        const { hashFunction, hashDigest, hashDigestLength } = outputOptions;
        const hash = createHash(hashFunction);

        for (const m of chunk.modulesIterable) {
          if (m.type === MODULE_TYPE) {
            m.updateHash(hash);
          }
        }

        const { contentHash } = chunk;
        contentHash[MODULE_TYPE] = hash
          .update(salt)
          .digest(hashDigest)
          .substring(0, hashDigestLength);
      });
    });
  }
}

module.exports = InvalidateCssContentHashPlugin;
