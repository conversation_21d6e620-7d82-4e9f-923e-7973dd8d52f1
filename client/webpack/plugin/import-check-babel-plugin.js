const fs = require('fs');
const path = require('path');
const enhancedResolver = require('enhanced-resolve');
const { getAppRoot } = require('../utils/helper');
const webpackResolveConfig = require('../config/resolve.config');

const appRootPath = getAppRoot();
const webpackResolveExtensionsConf = Array.isArray(webpackResolveConfig.extensions)
  ? webpackResolveConfig.extensions
  : ['.js'];

// 某个具体文件对应的入口文件的映射
const fileNameToMainEntryMap = {};

// 某个路径下是否有 main 文件的映射
const pathHasMainFileMap = {};

const resolverAsync = enhancedResolver.create.sync(webpackResolveConfig);

/**
 * 返回命中缓存的文件路径
 * @param {*} fileName 文件绝对路径
 */
function getMainFilePathInCache(fileName) {
  if (fileNameToMainEntryMap[fileName]) {
    return fileNameToMainEntryMap[fileName];
  }
  return null;
}

function setMainFilePathInCache(fileName, mainInfo) {
  fileNameToMainEntryMap[fileName] = mainInfo;
}

/**
 * 判断 main 文件是否存在
 * @param {string} filePath
 */
function checkMainFileExits(filePath) {
  let isHasMainFile = pathHasMainFileMap[filePath];

  if (isHasMainFile === undefined) {
    const fileMainPath = path.join(filePath, 'main');
    const fileArray = webpackResolveExtensionsConf.map(item => fileMainPath + item);
    isHasMainFile = fileArray.some(f => fs.existsSync(f));
    pathHasMainFileMap[filePath] = isHasMainFile;
  }

  return isHasMainFile;
}

/**
 * 返回改文件所在的 main 页面路径结果
 *
 * 返回结果有如下三种情况
 * 1. { mainFilePath: xxx, hasMainFile: true }
 * 2. { mainFilePath: xxx, hasMainFile: false }
 * 3. { mainFilePath: null, hasMainFile: false }
 *
 * @param {string} filename 文件绝对路径
 */
function findMainFile(filename) {
  const cacheMainFile = getMainFilePathInCache(filename);
  if (cacheMainFile) {
    return cacheMainFile;
  }

  const mainFileInfo = {
    mainFilePath: null,
    hasMainFile: false,
  };

  const fileDirPath = path.dirname(filename);

  const pageFolderPath = fileDirPath.replace(appRootPath + path.sep, '');
  // 引用文件不在 client/pages 路径下
  if (pageFolderPath === fileDirPath) {
    setMainFilePathInCache(filename, mainFileInfo);
    return mainFileInfo;
  }

  const dirNameArray = pageFolderPath.split(path.sep);

  let absoluteCheckPath = appRootPath;

  const arrayLen = dirNameArray.length;

  for (let i = 0; i < arrayLen; i++) {
    const dirName = dirNameArray[i];

    absoluteCheckPath = path.join(absoluteCheckPath, dirName);

    if (checkMainFileExits(absoluteCheckPath)) {
      mainFileInfo.hasMainFile = true;
      mainFileInfo.mainFilePath = absoluteCheckPath;
      break;
    }
  }

  if (!mainFileInfo.hasMainFile && arrayLen) {
    // 没有找到 main 文件 返回第一级的目录
    mainFileInfo.mainFilePath = path.join(appRootPath, dirNameArray[0]);
  }

  setMainFilePathInCache(filename, mainFileInfo);

  return mainFileInfo;
}

/**
 * 把import的路径转化为绝对路径
 * @param {*} currentFilePath
 * @param {*} importPath
 */
function transformImportPathToAbsolutePath(currentFilePath, importPath) {
  let importFileName;

  try {
    importFileName = resolverAsync(path.dirname(currentFilePath), importPath);
  } catch (e) {
    importFileName = null;
  }

  return importFileName;
}

/**
 * 当前文件 main === null || 引用文件 main === null - 不做判断 ✅
 * 当前文件有 main && 引用文件有 main - 两个 main 路径一致 ✅
 * 当前文件有 main && 引用文件无 main - 存在引用文件 && 引用文件 main 路径是 当前文件 main 路径的前缀 ✅
 * 当前文件无 main && 引用文件有 main - 🈲️
 * 当前文件无 main && 应用文件无 main - 两个 main 路径一致 ✅
 * @param {*} currentMainFileResult
 * @param {*} importMainFileResult
 * @return {boolean} isValid
 */
function checkIsImportValid(currentMainFileResult, importMainFileResult) {
  const {
    mainFilePath: currentMainFilePath,
    hasMainFile: currentHasMainFile,
  } = currentMainFileResult;
  const { mainFilePath: importMainFilePath, hasMainFile: importHasMainFile } = importMainFileResult;
  let isValid = true;

  if (!currentMainFilePath || !importMainFilePath) {
    return isValid;
  }

  if ((currentHasMainFile && importHasMainFile) || (!currentHasMainFile && !importHasMainFile)) {
    isValid = currentMainFilePath === importMainFilePath;
  } else if (currentHasMainFile && !importHasMainFile) {
    isValid = currentMainFilePath.startsWith(importMainFilePath);
  } else if (!currentHasMainFile && importHasMainFile) {
    isValid = false;
  }

  return isValid;
}

module.exports = function({ types: t }) {
  return {
    visitor: {
      ImportDeclaration(path) {
        const node = path.node;
        const { source } = node;

        if (!t.isStringLiteral(source)) {
          return;
        }

        const filename = this.file.opts.filename;
        const importFileName = transformImportPathToAbsolutePath(filename, source.value);

        // 解析失败或者引用的是一些依赖包时跳过检查
        if (!importFileName) {
          return;
        }

        // 当前文件路径在 client/pages 下
        if (filename.startsWith(appRootPath)) {
          const currentMainFileResult = findMainFile(filename);
          const importMainFileResult = findMainFile(importFileName);

          const isValid = checkIsImportValid(currentMainFileResult, importMainFileResult);

          if (!isValid) {
            throw path.buildCodeFrameError(
              '不同 pages 之间禁止互相引用，建议提取公共部分至业务级公共文件目录.'
            );
          }
        } else {
          // 除了client/pages 下的文件都不能用 pages 这个 alias
          if (importFileName.startsWith(appRootPath)) {
            throw path.buildCodeFrameError(
              '只有在 client/pages 目录下才能使用 pages 这个 alias 或者引用该目录下的文件.'
            );
          }
        }
      },
    },
  };
};
