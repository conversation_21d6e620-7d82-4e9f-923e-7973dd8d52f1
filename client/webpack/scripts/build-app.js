require('v8-compile-cache');
const ora = require('ora');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const helper = require('../utils/helper');
const log = require('../utils/log');

const smp = new SpeedMeasurePlugin({ disable: !helper.shouldMeasurePerformance() });

const MESSAGES = {
  FAIL: 'Failed to compile pages\n',
  SUCCESS: 'Compile pages\n',
  BUILDING: 'Compile pages\n',
};

if (helper.isDev()) {
  buildDevApp();
} else {
  buildProductionApp();
}

function buildDevApp() {
  const webpack = require('webpack');
  const inquirer = require('inquirer');
  const config = require('../config/dev.config');

  const startWatch = () => {
    const spinner = ora(MESSAGES.BUILDING);
    const compiler = webpack(smp.wrap(config));
    compiler.hooks.watchRun.tapAsync('fe-pc-build-dev', (params, callback) => {
      spinner.start();
      callback();
    });
    const onError = () => {
      spinner.fail(MESSAGES.FAIL);
    };

    let watching = compiler.watch(
      {
        aggregateTimeout: 1000,
      },
      (err, stats) => {
        if (err) {
          onError();
          log.error(err.message || err);

          // Errors like module not found are not recoverable
          // Restart webpack is the easiest way to fix that
          inquirer
            .prompt([
              {
                type: 'confirm',
                message: 'Webpack is NOT be able to recover from these errors, restart?',
                name: 'restart',
                default: true,
              },
            ])
            .then(answers => {
              if (watching) {
                if (answers.restart) {
                  watching.close();
                  watching = null;
                  setTimeout(startWatch, 100);
                } else {
                  watching.invalidate();
                }
              }
            })
            .catch(err => {
              log.panic(err);
            });
          return;
        }

        if (helper.inspectWebpackStats(stats)) {
          helper.saveWebpackStats(stats, 'app');
          spinner.succeed(MESSAGES.SUCCESS);
        } else {
          onError();
        }
      }
    );
  };

  startWatch();
}

function buildProductionApp() {
  const webpack = require('webpack');
  const config = require('../config/prod.config');

  const spinner = ora(MESSAGES.BUILDING).start();

  webpack(smp.wrap(config), (err, stats) => {
    if (err) {
      console.error(err.stack || err);
      if (err.details) {
        console.error(err.details);
      }
      spinner.fail(MESSAGES.FAIL);
      log.panic();
    }

    if (!helper.inspectWebpackStats(stats)) {
      spinner.fail(MESSAGES.FAIL);
      log.panic();
    }

    helper.saveWebpackStats(stats, 'app');

    spinner.succeed(MESSAGES.SUCCESS);
    process.exit(0);
  });
}
