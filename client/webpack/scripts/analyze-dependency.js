const extractCommonDependencies = require('../utils/extract-common-dependencies');

function main() {
  const refCountMap = extractCommonDependencies();
  prettyPrint(refCountMap);
}

function prettyPrint(refCountMap) {
  const entries = [...refCountMap.entries()];
  entries.sort(function cmp(a, b) {
    const ac = a[1].count;
    const bc = b[1].count;
    return bc - ac;
  });
  for (const [key, val] of entries) {
    const lines = [key, `shared: ${val.shared}`, `count: ${val.count}`];
    if (val.count > 0 && val.count !== Infinity) {
      lines.push(`refs: ${['', ...val.refs].join('\n    ')}`);
    }
    console.log(lines.join('\n  '));
  }
}

main();
