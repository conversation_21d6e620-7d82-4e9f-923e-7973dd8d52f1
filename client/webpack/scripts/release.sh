#!/bin/sh

set -e

basepath=$(dirname $0)

source $basepath/./utils.sh

# Check for uncommitted local changes
if output=$(git status --porcelain) && [ ! -z "$output" ]; then
  fail 'Commit your local changes first'
fi

branch="release-$1"

# Update package version
sed -i '' -E "s/\"version\": \".+\"/\"version\": \"$1\"/" $basepath/../package.json

pushd $basepath/.. > /dev/null
git add package.json
git commit -m "Release $1"
git push
git checkout -b "$branch"
git push -u origin "$branch"
popd > /dev/null
