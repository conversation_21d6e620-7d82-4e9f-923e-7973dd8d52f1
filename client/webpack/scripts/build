#!/bin/sh

set -o errtrace

basepath=$(dirname $0)
bin=$basepath/../node_modules/.bin

source $basepath/./utils.sh

# https://gist.github.com/ahendrix/7030300
errexit () {
  local err=$?
  set +o xtrace
  local code="${1:-42}"
  log_error "[build]: Error in ${BASH_SOURCE[1]}:${BASH_LINENO[0]}. '${BASH_COMMAND}' exited with status $err"
  # Print out the stack trace described by $function_stack
  if [ ${#FUNCNAME[@]} -gt 2 ]
  then
    log_error "Call tree:"
    for ((i=1;i<${#FUNCNAME[@]}-1;i++))
    do
      log_error " $i: ${BASH_SOURCE[$i+1]}:${BASH_LINENO[$i]} ${FUNCNAME[$i]}(...)"
    done
  fi

  $basepath/./clean-cache

  log_error "[build]: Failed with status ${code}"
  exit "${code}"
}

trap errexit ERR

# Parse aguments
# Supported arguments: -e [--entry]
while test $# -gt 0
do
  case "$1" in
    -e | --entry)
      entries="$entries $1 $2"
      shift
      ;;
    *)
      echo "bad argument $1"
      exit 1
      ;;
  esac
  shift
done

$bin/cross-env NODE_ENV=production node $basepath/./convention-check.js
$bin/cross-env NODE_ENV=production node $basepath/./prune.js
$bin/cross-env NODE_ENV=production node $basepath/./log-cache-dir.js
$bin/cross-env NODE_ENV=production BUILD_TYPE=dll node --max_old_space_size=8192 $basepath/./build-dll.js
$bin/cross-env NODE_ENV=production node --max_old_space_size=8192 $basepath/./build-app.js $entries
$bin/cross-env NODE_ENV=production node $bin/es-guard check ../static/build
$bin/cross-env NODE_ENV=production node $basepath/../hash/make.js
$bin/cross-env NODE_ENV=production node $basepath/../cdn/upload.js
$bin/cross-env NODE_ENV=production node $basepath/../sourcemap/upload.js
