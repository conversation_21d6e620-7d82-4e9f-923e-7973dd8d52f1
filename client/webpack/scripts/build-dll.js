/**
 * Multiple dlls must be built one by one to ensure no code are duplicated in mulitple dlls.
 */
require('v8-compile-cache');
const webpack = require('webpack');
const ora = require('ora');
const log = require('../utils/log');
const helper = require('../utils/helper');

const config = helper.isDev()
  ? require('../config/dll.dev.config')
  : require('../config/dll.prod.config');

const moduleSet = new Set();

(function step(idx) {
  const conf = config[idx];
  const dllName = getDllNameFromConfig(conf);
  const spinner = ora(`Compile dll ${dllName}\n`).start();

  webpack(conf, (err, stats) => {
    if (err) {
      console.error(err.stack || err);
      if (err.details) {
        console.error(err.details);
      }
      log.panic();
    }

    if (!helper.inspectWebpackStats(stats)) {
      log.panic();
    }

    helper.saveWebpackStats(stats, dllName);

    spinner.succeed();

    const nextIdx = idx + 1;

    if (idx >= 1) {
      removeDuplicateManifestEntry(config.slice(idx - 1, nextIdx).map(getDllNameFromConfig));
    }

    if (nextIdx < config.length) {
      step(nextIdx);
    } else {
      process.exit(0);
    }
  });
})(0);

function removeDuplicateManifestEntry(dllNameList) {
  const dependencies = dllNameList.map(helper.loadDllManifest);
  const [prevDll, target] = dependencies;

  Object.keys(prevDll.content).forEach(k => {
    if (!isDllReferenceEntry(k)) {
      moduleSet.add(k);
    }
  });

  const targetContent = target.content;
  const entriesToRemove = Object.keys(targetContent).reduce((trash, mod) => {
    if (moduleSet.has(mod)) {
      trash.push(mod);
    }

    return trash;
  }, []);

  entriesToRemove.forEach(mod => {
    delete targetContent[mod];
  });

  helper.saveDllManifest(dllNameList[1], target);
}

function getDllNameFromConfig(config) {
  return Object.keys(config.entry)[0];
}

function isDllReferenceEntry(entry) {
  return /^dll-reference .+$/.test(entry);
}
