/**
 * 项目规范检查：
 *
 * - ban npm 包
 * - 文件名/目录名规范
 */

require('v8-compile-cache');
const banNpmPackage = require('../ban/npm-package');
const checkFileName = require('../ban/file-naming');
const checkSharedNpmPackages = require('../ban/shared-npm-packages');
const checkHotLoader = require('../ban/react-hot-loader-version');
const warnGlobalCSSModule = require('../ban/global-css-module');
const banCSSModulesInScss = require('../ban/legacy-css-module');
const { banBase64Image } = require('../ban/base64-image');
const { banHttpCdnLink } = require('../ban/http-cdn-link');
const { banUpyunCdnLink } = require('../ban/upyun-cdn-link');

banNpmPackage();
checkSharedNpmPackages();
checkFileName({ verbose: false });
checkHotLoader();
banCSSModulesInScss();
warnGlobalCSSModule();
banBase64Image();
banHttpCdnLink();
banUpyunCdnLink();
