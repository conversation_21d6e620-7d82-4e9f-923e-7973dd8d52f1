const rimraf = require('rimraf');
const fs = require('fs');
const path = require('path');
const glob = require('glob');
const logSymbols = require('log-symbols');
const helper = require('../utils/helper');
const log = require('../utils/log');

const MANIFEST_SUFFIX = '-manifest.json';
const FILE_EXTS = ['-manifest.json', '.js', '.stats.json', '.css', '.js.map', '.css.map'];

function main() {
  const argv = process.argv.slice(2);
  const preserveDll = argv.indexOf('--preserve-dll') !== -1;
  prune(preserveDll);
  tryInvalidateCache();
}

function prune(preserveDll = false) {
  const output = helper.getOutputPath();
  const msg = {
    fail: `${logSymbols.error} Failed to clean previous output ${output}\n`,
    success: `${logSymbols.success} Output cleaned\n`,
  };

  if (!preserveDll) {
    rimraf(output, err => {
      if (err) {
        log.panic(`${msg.fail}${err}\n`);
      } else {
        log.success(msg.success);
      }
    });
    return;
  }

  // preserve dll files
  glob(
    '**/*.*',
    {
      cwd: output,
      absolute: true,
    },
    (err, matches) => {
      if (err) {
        log.panic(`${msg.fail}${err}\n`);
      }

      const dllNames = matches
        .filter(f => f.endsWith(MANIFEST_SUFFIX))
        .map(f => path.basename(f, MANIFEST_SUFFIX));
      const allowedFileNames = dllNames.reduce((names, n) => {
        FILE_EXTS.forEach(ext => {
          names.push(`${n}${ext}`);
        });
        return names;
      }, []);

      const filesToRemove = matches.filter(f => allowedFileNames.indexOf(path.basename(f)) === -1);
      let success = 0;
      let error = 0;
      filesToRemove.forEach(f =>
        rimraf(f, err => {
          if (err) {
            error++;
            log.warn(`${logSymbols.warning} Failed to remove ${f}\n`);
          } else {
            success++;
          }

          if (error + success === filesToRemove.length) {
            if (error === 0) {
              log.success(msg.success);
            } else {
              log.panic(`${logSymbols.error} Some files are not removed\n`);
            }
          }
        })
      );
    }
  );
}

function tryInvalidateCache() {
  const cacheDir = helper.getCacheDirNoEnv();
  const parent = path.dirname(cacheDir);

  if (!fs.existsSync(parent)) {
    return;
  }

  fs.readdir(parent, (err, items) => {
    if (err) {
      log.warn(`${logSymbols.warning} Failed to read cache ${parent}\n${err}`);
      return;
    }

    // there won't be too many items, normally it's 2 or 1
    items.forEach(item => {
      const filepath = path.join(parent, item);
      if (filepath !== cacheDir) {
        rimraf(filepath, err => {
          if (err) {
            log.warn(
              `${logSymbols.warning} Failed to invalidate outdated cache ${filepath}\n${err}`
            );
          } else {
            log.success(`${logSymbols.success} Cache ${filepath} invalidated`);
          }
        });
      }
    });
  });
}

main();
