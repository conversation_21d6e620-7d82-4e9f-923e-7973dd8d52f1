#!/bin/sh

set -e
set -o pipefail

basepath=$(dirname $0)
bin=$basepath/../node_modules/.bin
skip_dll=false

# Parse aguments
# Supported arguments: -e [--entry], --no-dll
while test $# -gt 0
do
  case "$1" in
    --no-dll)
      skip_dll=true
      ;;
    -e | --entry)
      entries="$entries $1 $2"
      shift
      ;;
    *)
      echo "bad argument $1"
      exit 1
      ;;
  esac
  shift
done

$bin/cross-env NODE_ENV=development node $basepath/./convention-check.js

if [ "$skip_dll" = false ] ; then
  $bin/cross-env NODE_ENV=development node $basepath/./prune.js
else
  $bin/cross-env NODE_ENV=development node $basepath/./prune.js --preserve-dll
fi

$bin/cross-env NODE_ENV=development node $basepath/./log-cache-dir.js

if [ "$skip_dll" = false ] ; then
  $bin/cross-env NODE_ENV=development BUILD_TYPE=dll node --max_old_space_size=8192 $basepath/./build-dll.js
  code=$?
  if [ $code -ne 0 ]; then
    exit 42
  fi
fi

$bin/cross-env NODE_ENV=development node --max_old_space_size=8192 $basepath/./build-app.js $entries
