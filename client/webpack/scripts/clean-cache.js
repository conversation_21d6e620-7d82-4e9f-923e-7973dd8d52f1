const rimraf = require('rimraf');
const logSymbols = require('log-symbols');
const helper = require('../utils/helper');
const log = require('../utils/log');

function cleanCache() {
  const dir = helper.getCacheDirNoEnv();
  rimraf(dir, err => {
    if (err) {
      log.panic(`${logSymbols.error} Failed to clean cache: ${err}\nCache location: ${dir}\n`);
    } else {
      log.success(`${logSymbols.success} Cache cleaned: ${dir}\n`);
    }
  });
}

cleanCache();
