const merge = require('webpack-merge');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const autoprefixer = require('autoprefixer');
const baseConfig = require('./base.config');
const helper = require('../utils/helper');

let config = merge.smart(baseConfig, {
  mode: 'development',

  devtool: 'cheap-module-source-map',

  optimization: {
    minimizer: [
      // Do css transformations once per css entry
      new OptimizeCSSAssetsPlugin({
        cssProcessorOptions: {
          map: {
            inline: false,
          },
        },
        cssProcessorPluginOptions: {
          preset: cssnanoDevPreset,
        },
      }),
    ],
  },

  // 我们假设打包是在打包服务器上做的，打包服务器是 Linux，文件系统是区分大小写的，不需要这个插件
  // 这个 plugin 是有一定开销的（约 20% - 30%），服务器上打包禁用可以提高性能
  plugins: [new CaseSensitivePathsPlugin()],
});

// This is required to support hot reloading React hooks
if (helper.shouldAliasReactDOM()) {
  config = merge.smart(config, {
    resolve: {
      alias: {
        'react-dom': '@hot-loader/react-dom',
      },
    },
  });
}

function cssnanoDevPreset(opts = {}) {
  const options = Object.assign(
    {
      autoprefixer: {},
    },
    opts
  );

  // Only autoprefixer in dev mode
  const plugins = [[autoprefixer, options.autoprefixer]];

  return { plugins };
}

module.exports = config;
