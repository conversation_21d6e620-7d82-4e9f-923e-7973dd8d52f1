const path = require('path');

module.exports = {
  alias: {
    components: path.resolve(__dirname, '../../components'),
    pages: path.resolve(__dirname, '../../pages'),
    fns: path.resolve(__dirname, '../../fns'),
    constants: path.resolve(__dirname, '../../constants'),
    'shared/design-components': path.resolve(__dirname, '../../design-components'),
    'shared/decorate-components': path.resolve(__dirname, '../../decorate-components'),
    shared: path.resolve(__dirname, '../../shared'),
    definitions: path.resolve(__dirname, '../../../definitions'),
    hooks: path.resolve(__dirname, '../../hooks'),
  },
  modules: [
    'node_modules',

    // ts 的定义文件在项目根目录，react-hot-loader 会找不到
    path.resolve(__dirname, '../../node_modules'),
  ],
  extensions: ['.js', '.jsx', '.ts', '.tsx'],
};
