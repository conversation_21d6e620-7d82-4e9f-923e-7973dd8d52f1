const webpack = require('webpack');
const path = require('path');
const helper = require('../utils/helper');

function getDllEntryNames() {
  const userDllConfig = helper.getUserDllConfig();
  const extraEntry = userDllConfig.extraEntry || {};

  return ['base', 'vendor', ...Object.keys(extraEntry)];
}

module.exports = getDllEntryNames().map(
  entry =>
    new webpack.DllReferencePlugin({
      context: path.join(__dirname, '..', '..'),
      manifest: require(path.join(helper.getOutputPath(), `${entry}-manifest.json`)),
    })
);
