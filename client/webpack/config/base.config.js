const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const WarningsToErrorsPlugin = require('../plugin/WarningsToErrorsPlugin');
const resolveConfig = require('./resolve.config');
const helper = require('../utils/helper');
const { getCssRule } = require('../rules/css');
const scssRule = require('../rules/scss');
const getBabelRule = require('../rules/babel');
const getTypeScriptRule = require('../rules/typescript');

const plugins = [
  new webpack.DefinePlugin({
    __DEBUG__: JSON.stringify(helper.isDev()),
  }),

  new webpack.EnvironmentPlugin(['NODE_ENV']),

  new MiniCssExtractPlugin({
    filename: `${helper.getOutputFileNameTemplate()}.css`,
  }),

  // Treat warnings as errors
  new WarningsToErrorsPlugin(),
];

const baseConfig = {
  context: path.resolve(__dirname, '../../'),

  output: {
    path: helper.getOutputPath(),
    publicPath: helper.getPublicPath(),
    filename: `${helper.getOutputFileNameTemplate()}.js`,
    chunkFilename: `${helper.getOutputFileNameTemplate()}.js`,
  },

  module: {
    rules: [
      getCssRule(),
      scssRule.getScssRule(),
      scssRule.getScssModuleRule(),
      scssRule.getScssModuleGlobalRule(),
      getBabelRule(),
      getTypeScriptRule(),
    ],
  },

  resolve: resolveConfig,

  plugins,

  optimization: {
    noEmitOnErrors: true,
  },

  performance: {
    hints: false,
  },

  stats: {
    modules: false,
    children: false,
    performance: false,
    entrypoints: false,
    colors: true,
  },

  // Fail out on the first error instead of tolerating it
  bail: true,
};

module.exports = baseConfig;
