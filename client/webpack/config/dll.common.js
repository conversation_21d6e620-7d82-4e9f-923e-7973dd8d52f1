const webpack = require('webpack');
const path = require('path');
const fs = require('fs');
const Walker = require('node-source-walk');
const ora = require('ora');
const enhancedResolver = require('enhanced-resolve');
const logSymbols = require('log-symbols');
const helper = require('../utils/helper');
const defaultDllConfig = require('./dll.config.default');
const resolveConfig = require('./resolve.config');
const log = require('../utils/log');
const { getBabelParserOptions } = require('../utils/node-source-walker-options');

const hasOwn = Object.prototype.hasOwnProperty;

let resolveSync = null;
const sourceRoot = path.resolve(__dirname, '../../');
const modulesRequireBabelTransform = [
  'zent',
  'vant',
  'captain-ui',
  'zan-utils',
  '@youzan/utils',
  'date-fns',
  '@youzan/captain-showcase',
  '@youzan/retail-components',
  '@youzan/retail-utils',
  '@youzan/biz-components',
  '@youzan/react-components',
  '@youzan/wsc-membercenter',
  '@youzan/wsc-user-membercenter',
];
const sharedPaths = [
  '../../shared',
  '../../design-components',
  '../../decorate-components',
  '../../components',
  '../../constants',
  '../../fns',
].map(f => path.resolve(__dirname, f));

// Dlls must be created one by one
function createMultiCompilerConfig() {
  const entries = getDllEntry();
  const sortedEntries = sortDllEntries();

  if (Object.keys(entries).length !== sortedEntries.length) {
    throw new Error('Not all dll entries have order defined');
  }

  return sortedEntries.map(entry => {
    const entryIndex = sortedEntries.indexOf(entry);
    const dependencies = sortedEntries.slice(0, entryIndex);
    return createDllConfig({ [entry]: entries[entry] }, dependencies);
  });
}

function getDllEntry() {
  const userDllConfig = helper.getUserDllConfig();
  const isBasePackage = userDllConfig.isBasePackage || defaultDllConfig.isBasePackage;
  const isVendorPackage = userDllConfig.isVendorPackage || defaultDllConfig.isVendorPackage;
  const extraEntry = userDllConfig.extraEntry || {};
  const { extraBasePackages, extraVendorPackages } = userDllConfig;
  const excludeOrigin = pkg => {
    if (defaultDllConfig.exclude && defaultDllConfig.exclude(pkg)) {
      return true;
    }

    if (userDllConfig.exclude && userDllConfig.exclude(pkg)) {
      return true;
    }

    return false;
  };
  const dependencies = helper.getAppDependencies();
  let basePackages = [];
  let vendorPackages = [];
  const promotedVendorAndBase = {
    base: new Set(),
    vendor: new Set(),
  };

  // 将自定义 DLL 依赖的公共模块提升到 base/vendor
  // 这是必须的，否则这个自定义 DLL 必须在所有页面都引用才能保证代码是安全的
  if (Object.keys(extraEntry).length) {
    const spinner = ora();

    Object.keys(extraEntry).forEach(dllName => {
      const deps = extraEntry[dllName];

      spinner.start(`Promoting dependencies for dll ${dllName}\n`);
      [].concat(deps).forEach(mod => {
        promoteToVendorOrBase(mod, promotedVendorAndBase);
      });
      spinner.succeed();
    });

    // console.log(promotedVendorAndBase);
  }

  // 把自定义的 dll 依赖的包 exclude 掉
  const extraEntryDependencies = new Set(
    Object.values(extraEntry).reduce((acc, e) => acc.concat(e), [])
  );
  const exclude = pkg => {
    if (extraEntryDependencies.has(pkg)) {
      return true;
    }

    return excludeOrigin(pkg);
  };

  dependencies.forEach(pkg => {
    if (exclude(pkg)) {
      return;
    }

    if (isBasePackage(pkg)) {
      basePackages.push(pkg);
    } else if (isVendorPackage(pkg)) {
      vendorPackages.push(pkg);
    }
  });

  basePackages = new Set(
    hasOwn.call(userDllConfig, 'overrideBasePackages')
      ? basePackages.concat(userDllConfig.overrideBasePackages)
      : basePackages.concat(defaultDllConfig.extraBasePackages, extraBasePackages || [])
  );
  vendorPackages = new Set(
    hasOwn.call(userDllConfig, 'overrideVendorPackages')
      ? vendorPackages.concat(userDllConfig.overrideVendorPackages)
      : vendorPackages.concat(defaultDllConfig.extraVendorPackages, extraVendorPackages || [])
  );

  promotedVendorAndBase.base.forEach(mod => basePackages.add(mod));
  promotedVendorAndBase.vendor.forEach(mod => vendorPackages.add(mod));

  return {
    vendor: [...vendorPackages],
    base: [...basePackages],
    ...extraEntry,
  };
}

// FIXME: This function should be async
function promoteToVendorOrBase(entry, vendorAndBase) {
  const PROMOTED_BASE_FILE = `shared/components/promoted-custom-dll-${path.basename(
    entry
  )}-base-components.js`;

  function shouldIncludeInBase(modName) {
    if (['zent', 'vant', 'captain-ui'].some(m => modName === m || modName.startsWith(`${m}/`))) {
      return true;
    }

    return /^(zan-|@zent\/|@youzan\/|components\/|fns\/|constants\/|shared\/)/.test(modName);
  }

  function createSpecialBaseComponentFile(specialBaseComponents) {
    const modules = Object.keys(specialBaseComponents);

    // Packages may export the same variable
    let incId = 1;
    const modNameList = [];
    const uniqueModName = mod => {
      const modName = `${mod}Mod${incId++}`;
      modNameList.push(modName);
      return modName;
    };

    if (!modules.length) {
      return false;
    }

    const content =
      '/* eslint-disable */\n// Auto generated. DO NOT EDIT.\n' +
      modules
        .map(mod => {
          const imports = specialBaseComponents[mod];
          return `import { ${[...imports]
            .map(comp => `${comp} as ${uniqueModName(comp)}`)
            .join(', ')} } from '${mod}';`;
        })
        .join('\n') +
      '\n' +
      `/* Bypass usage check in babel-plugin-import */\n` +
      `const __dummy__ = ${modNameList.join(' || ')};\n` +
      `export default __dummy__;\n`;

    fs.writeFileSync(path.resolve(__dirname, `../../${PROMOTED_BASE_FILE}`), content);

    return true;
  }

  const result = {};

  if (isDependency(entry, helper.getAppDependencies())) {
    return result;
  }

  const entryPath = resolveModule(entry);
  getDependenciesForEntry(entryPath, entryPath, result);

  const promotion = Object.keys(result).reduce(
    (acc, mod) => {
      const modImports = result[mod];
      if (shouldIncludeInBase(mod)) {
        if (modImports) {
          acc.specialBase[mod] = modImports;
        } else {
          acc.base.add(mod);
        }
      } else {
        acc.vendor.add(mod);
      }

      return acc;
    },
    {
      ...vendorAndBase,
      specialBase: {},
    }
  );

  if (createSpecialBaseComponentFile(promotion.specialBase)) {
    delete promotion.specialBase;
    promotion.base.add(PROMOTED_BASE_FILE);
  }

  return promotion;
}

function getDependenciesForEntry(entryPath, rootEntry, result) {
  let deps;
  try {
    deps = getModuleDependencies(entryPath);
  } catch (ex) {
    log.panic(`${entryPath}\n${ex.stack}`);
  }

  deps.forEach(mod => {
    if (typeof mod === 'string') {
      if (isRelativeModule(mod)) {
        const resolvedMod = resolveModule(mod, entryPath);
        if (isCommonModule(resolvedMod) && !isSubmodule(resolvedMod, rootEntry)) {
          result[rewriteCommonModuleUsingAlias(resolvedMod)] = null;
        } else if (isJavaScriptModule(resolvedMod) || isTypeScriptModule(resolvedMod)) {
          // console.log(resolvedMod);
          getDependenciesForEntry(resolvedMod, rootEntry, result);
          // } else {
          // console.log(resolvedMod);
        }
      } else {
        result[mod] = null;
      }
    } else {
      const { moduleName, specifiers } = mod;
      let prevSpecifiers = result[moduleName];
      if (!prevSpecifiers) {
        prevSpecifiers = new Set(specifiers);
        result[moduleName] = prevSpecifiers;
      } else {
        specifiers.forEach(sp => prevSpecifiers.add(sp));
      }
    }
  });
}

function resolveModule(mod, parentModPath) {
  if (!resolveSync) {
    resolveSync = enhancedResolver.create.sync(resolveConfig);
  }

  const root = parentModPath ? path.dirname(parentModPath) : sourceRoot;

  const modPath = resolveSync({}, root, mod);

  // console.log(modPath);

  return modPath;
}

function rewriteCommonModuleUsingAlias(mod) {
  return path.relative(sourceRoot, mod);
}

function isJavaScriptModule(mod) {
  return /\.jsx?$/.test(mod);
}

function isTypeScriptModule(mod) {
  return /\.tsx?$/.test(mod);
}

function isSubmodule(mod, parentModule) {
  const parentDir = path.dirname(parentModule);
  return mod.startsWith(parentDir);
}

function isRelativeModule(mod) {
  return mod.startsWith('.');
}

function isCommonModule(mod) {
  for (let i = 0; i < sharedPaths.length; i++) {
    const p = sharedPaths[i];
    if (mod.startsWith(p)) {
      return true;
    }
  }

  return false;
}

function getModuleDependencies(path) {
  const src = fs.readFileSync(path, { encoding: 'utf-8' });
  const walker = new Walker(getBabelParserOptions(path));

  const dependencies = [];

  if (typeof src === 'undefined') {
    throw new Error('src not given');
  }

  if (src === '') {
    return dependencies;
  }

  walker.walk(src, function(node) {
    switch (node.type) {
      case 'ImportDeclaration':
        // import Foo from 'foobar'
        // import { Alert } from 'zent'
        if (node.source && node.source.value) {
          const mod = node.source.value;
          if (modulesRequireBabelTransform.indexOf(mod) !== -1) {
            const specifiers = node.specifiers.reduce((acc, sp) => {
              if (sp.type === 'ImportSpecifier') {
                acc.push(sp.imported.name);
              } else {
                throw new Error(`Unexpected import: ${JSON.stringify(node, null, 2)} `);
              }
              return acc;
            }, []);
            dependencies.push({
              moduleName: mod,
              specifiers,
            });
          } else {
            dependencies.push(mod);
          }
        }
        break;
      case 'ExportNamedDeclaration':
      case 'ExportAllDeclaration':
        // export { Foo } from 'foobar'
        // export * from 'foobar'
        if (node.source && node.source.value) {
          dependencies.push(node.source.value);
        }
        break;
      case 'CallExpression':
        // import('foobar')
        if (node.callee.type === 'Import' && node.arguments.length) {
          dependencies.push(node.arguments[0].value);
        }

        // require('foobar')
        if (
          node.callee.type === 'Identifier' &&
          node.callee.name === 'require' &&
          node.arguments.length
        ) {
          dependencies.push(node.arguments[0].value);
        }
        break;
      default:
        return;
    }
  });

  return dependencies;
}

function isDependency(mod, dependencies) {
  for (let i = 0; i < dependencies.length; i++) {
    const dep = dependencies[i];
    if (mod === dep || dep.startsWith(`${mod}/`)) {
      return true;
    }
  }

  return false;
}

function sortDllEntries() {
  const userDllConfig = helper.getUserDllConfig();
  const orders = {
    ...defaultDllConfig.orders,
    ...userDllConfig.orders,
  };
  const entries = Object.keys(orders);
  entries.sort((a, b) => orders[a] - orders[b]);
  return entries;
}

function createDllConfig(entry, dependencies) {
  // output.library does NOT support [name] placeholder
  const dllEntryName = Object.keys(entry)[0];
  const dllName = `__yz_${dllEntryName}`;

  log.info(
    [`${logSymbols.info} Modules in dll ${dllEntryName}:`]
      .concat(entry[dllEntryName])
      .join('\n  ') + '\n'
  );

  return {
    entry,

    output: {
      filename: `${helper.getOutputFileNameTemplate()}.js`,
      library: dllName,
    },

    plugins: [
      new webpack.DllPlugin({
        context: path.join(__dirname, '..', '..'),
        path: path.join(helper.getOutputPath(), '[name]-manifest.json'),
        name: dllName,
      }),

      // All dependent dlls
      ...dependencies.map(dep => {
        return new webpack.DllReferencePlugin({
          context: path.join(__dirname, '..', '..'),
          manifest: path.join(helper.getOutputPath(), `${dep}-manifest.json`),
        });
      }),
    ],
  };
}

module.exports = createMultiCompilerConfig();
