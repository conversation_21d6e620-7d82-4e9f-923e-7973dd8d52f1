const path = require('path');
const parseArgs = require('minimist');

const scanEntries = require('../utils/scan-entries');

function getEntries() {
  const argv = parseArgs(process.argv.slice(2), {
    alias: {
      e: 'entry',
    },
  });
  let entry = argv.entry;

  if (entry) {
    entry = ['global'].concat(entry);
    return scanEntries(entry, path.resolve(__dirname, '../../pages'));
  } else {
    return scanEntries(path.resolve(__dirname, '../../pages'));
  }
}

module.exports = {
  getEntries,
};
