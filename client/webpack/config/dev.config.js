const path = require('path');
const { WebpackPluginServe } = require('@youzan/webpack-plugin-serve');
const merge = require('webpack-merge');

const baseConfig = require('./base.dev.config');
const dllRefPlugins = require('./dllref.plugins');
const helper = require('../utils/helper');
const { getEntries } = require('./entries');

const config = merge.smart(baseConfig, {
  entry: getEntries(),

  plugins: [
    new WebpackPluginServe({
      compress: true,
      hmr: true,
      host: '127.0.0.1',

      // Use a random free port
      // Use zan-proxy to redirect all HMR request to disk
      port: 0,

      // We don't use these assets, only for debugging
      static: path.resolve(__dirname, helper.getOutputPath()),
    }),
  ].concat(dllRefPlugins),

  watch: true,
});

module.exports = merge.smart(config, helper.getAppWebpackDevConfig());
