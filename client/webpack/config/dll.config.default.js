const enhancedResolver = require('enhanced-resolve');
const path = require('path');
const glob = require('glob');
const resolveConfig = require('./resolve.config');

const NAMED_BASE_PACKAGE = [
  'zan-pc-ajax',
  '@youzan/pc-ajax',
  'zan-ajax',
  '@youzan/ajax',
  'zan-shuai',
];

const NAMED_VENDOR_PACKAGE = [
  'react',
  'react-dom',
  'prop-types',
  'classnames',
  'react-router',
  'history',
];

// @babel/runtime and tslib should be included in vendor
const RUNTIME_PACKAGE = (function() {
  const packages = [];
  const resolveSync = enhancedResolver.create.sync({
    ...resolveConfig,
    mainFields: ['module', 'main'],
  });
  const root = path.resolve(__dirname, '../../');
  const tryResolve = mod => {
    try {
      const resolvedModule = resolveSync({}, root, mod);
      return resolvedModule;
    } catch (ex) {
      return false;
    }
  };

  const tslibPath = tryResolve('tslib');
  const babelHelperPath = tryResolve('@babel/runtime/helpers/esm/extends');
  const regeneratorPath = tryResolve('@babel/runtime/regenerator');

  if (tslibPath) {
    packages.push('tslib');
  }

  // There's no index.js in helpers/esm
  if (babelHelperPath) {
    const babelHelpers = glob.sync('**/*.js', {
      cwd: path.dirname(babelHelperPath),
    });
    babelHelpers.forEach(mod => packages.push(`@babel/runtime/helpers/esm/${mod}`));
  }

  if (regeneratorPath) {
    packages.push('@babel/runtime/regenerator');
  }

  return packages;
})();

const LODASH_PACKAGE = [
  'lodash/get',
  'lodash/map',
  'lodash/forEach',
  'lodash/find',
  'lodash/isEqual',
  'lodash/isString',
  'lodash/isFunction',
  'lodash/isEmpty',
  'lodash/isNumber',
  'lodash/isNil',
  'lodash/omit',
  'lodash/pick',
  'lodash/assign',
  'lodash/noop',
  'lodash/isArray',
  'lodash/debounce',
  'lodash/throttle',
];

const EXTRA_BASE_PACKAGE = ['shared/components', 'shared/fns'];

const EXTRA_VENDOR_PACKAGE = [...RUNTIME_PACKAGE, ...LODASH_PACKAGE];

function isVendorPackage(pkg) {
  return NAMED_VENDOR_PACKAGE.indexOf(pkg) !== -1;
}

function isBasePackage(pkg) {
  return NAMED_BASE_PACKAGE.indexOf(pkg) !== -1;
}

function exclude(pkg) {
  return false;
}

module.exports = {
  isVendorPackage,
  isBasePackage,
  exclude,
  extraBasePackages: EXTRA_BASE_PACKAGE,
  extraVendorPackages: EXTRA_VENDOR_PACKAGE,
  orders: {
    vendor: 0,
    base: 100,
  },
};
