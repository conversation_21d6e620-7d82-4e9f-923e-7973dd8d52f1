const merge = require('webpack-merge');
const webpack = require('webpack');
const crypto = require('crypto');
const TerserPlugin = require('terser-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const defaultPreset = require('cssnano-preset-default');
const autoprefixer = require('autoprefixer');

const InvalidateCssContentHashPlugin = require('../plugin/InvalidateCssContentHashPlugin');
const baseConfig = require('./base.config');
const { getTerserPluginCacheDir, isMasterBranch } = require('../utils/helper');

module.exports = merge.smart(baseConfig, {
  mode: 'production',

  devtool: isMasterBranch() ? 'source-map' : 'none',

  optimization: {
    namedModules: true,
    moduleIds: 'hashed',

    namedChunks: true,
    chunkIds: 'named',

    minimizer: [
      // Customize cache location
      new TerserPlugin({
        parallel: true,
        cache: getTerserPluginCacheDir(),
        sourceMap: isMasterBranch(),
      }),

      // Do css transformations once per css entry
      new OptimizeCSSAssetsPlugin({
        cssProcessorPluginOptions: {
          preset: cssnanoProdPreset,
        },
      }),
    ],
  },

  plugins: [
    // Generate consistent unique chunk names
    new webpack.NamedChunksPlugin(createChunkNameResolver()),

    // CSS 文件的 contenthash 在 optimize-css-assets-webpack-plugin 压缩完之后没有更新
    // 依然是压缩前的 contenthash。
    // 这个问题现在 webpack 4 的设计无法解决，也许 webpack 5 会解决。
    // https://github.com/NMFR/optimize-css-assets-webpack-plugin/issues/56
    // https://github.com/webpack-contrib/terser-webpack-plugin/issues/18
    // https://github.com/webpack/webpack/issues/9520
    //
    // 这个问题会导致文件内容变了但是文件名没变化的问题，superman-cdn 上传的时候发现文件名一样
    // 但是文件大小不一样会上传失败。
    // 这个 plugin 强刷了所有 CSS 文件的 contenthash。
    //
    // `why` 数组只增不减，把每次强刷的原因写上。不到万不得已不要强刷。
    new InvalidateCssContentHashPlugin({
      why: [
        '2019-09-10 Replace postcss-loader with optimize-css-assets-webpack-plugin for css minification',
      ],
    }),
  ],
});

function cssnanoProdPreset(opts = {}) {
  const options = Object.assign(
    {
      autoprefixer: {
        cascade: false,
      },
    },
    opts
  );

  const plugins = [...defaultPreset(options).plugins, [autoprefixer, options.autoprefixer]];

  return { plugins };
}

function createChunkNameResolver(hashDigestLength = 4, namePrefix = 'async-module') {
  const hashesSeen = new Set();

  return function resolveChunkName(chunk) {
    if (!chunk.name) {
      // hash all module ids
      const hash = crypto.createHash('sha512');
      for (const mod of chunk.modulesIterable) {
        hash.update(mod.id);
      }
      const hex = hash.digest('hex');

      // find the shortest hex
      let hexLen = hashDigestLength;
      let shortest = hex.slice(0, hexLen);
      while (hexLen < hex.length && hashesSeen.has(shortest)) {
        hexLen = hexLen + 1;
        shortest = hex.slice(0, hexLen);
      }
      if (hexLen >= hex.length) {
        throw new Error(`Chunk id collision found: ${hex}`);
      }

      // Make it a named chunk
      chunk.name = `${namePrefix}-${shortest}`;
    }

    return chunk.name;
  };
}
