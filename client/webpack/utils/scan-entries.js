const { promisify } = require('util');
const fs = require('fs');
const path = require('path');
const glob = require('glob');
const banPageName = require('../ban/page-name');
const helper = require('./helper');

const readdirP = promisify(fs.readdir);
const statP = promisify(fs.stat);
const DEV = helper.isDev();

async function findMainDotJs(root) {
  const filenames = await readdirP(root);
  const subdirs = [];

  // BFS traversal
  // Don't recurse if there's a match in this level
  for (let i = 0; i < filenames.length; i++) {
    const fname = filenames[i];
    const fpath = path.join(root, fname);
    const fstats = await statP(fpath);

    if (fstats.isFile() && /^main.[tj]sx?$/i.test(fname)) {
      return [path.resolve(fpath)];
    }

    if (fstats.isDirectory()) {
      subdirs.push(fpath);
    }
  }

  // recurse into sub directories
  let mainDotJsFiles = [];
  for (let i = 0; i < subdirs.length; i++) {
    const maybeMainDotJs = await findMainDotJs(subdirs[i]);
    if (maybeMainDotJs) {
      mainDotJsFiles = mainDotJsFiles.concat(maybeMainDotJs);
    }
  }

  return mainDotJsFiles.length ? mainDotJsFiles : null;
}

function scanEntriesInDirectory(root, relativeRoot) {
  const from = path.resolve(relativeRoot || root);

  return async () => {
    const files = await findMainDotJs(root);
    const entries = files.reduce((entries, p) => {
      const relPath = path.relative(from, p);
      const key = path.dirname(relPath);
      entries[key] =
        DEV && key !== 'global'
          ? [
              require.resolve('react-hot-loader/patch'),
              require.resolve('@youzan/webpack-plugin-serve/client'),
              p,
            ]
          : p;
      return entries;
    }, {});

    const keys = Object.keys(entries);
    return keys.reduce((acc, k) => {
      acc[k] = entries[k];
      return acc;
    }, {});
  };
}

function scanEntries(roots, relativeRoot) {
  if (typeof roots === 'string') {
    roots = [roots];
  }

  if (Array.isArray(roots)) {
    const globCwd = helper.getAppRoot();
    const expandedRoots = [
      ...new Set(
        roots.reduce((acc, pattern) => {
          const filenames = glob.sync(pattern, {
            cwd: globCwd,
            absolute: true,
          });
          acc = acc.concat(filenames);
          return acc;
        }, [])
      ),
    ];

    return () =>
      Promise.all(expandedRoots.map(r => scanEntriesInDirectory(r, relativeRoot)())).then(
        entries => {
          const mergedEntries = entries.reduce((acc, e) => {
            return Object.assign(acc, e);
          }, {});

          banPageName(Object.keys(mergedEntries));

          return mergedEntries;
        }
      );
  }

  return () => Promise.resolve([]);
}

module.exports = scanEntries;
