const path = require('path');
const fs = require('fs');
const os = require('os');
const mkdirp = require('mkdirp');
const crypto = require('crypto');
const log = require('./log');
const ch = require('child_process');

function isRegExp(val) {
  return val instanceof RegExp;
}

function isDev() {
  return process.env.NODE_ENV !== 'production';
}

function isDll() {
  return process.env.BUILD_TYPE === 'dll';
}

function shouldMeasurePerformance() {
  return !!process.env.FASTBUILD_MEASURE;
}

function shouldAliasReactDOM() {
  try {
    require('@hot-loader/react-dom/package.json');
    return true;
  } catch (ex) {
    return false;
  }
}

function getOutputPath() {
  return path.resolve(__dirname, isDev() ? '../../../static/local' : '../../../static/build');
}

function getCacheDirNoEnv() {
  const appRoot = getAppRoot();
  const hash = crypto
    .createHash('md5')
    .update(appRoot)
    .digest('hex');
  const hashedCacheDir = hash;

  const cacheLocation = process.env.FASTBUILD_CACHE_DIR || os.homedir();
  const cachePath = path.resolve(
    cacheLocation,
    '.pc-build-cache',
    hashedCacheDir,
    getSelfVersion()
  );

  return cachePath;
}

function getCacheDir() {
  const cachePath = path.resolve(getCacheDirNoEnv(), isDev() ? 'dev' : 'prod');
  try {
    mkdirp.sync(cachePath);
    return cachePath;
  } catch (ex) {
    log.panic(ex);
  }
}

function getCacheLoaderCacheDir() {
  return path.join(getCacheDir(), 'cache-loader');
}

function getBabelLoaderCacheDir() {
  return path.join(getCacheDir(), 'babel-loader');
}

function getTerserPluginCacheDir() {
  return path.join(getCacheDir(), 'terser-webpack-plugin');
}

function getOutputFileNameTemplate() {
  return isDev() ? '[name]' : '[name]_[contenthash]';
}

function getWebpackStatsPath(filename) {
  const staticDir = path.resolve(__dirname, '../../../static');
  return path.join(staticDir, isDev() ? 'local' : 'build', `${filename}.stats.json`);
}

function getSelfVersion() {
  return require('../package.json').version;
}

function getAppPackageDotJson() {
  return require('../../package.json');
}

function getAppDependencies() {
  const pkg = getAppPackageDotJson();
  return Object.keys(pkg.dependencies);
}

function getAppVersionedDependencies() {
  const pkg = getAppPackageDotJson();
  return pkg.dependencies;
}

function getAppName() {
  const pkg = getAppPackageDotJson();
  return `wsc-pc-${pkg.name}`;
}

function getUserDllConfig() {
  return require('../../dll.config');
}

function getAppRoot() {
  return path.resolve(__dirname, '../../pages');
}

function getAppBabelRC() {
  try {
    const babelrc = fs.readFileSync(path.resolve(__dirname, '../../.babelrc'), {
      encoding: 'utf-8',
    });
    if (babelrc) {
      return JSON.parse(babelrc);
    }
  } catch (ex) {
    return {};
  }
}

function getAppWebpackDevConfig() {
  try {
    return require('../../webpack.config.dev.js');
  } catch (ex) {
    return {};
  }
}

function getAppWebpackProductionConfig() {
  try {
    return require('../../webpack.config.prod.js');
  } catch (ex) {
    return {};
  }
}

function loadDllManifest(dllName) {
  try {
    return require(path.join(getOutputPath(), `${dllName}-manifest.json`));
  } catch (ex) {
    return null;
  }
}

function saveDllManifest(dllName, content) {
  if (!content) {
    return;
  }

  fs.writeFileSync(path.join(getOutputPath(), `${dllName}-manifest.json`), JSON.stringify(content));
}

function getPublicPath() {
  const appName = getAppName();
  return `https://b.yzcdn.cn/${appName}/`;
}

/**
 * Log webpack errors and warnings if any
 * @param {*} stats
 * @returns {boolean} true if no errors
 */
function inspectWebpackStats(stats) {
  const messages = stats.toJson({
    assets: false,
    chunks: false,
    chunkGroups: false,
    chunkModules: false,
    entrypoints: false,
    modules: false,
    performance: false,
    timings: false,
  });

  if (messages.errors.length) {
    log.error(messages.errors.join('\n\n'));
  }

  if (messages.warnings.length) {
    log.warn(messages.warnings.join('\n\n'));
  }

  // Report child errors
  if (stats.compilation && stats.compilation.children) {
    stats.compilation.children.forEach(c => {
      if (c.errors.length) {
        log.error(c.errors.join('\n\n'));
      }
      if (c.warnings.length) {
        log.warn(c.warnings.join('\n\n'));
      }
    });
  }

  return !stats.hasErrors() && !stats.hasWarnings();
}

function isYouzanBuild() {
  return !!process.env.YOUZAN_BUILD;
}

function saveWebpackStats(stats, filename) {
  if (!isYouzanBuild()) {
    try {
      // statsJson 可能超出 nodejs 字符串的最大长度
      const statsJson = stats.toJson();
      fs.writeFileSync(getWebpackStatsPath(filename), JSON.stringify(statsJson, null, 2));
    } catch (ex) {
      log.warn('webpack stats 未保存，很大可能是内容太大了，超过了 node 允许的字符串长度');
    }
  }
}

/**
 * 判断是否为 master 分支
 */
function isMasterBranch() {
  let gitBranch = process.env.HARDWORKER_GIT_BRANCH; // fastbuild 注入的环境变量

  // 本地打包时执行 git 命令来判断
  if (typeof gitBranch === 'undefined') {
    gitBranch = ch.execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim();
  }

  return gitBranch === 'master';
}

module.exports = {
  isRegExp,
  isDev,
  isDll,
  isYouzanBuild,
  shouldMeasurePerformance,
  shouldAliasReactDOM,
  getOutputPath,
  getOutputFileNameTemplate,
  getPublicPath,
  getAppDependencies,
  getAppVersionedDependencies,
  getUserDllConfig,
  getAppRoot,
  getAppName,
  getAppBabelRC,
  getAppWebpackDevConfig,
  getAppWebpackProductionConfig,
  inspectWebpackStats,
  loadDllManifest,
  saveDllManifest,
  getWebpackStatsPath,
  saveWebpackStats,
  getCacheDirNoEnv,
  getCacheDir,
  getCacheLoaderCacheDir,
  getBabelLoaderCacheDir,
  getTerserPluginCacheDir,
  isMasterBranch,
};
