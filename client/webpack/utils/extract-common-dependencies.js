const glob = require('glob');
const path = require('path');
const fs = require('fs');
const Walker = require('node-source-walk');
const log = require('./log');
const helper = require('./helper');
const { getBabelParserOptions } = require('./node-source-walker-options');

const PAGES_DIR = path.resolve(__dirname, '../../pages');
const EXTS = ['js', 'jsx', 'ts', 'tsx'];
const PAGE_CACHE = new Map();

function extractCommonDependencies() {
  try {
    const matches = glob.sync('**/*.@(js|jsx|ts|tsx)', {
      cwd: path.resolve(__dirname, '../../'),
      ignore: ['**/node_modules/**', 'webpack/**', '*.js'],
      absolute: true,
    });

    const refCountMap = getModuleReferenceCountMap(matches);
    refCountMap.forEach(ref => {
      ref.count = reduceRefCount(ref.refs);
    });

    // they're referenced by compiler not user code
    ['@babel/runtime', 'tslib'].forEach(mod => {
      const ref = refCountMap.get(mod);
      if (ref) {
        ref.shared = true;
        ref.count = Infinity;
      }
    });

    return refCountMap;
  } catch (ex) {
    log.panic(ex.stack);
  }
}

function getModuleReferenceCountMap(modules) {
  const refCount = new Map();
  const dependencies = helper.getAppDependencies();
  dependencies.forEach(mod => {
    refCount.set(mod, { shared: false, refs: new Set() });
  });

  modules.forEach(mod => {
    try {
      const src = fs.readFileSync(mod, { encoding: 'utf-8' });
      const walker = new Walker(getBabelParserOptions(mod));
      walker.walk(src, function(node) {
        let dep;
        const nodeType = node.type;

        if (
          nodeType === 'ImportDeclaration' ||
          nodeType === 'ExportNamedDeclaration' ||
          nodeType === 'ExportAllDeclaration'
        ) {
          dep = findDependency(node.source && node.source.value, dependencies);
        } else if (nodeType === 'CallExpression') {
          if (
            // import('foobar')
            (node.callee.type === 'Import' && node.arguments.length) ||
            // require('foobar')
            (node.callee.type === 'Identifier' &&
              node.callee.name === 'require' &&
              node.arguments.length)
          ) {
            dep = findDependency(node.arguments[0].value, dependencies);
          }
        }

        if (dep) {
          const val = refCount.get(dep);
          val.refs.add(mod);
          refCount.set(dep, {
            shared: val.shared || isSharedModule(mod),
            refs: val.refs,
          });
        }
      });
    } catch (ex) {
      log.panic(`${mod}\n${ex.stack}`);
    }
  });

  return refCount;
}

function findDependency(source, dependencies) {
  if (!source) {
    return null;
  }

  for (let i = 0; i < dependencies.length; i++) {
    const mod = dependencies[i];
    if (source === mod || source.startsWith(`${mod}/`)) {
      return mod;
    }
  }

  return null;
}

function isSharedModule(mod) {
  // 非 pages 下的认为是公用代码
  return mod.indexOf('/client/pages/') === -1;
}

// 计算页面纬度的引用计数
// pages/xx 下的所有引用算一个引用
// 非 pages 下的所有引用按 ref 数量算，不做页面纬度的去重
function reduceRefCount(refs) {
  const refsDedup = new Set();
  for (const mod of refs) {
    const page = findPageName(mod);
    // console.log(`${mod} => ${page}`);
    refsDedup.add(page);
  }

  return refsDedup.size;
}

function findPageName(mod) {
  // 非pages下面的不去重
  if (!mod.startsWith(PAGES_DIR)) {
    return mod;
  }

  let dir = PAGES_DIR;
  const parts = mod.replace(PAGES_DIR, '').split('/');
  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    if (!part) {
      continue;
    }

    dir = path.join(dir, part);
    if (PAGE_CACHE.get(dir)) {
      return dir;
    }

    for (let j = 0; j < EXTS.length; j++) {
      const ext = EXTS[j];
      const file = path.join(dir, `main.${ext}`);
      if (fs.existsSync(file)) {
        PAGE_CACHE.set(dir, true);
        return dir;
      }
    }
    PAGE_CACHE.set(dir, false);
  }
}

module.exports = extractCommonDependencies;
