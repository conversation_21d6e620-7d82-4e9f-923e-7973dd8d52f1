function getBabelParserOptions(filename) {
  const enableTypeScript = ['.tsx', '.ts'].some(ext => filename.endsWith(ext));
  const enableJSX = ['.tsx', '.js', '.jsx'].some(ext => filename.endsWith(ext));
  const opts = {
    sourceType: 'module',
    sourceFilename: filename,
    plugins: [
      'asyncGenerators',
      'bigInt',
      'classPrivateMethods',
      'classPrivateProperties',
      'classProperties',
      ['decorators', { decoratorsBeforeExport: true }],
      'doExpressions',
      'dynamicImport',
      'exportDefaultFrom',
      'exportNamespaceFrom',
      'functionBind',
      'functionSent',
      'importMeta',
      'logicalAssignment',
      'nullishCoalescingOperator',
      'numericSeparator',
      'objectRestSpread',
      'optionalCatchBinding',
      'optionalChaining',
      'partialApplication',
      // 'pipelineOperator', // we have to choose a proposal, better off now
      'throwExpressions',
    ],
  };

  // Don't blindly enable both typescript and jsx.
  // When the JSX plugin is on, type assertions (`<T> x`) aren't valid syntax.
  if (enableTypeScript) {
    opts.plugins.unshift('typescript');
  }

  if (enableJSX) {
    opts.plugins.unshift('jsx');
  }

  return opts;
}

module.exports = {
  getBabelParserOptions,
};
