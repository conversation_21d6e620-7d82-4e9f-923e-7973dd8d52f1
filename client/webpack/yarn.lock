# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.5.5.tgz#bc0782f6d69f7b7d49531219699b988f669a8f9d"
  integrity sha1-vAeC9tafe31JUxIZaZuYj2aaj50=
  dependencies:
    "@babel/highlight" "^7.0.0"

"@babel/code-frame@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/code-frame/download/@babel/code-frame-7.8.3.tgz#33e25903d7481181534e12ec0a25f16b6fcf419e"
  integrity sha1-M+<PERSON><PERSON>A9dIEYFTThLsCiXxa2/PQZ4=
  dependencies:
    "@babel/highlight" "^7.8.3"

"@babel/core@^7.1.0":
  version "7.7.2"
  resolved "http://registry.npm.qima-inc.com/@babel/core/download/@babel/core-7.7.2.tgz?cache=0&sync_timestamp=1573083631493&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fcore%2Fdownload%2F%40babel%2Fcore-7.7.2.tgz#ea5b99693bcfc058116f42fa1dd54da412b29d91"
  integrity sha1-6luZaTvPwFgRb0L6HdVNpBKynZE=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.7.2"
    "@babel/helpers" "^7.7.0"
    "@babel/parser" "^7.7.2"
    "@babel/template" "^7.7.0"
    "@babel/traverse" "^7.7.2"
    "@babel/types" "^7.7.2"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    json5 "^2.1.0"
    lodash "^4.17.13"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/core@^7.5.4":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/core/download/@babel/core-7.6.0.tgz#9b00f73554edd67bebc86df8303ef678be3d7b48"
  integrity sha1-mwD3NVTt1nvryG34MD72eL49e0g=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.6.0"
    "@babel/helpers" "^7.6.0"
    "@babel/parser" "^7.6.0"
    "@babel/template" "^7.6.0"
    "@babel/traverse" "^7.6.0"
    "@babel/types" "^7.6.0"
    convert-source-map "^1.1.0"
    debug "^4.1.0"
    json5 "^2.1.0"
    lodash "^4.17.13"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/core@^7.7.5":
  version "7.8.7"
  resolved "http://registry.npm.qima-inc.com/@babel/core/download/@babel/core-7.8.7.tgz#b69017d221ccdeb203145ae9da269d72cf102f3b"
  integrity sha1-tpAX0iHM3rIDFFrp2iadcs8QLzs=
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@babel/generator" "^7.8.7"
    "@babel/helpers" "^7.8.4"
    "@babel/parser" "^7.8.7"
    "@babel/template" "^7.8.6"
    "@babel/traverse" "^7.8.6"
    "@babel/types" "^7.8.7"
    convert-source-map "^1.7.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.1"
    json5 "^2.1.0"
    lodash "^4.17.13"
    resolve "^1.3.2"
    semver "^5.4.1"
    source-map "^0.5.0"

"@babel/generator@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/generator/download/@babel/generator-7.6.0.tgz#e2c21efbfd3293ad819a2359b448f002bfdfda56"
  integrity sha1-4sIe+/0yk62BmiNZtEjwAr/f2lY=
  dependencies:
    "@babel/types" "^7.6.0"
    jsesc "^2.5.1"
    lodash "^4.17.13"
    source-map "^0.5.0"
    trim-right "^1.0.1"

"@babel/generator@^7.7.2":
  version "7.7.2"
  resolved "http://registry.npm.qima-inc.com/@babel/generator/download/@babel/generator-7.7.2.tgz?cache=0&sync_timestamp=1573082874917&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fgenerator%2Fdownload%2F%40babel%2Fgenerator-7.7.2.tgz#2f4852d04131a5e17ea4f6645488b5da66ebf3af"
  integrity sha1-L0hS0EExpeF+pPZkVIi12mbr868=
  dependencies:
    "@babel/types" "^7.7.2"
    jsesc "^2.5.1"
    lodash "^4.17.13"
    source-map "^0.5.0"

"@babel/generator@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/generator/download/@babel/generator-7.7.4.tgz#db651e2840ca9aa66f327dcec1dc5f5fa9611369"
  integrity sha1-22UeKEDKmqZvMn3OwdxfX6lhE2k=
  dependencies:
    "@babel/types" "^7.7.4"
    jsesc "^2.5.1"
    lodash "^4.17.13"
    source-map "^0.5.0"

"@babel/generator@^7.8.6", "@babel/generator@^7.8.7":
  version "7.8.8"
  resolved "http://registry.npm.qima-inc.com/@babel/generator/download/@babel/generator-7.8.8.tgz#cdcd58caab730834cee9eeadb729e833b625da3e"
  integrity sha1-zc1YyqtzCDTO6e6ttynoM7Yl2j4=
  dependencies:
    "@babel/types" "^7.8.7"
    jsesc "^2.5.1"
    lodash "^4.17.13"
    source-map "^0.5.0"

"@babel/helper-annotate-as-pure@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.0.0.tgz#323d39dd0b50e10c7c06ca7d7638e6864d8c5c32"
  integrity sha1-Mj053QtQ4Qx8Bsp9djjmhk2MXDI=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-builder-binary-assignment-operator-visitor@^7.1.0":
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.1.0.tgz#6b69628dfe4087798e0c4ed98e3d4a6b2fbd2f5f"
  integrity sha1-a2lijf5Ah3mODE7Zjj1Kay+9L18=
  dependencies:
    "@babel/helper-explode-assignable-expression" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-builder-react-jsx@^7.3.0":
  version "7.3.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-builder-react-jsx/download/@babel/helper-builder-react-jsx-7.3.0.tgz#a1ac95a5d2b3e88ae5e54846bf462eeb81b318a4"
  integrity sha1-oayVpdKz6Irl5UhGv0Yu64GzGKQ=
  dependencies:
    "@babel/types" "^7.3.0"
    esutils "^2.0.0"

"@babel/helper-call-delegate@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-call-delegate/download/@babel/helper-call-delegate-7.4.4.tgz#87c1f8ca19ad552a736a7a27b1c1fcf8b1ff1f43"
  integrity sha1-h8H4yhmtVSpzanonscH8+LH/H0M=
  dependencies:
    "@babel/helper-hoist-variables" "^7.4.4"
    "@babel/traverse" "^7.4.4"
    "@babel/types" "^7.4.4"

"@babel/helper-create-class-features-plugin@^7.5.5", "@babel/helper-create-class-features-plugin@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.6.0.tgz#769711acca889be371e9bc2eb68641d55218021f"
  integrity sha1-dpcRrMqIm+Nx6bwutoZB1VIYAh8=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-member-expression-to-functions" "^7.5.5"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.5.5"
    "@babel/helper-split-export-declaration" "^7.4.4"

"@babel/helper-define-map@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-define-map/download/@babel/helper-define-map-7.5.5.tgz#3dec32c2046f37e09b28c93eb0b103fd2a25d369"
  integrity sha1-PewywgRvN+CbKMk+sLED/Sol02k=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/types" "^7.5.5"
    lodash "^4.17.13"

"@babel/helper-explode-assignable-expression@^7.1.0":
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.1.0.tgz#537fa13f6f1674df745b0c00ec8fe4e99681c8f6"
  integrity sha1-U3+hP28WdN90WwwA7I/k6ZaByPY=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-function-name@^7.1.0":
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.1.0.tgz#a0ceb01685f73355d4360c1247f582bfafc8ff53"
  integrity sha1-oM6wFoX3M1XUNgwSR/WCv6/I/1M=
  dependencies:
    "@babel/helper-get-function-arity" "^7.0.0"
    "@babel/template" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-function-name@^7.7.0":
  version "7.7.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.7.0.tgz#44a5ad151cfff8ed2599c91682dda2ec2c8430a3"
  integrity sha1-RKWtFRz/+O0lmckWgt2i7CyEMKM=
  dependencies:
    "@babel/helper-get-function-arity" "^7.7.0"
    "@babel/template" "^7.7.0"
    "@babel/types" "^7.7.0"

"@babel/helper-function-name@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.7.4.tgz?cache=0&sync_timestamp=1574465630791&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Fhelper-function-name%2Fdownload%2F%40babel%2Fhelper-function-name-7.7.4.tgz#ab6e041e7135d436d8f0a3eca15de5b67a341a2e"
  integrity sha1-q24EHnE11DbY8KPsoV3ltno0Gi4=
  dependencies:
    "@babel/helper-get-function-arity" "^7.7.4"
    "@babel/template" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/helper-function-name@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-function-name/download/@babel/helper-function-name-7.8.3.tgz#eeeb665a01b1f11068e9fb86ad56a1cb1a824cca"
  integrity sha1-7utmWgGx8RBo6fuGrVahyxqCTMo=
  dependencies:
    "@babel/helper-get-function-arity" "^7.8.3"
    "@babel/template" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helper-get-function-arity@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0.tgz#83572d4320e2a4657263734113c42868b64e49c3"
  integrity sha1-g1ctQyDipGVyY3NBE8QoaLZOScM=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-get-function-arity@^7.7.0":
  version "7.7.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.7.0.tgz#c604886bc97287a1d1398092bc666bc3d7d7aa2d"
  integrity sha1-xgSIa8lyh6HROYCSvGZrw9fXqi0=
  dependencies:
    "@babel/types" "^7.7.0"

"@babel/helper-get-function-arity@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.7.4.tgz#cb46348d2f8808e632f0ab048172130e636005f0"
  integrity sha1-y0Y0jS+ICOYy8KsEgXITDmNgBfA=
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-get-function-arity@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.8.3.tgz#b894b947bd004381ce63ea1db9f08547e920abd5"
  integrity sha1-uJS5R70AQ4HOY+odufCFR+kgq9U=
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-hoist-variables@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.4.4.tgz#0298b5f25c8c09c53102d52ac4a98f773eb2850a"
  integrity sha1-Api18lyMCcUxAtUqxKmPdz6yhQo=
  dependencies:
    "@babel/types" "^7.4.4"

"@babel/helper-member-expression-to-functions@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.5.5.tgz#1fb5b8ec4453a93c439ee9fe3aeea4a84b76b590"
  integrity sha1-H7W47ERTqTxDnun+Ou6kqEt2tZA=
  dependencies:
    "@babel/types" "^7.5.5"

"@babel/helper-member-expression-to-functions@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.8.3.tgz#659b710498ea6c1d9907e0c73f206eee7dadc24c"
  integrity sha1-ZZtxBJjqbB2ZB+DHPyBu7n2twkw=
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-module-imports@^7.0.0", "@babel/helper-module-imports@^7.0.0-beta.49":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.0.0.tgz#96081b7111e486da4d2cd971ad1a4fe216cc2e3d"
  integrity sha1-lggbcRHkhtpNLNlxrRpP4hbMLj0=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-module-imports@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.8.3.tgz#7fe39589b39c016331b6b8c3f441e8f0b1419498"
  integrity sha1-f+OVibOcAWMxtrjD9EHo8LFBlJg=
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-module-transforms@^7.1.0", "@babel/helper-module-transforms@^7.4.4":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.5.5.tgz#f84ff8a09038dcbca1fd4355661a500937165b4a"
  integrity sha1-+E/4oJA43Lyh/UNVZhpQCTcWW0o=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-simple-access" "^7.1.0"
    "@babel/helper-split-export-declaration" "^7.4.4"
    "@babel/template" "^7.4.4"
    "@babel/types" "^7.5.5"
    lodash "^4.17.13"

"@babel/helper-module-transforms@^7.9.0":
  version "7.9.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.9.0.tgz#43b34dfe15961918707d247327431388e9fe96e5"
  integrity sha1-Q7NN/hWWGRhwfSRzJ0MTiOn+luU=
  dependencies:
    "@babel/helper-module-imports" "^7.8.3"
    "@babel/helper-replace-supers" "^7.8.6"
    "@babel/helper-simple-access" "^7.8.3"
    "@babel/helper-split-export-declaration" "^7.8.3"
    "@babel/template" "^7.8.6"
    "@babel/types" "^7.9.0"
    lodash "^4.17.13"

"@babel/helper-optimise-call-expression@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.0.0.tgz#a2920c5702b073c15de51106200aa8cad20497d5"
  integrity sha1-opIMVwKwc8Fd5REGIAqoytIEl9U=
  dependencies:
    "@babel/types" "^7.0.0"

"@babel/helper-optimise-call-expression@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.8.3.tgz#7ed071813d09c75298ef4f208956006b6111ecb9"
  integrity sha1-ftBxgT0Jx1KY708giVYAa2ER7Lk=
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-plugin-utils@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.0.0.tgz#bbb3fbee98661c569034237cc03967ba99b4f250"
  integrity sha1-u7P77phmHFaQNCN8wDlnupm08lA=

"@babel/helper-plugin-utils@^7.8.0", "@babel/helper-plugin-utils@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.8.3.tgz#9ea293be19babc0f52ff8ca88b34c3611b208670"
  integrity sha1-nqKTvhm6vA9S/4yoizTDYRsghnA=

"@babel/helper-regex@^7.0.0", "@babel/helper-regex@^7.4.4":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-regex/download/@babel/helper-regex-7.5.5.tgz#0aa6824f7100a2e0e89c1527c23936c152cab351"
  integrity sha1-CqaCT3EAouDonBUnwjk2wVLKs1E=
  dependencies:
    lodash "^4.17.13"

"@babel/helper-remap-async-to-generator@^7.1.0":
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.1.0.tgz#361d80821b6f38da75bd3f0785ece20a88c5fe7f"
  integrity sha1-Nh2AghtvONp1vT8HheziCojF/n8=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.0.0"
    "@babel/helper-wrap-function" "^7.1.0"
    "@babel/template" "^7.1.0"
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-replace-supers@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.5.5.tgz#f84ce43df031222d2bad068d2626cb5799c34bc2"
  integrity sha1-+EzkPfAxIi0rrQaNJibLV5nDS8I=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.5.5"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/traverse" "^7.5.5"
    "@babel/types" "^7.5.5"

"@babel/helper-replace-supers@^7.8.6":
  version "7.8.6"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.8.6.tgz#5ada744fd5ad73203bf1d67459a27dcba67effc8"
  integrity sha1-Wtp0T9WtcyA78dZ0WaJ9y6Z+/8g=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.8.3"
    "@babel/helper-optimise-call-expression" "^7.8.3"
    "@babel/traverse" "^7.8.6"
    "@babel/types" "^7.8.6"

"@babel/helper-simple-access@^7.1.0":
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.1.0.tgz#65eeb954c8c245beaa4e859da6188f39d71e585c"
  integrity sha1-Ze65VMjCRb6qToWdphiPOdceWFw=
  dependencies:
    "@babel/template" "^7.1.0"
    "@babel/types" "^7.0.0"

"@babel/helper-simple-access@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-simple-access/download/@babel/helper-simple-access-7.8.3.tgz#7f8109928b4dab4654076986af575231deb639ae"
  integrity sha1-f4EJkotNq0ZUB2mGr1dSMd62Oa4=
  dependencies:
    "@babel/template" "^7.8.3"
    "@babel/types" "^7.8.3"

"@babel/helper-split-export-declaration@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.4.4.tgz#ff94894a340be78f53f06af038b205c49d993677"
  integrity sha1-/5SJSjQL549T8GrwOLIFxJ2ZNnc=
  dependencies:
    "@babel/types" "^7.4.4"

"@babel/helper-split-export-declaration@^7.7.0":
  version "7.7.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.7.0.tgz#1365e74ea6c614deeb56ebffabd71006a0eb2300"
  integrity sha1-E2XnTqbGFN7rVuv/q9cQBqDrIwA=
  dependencies:
    "@babel/types" "^7.7.0"

"@babel/helper-split-export-declaration@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.7.4.tgz#57292af60443c4a3622cf74040ddc28e68336fd8"
  integrity sha1-Vykq9gRDxKNiLPdAQN3Cjmgzb9g=
  dependencies:
    "@babel/types" "^7.7.4"

"@babel/helper-split-export-declaration@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.8.3.tgz#31a9f30070f91368a7182cf05f831781065fc7a9"
  integrity sha1-ManzAHD5E2inGCzwX4MXgQZfx6k=
  dependencies:
    "@babel/types" "^7.8.3"

"@babel/helper-validator-identifier@^7.9.5":
  version "7.9.5"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.9.5.tgz#90977a8e6fbf6b431a7dc31752eee233bf052d80"
  integrity sha1-kJd6jm+/a0MafcMXUu7iM78FLYA=

"@babel/helper-wrap-function@^7.1.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.2.0.tgz#c4e0012445769e2815b55296ead43a958549f6fa"
  integrity sha1-xOABJEV2nigVtVKW6tQ6lYVJ9vo=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/template" "^7.1.0"
    "@babel/traverse" "^7.1.0"
    "@babel/types" "^7.2.0"

"@babel/helpers@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helpers/download/@babel/helpers-7.6.0.tgz#21961d16c6a3c3ab597325c34c465c0887d31c6e"
  integrity sha1-IZYdFsajw6tZcyXDTEZcCIfTHG4=
  dependencies:
    "@babel/template" "^7.6.0"
    "@babel/traverse" "^7.6.0"
    "@babel/types" "^7.6.0"

"@babel/helpers@^7.7.0":
  version "7.7.0"
  resolved "http://registry.npm.qima-inc.com/@babel/helpers/download/@babel/helpers-7.7.0.tgz#359bb5ac3b4726f7c1fde0ec75f64b3f4275d60b"
  integrity sha1-NZu1rDtHJvfB/eDsdfZLP0J11gs=
  dependencies:
    "@babel/template" "^7.7.0"
    "@babel/traverse" "^7.7.0"
    "@babel/types" "^7.7.0"

"@babel/helpers@^7.8.4":
  version "7.8.4"
  resolved "http://registry.npm.qima-inc.com/@babel/helpers/download/@babel/helpers-7.8.4.tgz#754eb3ee727c165e0a240d6c207de7c455f36f73"
  integrity sha1-dU6z7nJ8Fl4KJA1sIH3nxFXzb3M=
  dependencies:
    "@babel/template" "^7.8.3"
    "@babel/traverse" "^7.8.4"
    "@babel/types" "^7.8.3"

"@babel/highlight@^7.0.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/highlight/download/@babel/highlight-7.5.0.tgz#56d11312bd9248fa619591d02472be6e8cb32540"
  integrity sha1-VtETEr2SSPphlZHQJHK+boyzJUA=
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^4.0.0"

"@babel/highlight@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/highlight/download/@babel/highlight-7.8.3.tgz#28f173d04223eaaa59bc1d439a3836e6d1265797"
  integrity sha1-KPFz0EIj6qpZvB1Dmjg25tEmV5c=
  dependencies:
    chalk "^2.0.0"
    esutils "^2.0.2"
    js-tokens "^4.0.0"

"@babel/parser@^7.0.0", "@babel/parser@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.6.0.tgz#3e05d0647432a8326cb28d0de03895ae5a57f39b"
  integrity sha1-PgXQZHQyqDJsso0N4DiVrlpX85s=

"@babel/parser@^7.1.0", "@babel/parser@^7.7.0", "@babel/parser@^7.7.2":
  version "7.7.3"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.7.3.tgz#5fad457c2529de476a248f75b0f090b3060af043"
  integrity sha1-X61FfCUp3kdqJI91sPCQswYK8EM=

"@babel/parser@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.7.4.tgz#75ab2d7110c2cf2fa949959afb05fa346d2231bb"
  integrity sha1-dastcRDCzy+pSZWa+wX6NG0iMbs=

"@babel/parser@^7.7.5", "@babel/parser@^7.8.6", "@babel/parser@^7.8.7":
  version "7.8.8"
  resolved "http://registry.npm.qima-inc.com/@babel/parser/download/@babel/parser-7.8.8.tgz#4c3b7ce36db37e0629be1f0d50a571d2f86f6cd4"
  integrity sha1-TDt8422zfgYpvh8NUKVx0vhvbNQ=

"@babel/plugin-proposal-async-generator-functions@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.2.0.tgz#b289b306669dce4ad20b0252889a15768c9d417e"
  integrity sha1-somzBmadzkrSCwJSiJoVdoydQX4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-remap-async-to-generator" "^7.1.0"
    "@babel/plugin-syntax-async-generators" "^7.2.0"

"@babel/plugin-proposal-class-properties@^7.5.0":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.5.5.tgz#a974cfae1e37c3110e71f3c6a2e48b8e71958cd4"
  integrity sha1-qXTPrh43wxEOcfPGouSLjnGVjNQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.5.5"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-proposal-decorators@^7.4.4":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.6.0.tgz#6659d2572a17d70abd68123e89a12a43d90aa30c"
  integrity sha1-ZlnSVyoX1wq9aBI+iaEqQ9kKoww=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.6.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-decorators" "^7.2.0"

"@babel/plugin-proposal-dynamic-import@^7.5.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-dynamic-import/download/@babel/plugin-proposal-dynamic-import-7.5.0.tgz#e532202db4838723691b10a67b8ce509e397c506"
  integrity sha1-5TIgLbSDhyNpGxCme4zlCeOXxQY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-dynamic-import" "^7.2.0"

"@babel/plugin-proposal-export-default-from@^7.5.2":
  version "7.5.2"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.5.2.tgz#2c0ac2dcc36e3b2443fead2c3c5fc796fb1b5145"
  integrity sha1-LArC3MNuOyRD/q0sPF/HlvsbUUU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-export-default-from" "^7.2.0"

"@babel/plugin-proposal-export-namespace-from@^7.5.2":
  version "7.5.2"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-export-namespace-from/download/@babel/plugin-proposal-export-namespace-from-7.5.2.tgz#ccd5ed05b06d700688ff1db01a9dd27155e0d2a0"
  integrity sha1-zNXtBbBtcAaI/x2wGp3ScVXg0qA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-export-namespace-from" "^7.2.0"

"@babel/plugin-proposal-json-strings@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.2.0.tgz#568ecc446c6148ae6b267f02551130891e29f317"
  integrity sha1-Vo7MRGxhSK5rJn8CVREwiR4p8xc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"

"@babel/plugin-proposal-nullish-coalescing-operator@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-nullish-coalescing-operator/download/@babel/plugin-proposal-nullish-coalescing-operator-7.7.4.tgz#7db302c83bc30caa89e38fee935635ef6bd11c28"
  integrity sha1-fbMCyDvDDKqJ44/uk1Y172vRHCg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.7.4"

"@babel/plugin-proposal-object-rest-spread@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.5.5.tgz#61939744f71ba76a3ae46b5eea18a54c16d22e58"
  integrity sha1-YZOXRPcbp2o65Gte6hilTBbSLlg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"

"@babel/plugin-proposal-optional-catch-binding@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.2.0.tgz#135d81edb68a081e55e56ec48541ece8065c38f5"
  integrity sha1-E12B7baKCB5V5W7EhUHs6AZcOPU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"

"@babel/plugin-proposal-optional-chaining@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-optional-chaining/download/@babel/plugin-proposal-optional-chaining-7.7.4.tgz#3f04c2de1a942cbd3008324df8144b9cbc0ca0ba"
  integrity sha1-PwTC3hqULL0wCDJN+BRLnLwMoLo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-optional-chaining" "^7.7.4"

"@babel/plugin-proposal-unicode-property-regex@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.4.4.tgz#501ffd9826c0b91da22690720722ac7cb1ca9c78"
  integrity sha1-UB/9mCbAuR2iJpByByKsfLHKnHg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.4.4"
    regexpu-core "^4.5.4"

"@babel/plugin-syntax-async-generators@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.2.0.tgz#69e1f0db34c6f5a0cf7e2b3323bf159a76c8cb7f"
  integrity sha1-aeHw2zTG9aDPfiszI78VmnbIy38=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.0.0", "@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-bigint/download/@babel/plugin-syntax-bigint-7.8.3.tgz#4c9a6f669f5d0cdf1b90a1671e9a146be5300cea"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-class-properties/download/@babel/plugin-syntax-class-properties-7.8.3.tgz#6cb933a8872c8d359bfde69bbeaae5162fd1e8f7"
  integrity sha1-bLkzqIcsjTWb/eabvqrlFi/R6Pc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-decorators@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.2.0.tgz#c50b1b957dcc69e4b1127b65e1c33eef61570c1b"
  integrity sha1-xQsblX3MaeSxEntl4cM+72FXDBs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-dynamic-import@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.2.0.tgz#69c159ffaf4998122161ad8ebc5e6d1f55df8612"
  integrity sha1-acFZ/69JmBIhYa2OvF5tH1XfhhI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-export-default-from@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-export-default-from/download/@babel/plugin-syntax-export-default-from-7.2.0.tgz#edd83b7adc2e0d059e2467ca96c650ab6d2f3820"
  integrity sha1-7dg7etwuDQWeJGfKlsZQq20vOCA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-export-namespace-from@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-export-namespace-from/download/@babel/plugin-syntax-export-namespace-from-7.2.0.tgz#8d257838c6b3b779db52c0224443459bd27fb039"
  integrity sha1-jSV4OMazt3nbUsAiRENFm9J/sDk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-json-strings@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.2.0.tgz#72bd13f6ffe1d25938129d2a186b11fd62951470"
  integrity sha1-cr0T9v/h0lk4Ep0qGGsR/WKVFHA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.2.0.tgz#0b85a3b4bc7cdf4cc4b8bf236335b907ca22e7c7"
  integrity sha1-C4WjtLx830zEuL8jYzW5B8oi58c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-logical-assignment-operators@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-logical-assignment-operators/download/@babel/plugin-syntax-logical-assignment-operators-7.8.3.tgz#3995d7d7ffff432f6ddc742b47e730c054599897"
  integrity sha1-OZXX1///Qy9t3HQrR+cwwFRZmJc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.7.4.tgz#e53b751d0c3061b1ba3089242524b65a7a9da12b"
  integrity sha1-5Tt1HQwwYbG6MIkkJSS2WnqdoSs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-nullish-coalescing-operator/download/@babel/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-numeric-separator/download/@babel/plugin-syntax-numeric-separator-7.8.3.tgz#0e3fb63e09bea1b11e96467271c8308007e7c41f"
  integrity sha1-Dj+2Pgm+obEelkZyccgwgAfnxB8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.3"

"@babel/plugin-syntax-object-rest-spread@^7.0.0", "@babel/plugin-syntax-object-rest-spread@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.2.0.tgz#3b7a3e733510c57e820b9142a6579ac8b0dfad2e"
  integrity sha1-O3o+czUQxX6CC5FCpleayLDfrS4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.2.0.tgz#a94013d6eda8908dfe6a477e7f9eda85656ecf5c"
  integrity sha1-qUAT1u2okI3+akd+f57ahWVuz1w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.7.4.tgz#c91fdde6de85d2eb8906daea7b21944c3610c901"
  integrity sha1-yR/d5t6F0uuJBtrqeyGUTDYQyQE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-syntax-optional-chaining/download/@babel/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-transform-arrow-functions@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.2.0.tgz#9aeafbe4d6ffc6563bf8f8372091628f00779550"
  integrity sha1-mur75Nb/xlY7+Pg3IJFijwB3lVA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-async-to-generator@^7.5.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.5.0.tgz#89a3848a0166623b5bc481164b5936ab947e887e"
  integrity sha1-iaOEigFmYjtbxIEWS1k2q5R+iH4=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-remap-async-to-generator" "^7.1.0"

"@babel/plugin-transform-block-scoped-functions@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.2.0.tgz#5d3cc11e8d5ddd752aa64c9148d0db6cb79fd190"
  integrity sha1-XTzBHo1d3XUqpkyRSNDbbLef0ZA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-block-scoping@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.6.0.tgz#c49e21228c4bbd4068a35667e6d951c75439b1dc"
  integrity sha1-xJ4hIoxLvUBoo1Zn5tlRx1Q5sdw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    lodash "^4.17.13"

"@babel/plugin-transform-classes@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.5.5.tgz#d094299d9bd680a14a2a0edae38305ad60fb4de9"
  integrity sha1-0JQpnZvWgKFKKg7a44MFrWD7Tek=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.0.0"
    "@babel/helper-define-map" "^7.5.5"
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-optimise-call-expression" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.5.5"
    "@babel/helper-split-export-declaration" "^7.4.4"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.2.0.tgz#83a7df6a658865b1c8f641d510c6f3af220216da"
  integrity sha1-g6ffamWIZbHI9kHVEMbzryICFto=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-destructuring@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.6.0.tgz#44bbe08b57f4480094d57d9ffbcd96d309075ba6"
  integrity sha1-RLvgi1f0SACU1X2f+82W0wkHW6Y=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-dotall-regex@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.4.4.tgz#361a148bc951444312c69446d76ed1ea8e4450c3"
  integrity sha1-NhoUi8lRREMSxpRG127R6o5EUMM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.4.4"
    regexpu-core "^4.5.4"

"@babel/plugin-transform-duplicate-keys@^7.5.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.5.0.tgz#c5dbf5106bf84cdf691222c0974c12b1df931853"
  integrity sha1-xdv1EGv4TN9pEiLAl0wSsd+TGFM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-exponentiation-operator@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.2.0.tgz#a63868289e5b4007f7054d46491af51435766008"
  integrity sha1-pjhoKJ5bQAf3BU1GSRr1FDV2YAg=
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-for-of@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.4.4.tgz#0267fc735e24c808ba173866c6c4d1440fc3c556"
  integrity sha1-Amf8c14kyAi6FzhmxsTRRA/DxVY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-function-name@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.4.4.tgz#e1436116abb0610c2259094848754ac5230922ad"
  integrity sha1-4UNhFquwYQwiWQlISHVKxSMJIq0=
  dependencies:
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-literals@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.2.0.tgz#690353e81f9267dad4fd8cfd77eafa86aba53ea1"
  integrity sha1-aQNT6B+SZ9rU/Yz9d+r6hqulPqE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-member-expression-literals@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.2.0.tgz#fa10aa5c58a2cb6afcf2c9ffa8cb4d8b3d489a2d"
  integrity sha1-+hCqXFiiy2r88sn/qMtNiz1Imi0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-modules-amd@^7.5.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.5.0.tgz#ef00435d46da0a5961aa728a1d2ecff063e4fb91"
  integrity sha1-7wBDXUbaCllhqnKKHS7P8GPk+5E=
  dependencies:
    "@babel/helper-module-transforms" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-commonjs@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.6.0.tgz#39dfe957de4420445f1fcf88b68a2e4aa4515486"
  integrity sha1-Od/pV95EIERfH8+ItoouSqRRVIY=
  dependencies:
    "@babel/helper-module-transforms" "^7.4.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-simple-access" "^7.1.0"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-commonjs@^7.9.0":
  version "7.9.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.9.0.tgz#e3e72f4cbc9b4a260e30be0ea59bdf5a39748940"
  integrity sha1-4+cvTLybSiYOML4OpZvfWjl0iUA=
  dependencies:
    "@babel/helper-module-transforms" "^7.9.0"
    "@babel/helper-plugin-utils" "^7.8.3"
    "@babel/helper-simple-access" "^7.8.3"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-systemjs@^7.5.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.5.0.tgz#e75266a13ef94202db2a0620977756f51d52d249"
  integrity sha1-51JmoT75QgLbKgYgl3dW9R1S0kk=
  dependencies:
    "@babel/helper-hoist-variables" "^7.4.4"
    "@babel/helper-plugin-utils" "^7.0.0"
    babel-plugin-dynamic-import-node "^2.3.0"

"@babel/plugin-transform-modules-umd@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.2.0.tgz#7678ce75169f0877b8eb2235538c074268dd01ae"
  integrity sha1-dnjOdRafCHe46yI1U4wHQmjdAa4=
  dependencies:
    "@babel/helper-module-transforms" "^7.1.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-named-capturing-groups-regex@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.6.0.tgz#1e6e663097813bb4f53d42df0750cf28ad3bb3f1"
  integrity sha1-Hm5mMJeBO7T1PULfB1DPKK07s/E=
  dependencies:
    regexp-tree "^0.1.13"

"@babel/plugin-transform-new-target@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.4.4.tgz#18d120438b0cc9ee95a47f2c72bc9768fbed60a5"
  integrity sha1-GNEgQ4sMye6VpH8scryXaPvtYKU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-object-super@^7.5.5":
  version "7.5.5"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.5.5.tgz#c70021df834073c65eb613b8679cc4a381d1a9f9"
  integrity sha1-xwAh34NAc8ZethO4Z5zEo4HRqfk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-replace-supers" "^7.5.5"

"@babel/plugin-transform-parameters@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.4.4.tgz#7556cf03f318bd2719fe4c922d2d808be5571e16"
  integrity sha1-dVbPA/MYvScZ/kySLS2Ai+VXHhY=
  dependencies:
    "@babel/helper-call-delegate" "^7.4.4"
    "@babel/helper-get-function-arity" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-property-literals@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.2.0.tgz#03e33f653f5b25c4eb572c98b9485055b389e905"
  integrity sha1-A+M/ZT9bJcTrVyyYuUhQVbOJ6QU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-react-display-name@^7.0.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.2.0.tgz#ebfaed87834ce8dc4279609a4f0c324c156e3eb0"
  integrity sha1-6/rth4NM6NxCeWCaTwwyTBVuPrA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-react-jsx-self@^7.0.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.2.0.tgz#461e21ad9478f1031dd5e276108d027f1b5240ba"
  integrity sha1-Rh4hrZR48QMd1eJ2EI0CfxtSQLo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@babel/plugin-transform-react-jsx-source@^7.0.0":
  version "7.5.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.5.0.tgz#583b10c49cf057e237085bcbd8cc960bd83bd96b"
  integrity sha1-WDsQxJzwV+I3CFvL2MyWC9g72Ws=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@babel/plugin-transform-react-jsx@^7.0.0":
  version "7.3.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.3.0.tgz#f2cab99026631c767e2745a5368b331cfe8f5290"
  integrity sha1-8sq5kCZjHHZ+J0WlNoszHP6PUpA=
  dependencies:
    "@babel/helper-builder-react-jsx" "^7.3.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-syntax-jsx" "^7.2.0"

"@babel/plugin-transform-regenerator@^7.4.5":
  version "7.4.5"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.4.5.tgz#629dc82512c55cee01341fb27bdfcb210354680f"
  integrity sha1-Yp3IJRLFXO4BNB+ye9/LIQNUaA8=
  dependencies:
    regenerator-transform "^0.14.0"

"@babel/plugin-transform-reserved-words@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.2.0.tgz#4792af87c998a49367597d07fedf02636d2e1634"
  integrity sha1-R5Kvh8mYpJNnWX0H/t8CY20uFjQ=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-runtime@^7.5.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.6.0.tgz#85a3cce402b28586138e368fce20ab3019b9713e"
  integrity sha1-haPM5AKyhYYTjjaPziCrMBm5cT4=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    resolve "^1.8.1"
    semver "^5.5.1"

"@babel/plugin-transform-shorthand-properties@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.2.0.tgz#6333aee2f8d6ee7e28615457298934a3b46198f0"
  integrity sha1-YzOu4vjW7n4oYVRXKYk0o7RhmPA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-spread@^7.2.0":
  version "7.2.2"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.2.2.tgz#3103a9abe22f742b6d406ecd3cd49b774919b406"
  integrity sha1-MQOpq+IvdCttQG7NPNSbd0kZtAY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-sticky-regex@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.2.0.tgz#a1e454b5995560a9c1e0d537dfc15061fd2687e1"
  integrity sha1-oeRUtZlVYKnB4NU338FQYf0mh+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.0.0"

"@babel/plugin-transform-template-literals@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.4.4.tgz#9d28fea7bbce637fb7612a0750989d8321d4bcb0"
  integrity sha1-nSj+p7vOY3+3YSoHUJidgyHUvLA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-typeof-symbol@^7.2.0":
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.2.0.tgz#117d2bcec2fbf64b4b59d1f9819894682d29f2b2"
  integrity sha1-EX0rzsL79ktLWdH5gZiUaC0p8rI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"

"@babel/plugin-transform-unicode-regex@^7.4.4":
  version "7.4.4"
  resolved "http://registry.npm.qima-inc.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.4.4.tgz#ab4634bb4f14d36728bf5978322b35587787970f"
  integrity sha1-q0Y0u08U02cov1l4Mis1WHeHlw8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/helper-regex" "^7.4.4"
    regexpu-core "^4.5.4"

"@babel/preset-env@^7.5.4":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/preset-env/download/@babel/preset-env-7.6.0.tgz#aae4141c506100bb2bfaa4ac2a5c12b395619e50"
  integrity sha1-quQUHFBhALsr+qSsKlwSs5VhnlA=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-proposal-async-generator-functions" "^7.2.0"
    "@babel/plugin-proposal-dynamic-import" "^7.5.0"
    "@babel/plugin-proposal-json-strings" "^7.2.0"
    "@babel/plugin-proposal-object-rest-spread" "^7.5.5"
    "@babel/plugin-proposal-optional-catch-binding" "^7.2.0"
    "@babel/plugin-proposal-unicode-property-regex" "^7.4.4"
    "@babel/plugin-syntax-async-generators" "^7.2.0"
    "@babel/plugin-syntax-dynamic-import" "^7.2.0"
    "@babel/plugin-syntax-json-strings" "^7.2.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.2.0"
    "@babel/plugin-syntax-optional-catch-binding" "^7.2.0"
    "@babel/plugin-transform-arrow-functions" "^7.2.0"
    "@babel/plugin-transform-async-to-generator" "^7.5.0"
    "@babel/plugin-transform-block-scoped-functions" "^7.2.0"
    "@babel/plugin-transform-block-scoping" "^7.6.0"
    "@babel/plugin-transform-classes" "^7.5.5"
    "@babel/plugin-transform-computed-properties" "^7.2.0"
    "@babel/plugin-transform-destructuring" "^7.6.0"
    "@babel/plugin-transform-dotall-regex" "^7.4.4"
    "@babel/plugin-transform-duplicate-keys" "^7.5.0"
    "@babel/plugin-transform-exponentiation-operator" "^7.2.0"
    "@babel/plugin-transform-for-of" "^7.4.4"
    "@babel/plugin-transform-function-name" "^7.4.4"
    "@babel/plugin-transform-literals" "^7.2.0"
    "@babel/plugin-transform-member-expression-literals" "^7.2.0"
    "@babel/plugin-transform-modules-amd" "^7.5.0"
    "@babel/plugin-transform-modules-commonjs" "^7.6.0"
    "@babel/plugin-transform-modules-systemjs" "^7.5.0"
    "@babel/plugin-transform-modules-umd" "^7.2.0"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.6.0"
    "@babel/plugin-transform-new-target" "^7.4.4"
    "@babel/plugin-transform-object-super" "^7.5.5"
    "@babel/plugin-transform-parameters" "^7.4.4"
    "@babel/plugin-transform-property-literals" "^7.2.0"
    "@babel/plugin-transform-regenerator" "^7.4.5"
    "@babel/plugin-transform-reserved-words" "^7.2.0"
    "@babel/plugin-transform-shorthand-properties" "^7.2.0"
    "@babel/plugin-transform-spread" "^7.2.0"
    "@babel/plugin-transform-sticky-regex" "^7.2.0"
    "@babel/plugin-transform-template-literals" "^7.4.4"
    "@babel/plugin-transform-typeof-symbol" "^7.2.0"
    "@babel/plugin-transform-unicode-regex" "^7.4.4"
    "@babel/types" "^7.6.0"
    browserslist "^4.6.0"
    core-js-compat "^3.1.1"
    invariant "^2.2.2"
    js-levenshtein "^1.1.3"
    semver "^5.5.0"

"@babel/preset-react@^7.0.0":
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/@babel/preset-react/download/@babel/preset-react-7.0.0.tgz#e86b4b3d99433c7b3e9e91747e2653958bc6b3c0"
  integrity sha1-6GtLPZlDPHs+npF0fiZTlYvGs8A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/plugin-transform-react-display-name" "^7.0.0"
    "@babel/plugin-transform-react-jsx" "^7.0.0"
    "@babel/plugin-transform-react-jsx-self" "^7.0.0"
    "@babel/plugin-transform-react-jsx-source" "^7.0.0"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.5":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/runtime/download/@babel/runtime-7.6.0.tgz#4fc1d642a9fd0299754e8b5de62c631cf5568205"
  integrity sha1-T8HWQqn9Apl1Totd5ixjHPVWggU=
  dependencies:
    regenerator-runtime "^0.13.2"

"@babel/template@^7.1.0", "@babel/template@^7.4.4", "@babel/template@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/template/download/@babel/template-7.6.0.tgz#7f0159c7f5012230dad64cca42ec9bdb5c9536e6"
  integrity sha1-fwFZx/UBIjDa1kzKQuyb21yVNuY=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.6.0"
    "@babel/types" "^7.6.0"

"@babel/template@^7.7.0":
  version "7.7.0"
  resolved "http://registry.npm.qima-inc.com/@babel/template/download/@babel/template-7.7.0.tgz#4fadc1b8e734d97f56de39c77de76f2562e597d0"
  integrity sha1-T63BuOc02X9W3jnHfedvJWLll9A=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.0"
    "@babel/types" "^7.7.0"

"@babel/template@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/template/download/@babel/template-7.7.4.tgz?cache=0&sync_timestamp=1574465630781&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftemplate%2Fdownload%2F%40babel%2Ftemplate-7.7.4.tgz#428a7d9eecffe27deac0a98e23bf8e3675d2a77b"
  integrity sha1-Qop9nuz/4n3qwKmOI7+ONnXSp3s=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.7.4"
    "@babel/types" "^7.7.4"

"@babel/template@^7.8.3", "@babel/template@^7.8.6":
  version "7.8.6"
  resolved "http://registry.npm.qima-inc.com/@babel/template/download/@babel/template-7.8.6.tgz#86b22af15f828dfb086474f964dcc3e39c43ce2b"
  integrity sha1-hrIq8V+CjfsIZHT5ZNzD45xDzis=
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@babel/parser" "^7.8.6"
    "@babel/types" "^7.8.6"

"@babel/traverse@^7.0.0", "@babel/traverse@^7.1.0", "@babel/traverse@^7.4.4", "@babel/traverse@^7.5.5", "@babel/traverse@^7.6.0":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@babel/traverse/download/@babel/traverse-7.6.0.tgz#389391d510f79be7ce2ddd6717be66d3fed4b516"
  integrity sha1-OJOR1RD3m+fOLd1nF75m0/7UtRY=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.6.0"
    "@babel/helper-function-name" "^7.1.0"
    "@babel/helper-split-export-declaration" "^7.4.4"
    "@babel/parser" "^7.6.0"
    "@babel/types" "^7.6.0"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.13"

"@babel/traverse@^7.7.0", "@babel/traverse@^7.7.2":
  version "7.7.2"
  resolved "http://registry.npm.qima-inc.com/@babel/traverse/download/@babel/traverse-7.7.2.tgz?cache=0&sync_timestamp=1573082879821&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.7.2.tgz#ef0a65e07a2f3c550967366b3d9b62a2dcbeae09"
  integrity sha1-7wpl4HovPFUJZzZrPZtioty+rgk=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.7.2"
    "@babel/helper-function-name" "^7.7.0"
    "@babel/helper-split-export-declaration" "^7.7.0"
    "@babel/parser" "^7.7.2"
    "@babel/types" "^7.7.2"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.13"

"@babel/traverse@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/traverse/download/@babel/traverse-7.7.4.tgz?cache=0&sync_timestamp=1574465640801&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftraverse%2Fdownload%2F%40babel%2Ftraverse-7.7.4.tgz#9c1e7c60fb679fe4fcfaa42500833333c2058558"
  integrity sha1-nB58YPtnn+T8+qQlAIMzM8IFhVg=
  dependencies:
    "@babel/code-frame" "^7.5.5"
    "@babel/generator" "^7.7.4"
    "@babel/helper-function-name" "^7.7.4"
    "@babel/helper-split-export-declaration" "^7.7.4"
    "@babel/parser" "^7.7.4"
    "@babel/types" "^7.7.4"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.13"

"@babel/traverse@^7.8.4", "@babel/traverse@^7.8.6":
  version "7.8.6"
  resolved "http://registry.npm.qima-inc.com/@babel/traverse/download/@babel/traverse-7.8.6.tgz#acfe0c64e1cd991b3e32eae813a6eb564954b5ff"
  integrity sha1-rP4MZOHNmRs+MuroE6brVklUtf8=
  dependencies:
    "@babel/code-frame" "^7.8.3"
    "@babel/generator" "^7.8.6"
    "@babel/helper-function-name" "^7.8.3"
    "@babel/helper-split-export-declaration" "^7.8.3"
    "@babel/parser" "^7.8.6"
    "@babel/types" "^7.8.6"
    debug "^4.1.0"
    globals "^11.1.0"
    lodash "^4.17.13"

"@babel/types@^7.0.0", "@babel/types@^7.0.0-beta.49", "@babel/types@^7.2.0", "@babel/types@^7.3.0", "@babel/types@^7.4.4", "@babel/types@^7.5.5", "@babel/types@^7.6.0":
  version "7.6.1"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.6.1.tgz#53abf3308add3ac2a2884d539151c57c4b3ac648"
  integrity sha1-U6vzMIrdOsKiiE1TkVHFfEs6xkg=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@babel/types@^7.7.0", "@babel/types@^7.7.2":
  version "7.7.2"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.7.2.tgz#550b82e5571dcd174af576e23f0adba7ffc683f7"
  integrity sha1-VQuC5VcdzRdK9XbiPwrbp//Gg/c=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@babel/types@^7.7.4":
  version "7.7.4"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.7.4.tgz?cache=0&sync_timestamp=1574465636802&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2F%40babel%2Ftypes%2Fdownload%2F%40babel%2Ftypes-7.7.4.tgz#516570d539e44ddf308c07569c258ff94fde9193"
  integrity sha1-UWVw1TnkTd8wjAdWnCWP+U/ekZM=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@babel/types@^7.8.3", "@babel/types@^7.8.6", "@babel/types@^7.8.7":
  version "7.8.7"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.8.7.tgz#1fc9729e1acbb2337d5b6977a63979b4819f5d1d"
  integrity sha1-H8lynhrLsjN9W2l3pjl5tIGfXR0=
  dependencies:
    esutils "^2.0.2"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@babel/types@^7.9.0":
  version "7.9.5"
  resolved "http://registry.npm.qima-inc.com/@babel/types/download/@babel/types-7.9.5.tgz#89231f82915a8a566a703b3b20133f73da6b9444"
  integrity sha1-iSMfgpFailZqcDs7IBM/c9prlEQ=
  dependencies:
    "@babel/helper-validator-identifier" "^7.9.5"
    lodash "^4.17.13"
    to-fast-properties "^2.0.0"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/@bcoe/v8-coverage/download/@bcoe/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@cnakazawa/watch@^1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@cnakazawa/watch/download/@cnakazawa/watch-1.0.3.tgz#099139eaec7ebf07a27c1786a3ff64f39464d2ef"
  integrity sha1-CZE56ux+vweifBeGo/9k85Rk0u8=
  dependencies:
    exec-sh "^0.3.2"
    minimist "^1.2.0"

"@hapi/address@2.x.x":
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/address/download/@hapi/address-2.1.1.tgz#61395b5ed94c4cb19c2dc4c85969cff3d40d583f"
  integrity sha1-YTlbXtlMTLGcLcTIWWnP89QNWD8=

"@hapi/bourne@1.x.x":
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/@hapi/bourne/download/@hapi/bourne-1.3.2.tgz#0a7095adea067243ce3283e1b56b8a8f453b242a"
  integrity sha1-CnCVreoGckPOMoPhtWuKj0U7JCo=

"@hapi/hoek@8.x.x":
  version "8.2.4"
  resolved "http://registry.npm.qima-inc.com/@hapi/hoek/download/@hapi/hoek-8.2.4.tgz#684a14f4ca35d46f44abc87dfc696e5e4fe8a020"
  integrity sha1-aEoU9Mo11G9Eq8h9/GluXk/ooCA=

"@hapi/joi@^15.1.0":
  version "15.1.1"
  resolved "http://registry.npm.qima-inc.com/@hapi/joi/download/@hapi/joi-15.1.1.tgz#c675b8a71296f02833f8d6d243b34c57b8ce19d7"
  integrity sha1-xnW4pxKW8Cgz+NbSQ7NMV7jOGdc=
  dependencies:
    "@hapi/address" "2.x.x"
    "@hapi/bourne" "1.x.x"
    "@hapi/hoek" "8.x.x"
    "@hapi/topo" "3.x.x"

"@hapi/topo@3.x.x":
  version "3.1.3"
  resolved "http://registry.npm.qima-inc.com/@hapi/topo/download/@hapi/topo-3.1.3.tgz#c7a02e0d936596d29f184e6d7fdc07e8b5efce11"
  integrity sha1-x6AuDZNlltKfGE5tf9wH6LXvzhE=
  dependencies:
    "@hapi/hoek" "8.x.x"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/@istanbuljs/load-nyc-config/download/@istanbuljs/load-nyc-config-1.0.0.tgz#10602de5570baea82f8afbfa2630b24e7a8cfe5b"
  integrity sha1-EGAt5VcLrqgvivv6JjCyTnqM/ls=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2":
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/@istanbuljs/schema/download/@istanbuljs/schema-0.1.2.tgz#26520bf09abe4a5644cd5414e37125a8954241dd"
  integrity sha1-JlIL8Jq+SlZEzVQU43ElqJVCQd0=

"@jest/console@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/console/download/@jest/console-25.1.0.tgz#1fc765d44a1e11aec5029c08e798246bd37075ab"
  integrity sha1-H8dl1EoeEa7FApwI55gka9Nwdas=
  dependencies:
    "@jest/source-map" "^25.1.0"
    chalk "^3.0.0"
    jest-util "^25.1.0"
    slash "^3.0.0"

"@jest/core@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/core/download/@jest/core-25.1.0.tgz#3d4634fc3348bb2d7532915d67781cdac0869e47"
  integrity sha1-PUY0/DNIuy11MpFdZ3gc2sCGnkc=
  dependencies:
    "@jest/console" "^25.1.0"
    "@jest/reporters" "^25.1.0"
    "@jest/test-result" "^25.1.0"
    "@jest/transform" "^25.1.0"
    "@jest/types" "^25.1.0"
    ansi-escapes "^4.2.1"
    chalk "^3.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.3"
    jest-changed-files "^25.1.0"
    jest-config "^25.1.0"
    jest-haste-map "^25.1.0"
    jest-message-util "^25.1.0"
    jest-regex-util "^25.1.0"
    jest-resolve "^25.1.0"
    jest-resolve-dependencies "^25.1.0"
    jest-runner "^25.1.0"
    jest-runtime "^25.1.0"
    jest-snapshot "^25.1.0"
    jest-util "^25.1.0"
    jest-validate "^25.1.0"
    jest-watcher "^25.1.0"
    micromatch "^4.0.2"
    p-each-series "^2.1.0"
    realpath-native "^1.1.0"
    rimraf "^3.0.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/environment/download/@jest/environment-25.1.0.tgz#4a97f64770c9d075f5d2b662b5169207f0a3f787"
  integrity sha1-Spf2R3DJ0HX10rZitRaSB/Cj94c=
  dependencies:
    "@jest/fake-timers" "^25.1.0"
    "@jest/types" "^25.1.0"
    jest-mock "^25.1.0"

"@jest/fake-timers@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/fake-timers/download/@jest/fake-timers-25.1.0.tgz#a1e0eff51ffdbb13ee81f35b52e0c1c11a350ce8"
  integrity sha1-oeDv9R/9uxPugfNbUuDBwRo1DOg=
  dependencies:
    "@jest/types" "^25.1.0"
    jest-message-util "^25.1.0"
    jest-mock "^25.1.0"
    jest-util "^25.1.0"
    lolex "^5.0.0"

"@jest/reporters@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/reporters/download/@jest/reporters-25.1.0.tgz#9178ecf136c48f125674ac328f82ddea46e482b0"
  integrity sha1-kXjs8TbEjxJWdKwyj4Ld6kbkgrA=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^25.1.0"
    "@jest/environment" "^25.1.0"
    "@jest/test-result" "^25.1.0"
    "@jest/transform" "^25.1.0"
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.2"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^4.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.0.0"
    jest-haste-map "^25.1.0"
    jest-resolve "^25.1.0"
    jest-runtime "^25.1.0"
    jest-util "^25.1.0"
    jest-worker "^25.1.0"
    slash "^3.0.0"
    source-map "^0.6.0"
    string-length "^3.1.0"
    terminal-link "^2.0.0"
    v8-to-istanbul "^4.0.1"
  optionalDependencies:
    node-notifier "^6.0.0"

"@jest/source-map@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/source-map/download/@jest/source-map-25.1.0.tgz#b012e6c469ccdbc379413f5c1b1ffb7ba7034fb0"
  integrity sha1-sBLmxGnM28N5QT9cGx/7e6cDT7A=
  dependencies:
    callsites "^3.0.0"
    graceful-fs "^4.2.3"
    source-map "^0.6.0"

"@jest/test-result@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/test-result/download/@jest/test-result-25.1.0.tgz#847af2972c1df9822a8200457e64be4ff62821f7"
  integrity sha1-hHrylywd+YIqggBFfmS+T/YoIfc=
  dependencies:
    "@jest/console" "^25.1.0"
    "@jest/transform" "^25.1.0"
    "@jest/types" "^25.1.0"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/test-sequencer/download/@jest/test-sequencer-25.1.0.tgz#4df47208542f0065f356fcdb80026e3c042851ab"
  integrity sha1-TfRyCFQvAGXzVvzbgAJuPAQoUas=
  dependencies:
    "@jest/test-result" "^25.1.0"
    jest-haste-map "^25.1.0"
    jest-runner "^25.1.0"
    jest-runtime "^25.1.0"

"@jest/transform@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/transform/download/@jest/transform-25.1.0.tgz#221f354f512b4628d88ce776d5b9e601028ea9da"
  integrity sha1-Ih81T1ErRijYjOd21bnmAQKOqdo=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^25.1.0"
    babel-plugin-istanbul "^6.0.0"
    chalk "^3.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.3"
    jest-haste-map "^25.1.0"
    jest-regex-util "^25.1.0"
    jest-util "^25.1.0"
    micromatch "^4.0.2"
    pirates "^4.0.1"
    realpath-native "^1.1.0"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/transform@^25.3.0":
  version "25.3.0"
  resolved "http://registry.npm.qima-inc.com/@jest/transform/download/@jest/transform-25.3.0.tgz#083c5447d5307d9b9494d6968115b647460e71f1"
  integrity sha1-CDxUR9UwfZuUlNaWgRW2R0YOcfE=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/types" "^25.3.0"
    babel-plugin-istanbul "^6.0.0"
    chalk "^3.0.0"
    convert-source-map "^1.4.0"
    fast-json-stable-stringify "^2.0.0"
    graceful-fs "^4.2.3"
    jest-haste-map "^25.3.0"
    jest-regex-util "^25.2.6"
    jest-util "^25.3.0"
    micromatch "^4.0.2"
    pirates "^4.0.1"
    realpath-native "^2.0.0"
    slash "^3.0.0"
    source-map "^0.6.1"
    write-file-atomic "^3.0.0"

"@jest/types@^24.9.0":
  version "24.9.0"
  resolved "http://registry.npm.qima-inc.com/@jest/types/download/@jest/types-24.9.0.tgz#63cb26cb7500d069e5a389441a7c6ab5e909fc59"
  integrity sha1-Y8smy3UA0Gnlo4lEGnxqtekJ/Fk=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^13.0.0"

"@jest/types@^25.1.0":
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/@jest/types/download/@jest/types-25.1.0.tgz#b26831916f0d7c381e11dbb5e103a72aed1b4395"
  integrity sha1-smgxkW8NfDgeEdu14QOnKu0bQ5U=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    chalk "^3.0.0"

"@jest/types@^25.3.0":
  version "25.3.0"
  resolved "http://registry.npm.qima-inc.com/@jest/types/download/@jest/types-25.3.0.tgz#88f94b277a1d028fd7117bc1f74451e0fc2131e7"
  integrity sha1-iPlLJ3odAo/XEXvB90RR4PwhMec=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^1.1.1"
    "@types/yargs" "^15.0.0"
    chalk "^3.0.0"

"@nodelib/fs.scandir@2.1.2":
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.2.tgz#1f981cd5b83e85cfdeb386fc693d4baab392fa54"
  integrity sha1-H5gc1bg+hc/es4b8aT1LqrOS+lQ=
  dependencies:
    "@nodelib/fs.stat" "2.0.2"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.2", "@nodelib/fs.stat@^2.0.1":
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.2.tgz#2762aea8fe78ea256860182dcb52d61ee4b8fda6"
  integrity sha1-J2KuqP546iVoYBgty1LWHuS4/aY=

"@nodelib/fs.walk@^1.2.1":
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.3.tgz#a555dc256acaf00c62b0db29529028dd4d4cb141"
  integrity sha1-pVXcJWrK8AxisNspUpAo3U1MsUE=
  dependencies:
    "@nodelib/fs.scandir" "2.1.2"
    fastq "^1.6.0"

"@samverschueren/stream-to-observable@^0.3.0":
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/@samverschueren/stream-to-observable/download/@samverschueren/stream-to-observable-0.3.0.tgz#ecdf48d532c58ea477acfcab80348424f8d0662f"
  integrity sha1-7N9I1TLFjqR3rPyrgDSEJPjQZi8=
  dependencies:
    any-observable "^0.3.0"

"@sinonjs/commons@^1.7.0":
  version "1.7.1"
  resolved "http://registry.npm.qima-inc.com/@sinonjs/commons/download/@sinonjs/commons-1.7.1.tgz#da5fd19a5f71177a53778073978873964f49acf1"
  integrity sha1-2l/Rml9xF3pTd4Bzl4hzlk9JrPE=
  dependencies:
    type-detect "4.0.8"

"@types/babel__core@^7.1.0":
  version "7.1.3"
  resolved "http://registry.npm.qima-inc.com/@types/babel__core/download/@types/babel__core-7.1.3.tgz#e441ea7df63cd080dfcd02ab199e6d16a735fc30"
  integrity sha1-5EHqffY80IDfzQKrGZ5tFqc1/DA=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__core@^7.1.7":
  version "7.1.7"
  resolved "http://registry.npm.qima-inc.com/@types/babel__core/download/@types/babel__core-7.1.7.tgz#1dacad8840364a57c98d0dd4855c6dd3752c6b89"
  integrity sha1-HaytiEA2SlfJjQ3UhVxt03Usa4k=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.6.0"
  resolved "http://registry.npm.qima-inc.com/@types/babel__generator/download/@types/babel__generator-7.6.0.tgz#f1ec1c104d1bb463556ecb724018ab788d0c172a"
  integrity sha1-8ewcEE0btGNVbstyQBireI0MFyo=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.0.2"
  resolved "http://registry.npm.qima-inc.com/@types/babel__template/download/@types/babel__template-7.0.2.tgz#4ff63d6b52eddac1de7b975a5223ed32ecea9307"
  integrity sha1-T/Y9a1Lt2sHee5daUiPtMuzqkwc=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6":
  version "7.0.7"
  resolved "http://registry.npm.qima-inc.com/@types/babel__traverse/download/@types/babel__traverse-7.0.7.tgz#2496e9ff56196cc1429c72034e07eab6121b6f3f"
  integrity sha1-JJbp/1YZbMFCnHIDTgfqthIbbz8=
  dependencies:
    "@babel/types" "^7.3.0"

"@types/caseless@*":
  version "0.12.2"
  resolved "http://registry.npm.qima-inc.com/@types/caseless/download/@types/caseless-0.12.2.tgz#f65d3d6389e01eeb458bd54dc8f52b95a9463bc8"
  integrity sha1-9l09Y4ngHutFi9VNyPUrlalGO8g=

"@types/color-name@^1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/color-name/download/@types/color-name-1.1.1.tgz#1c1261bbeaa10a8055bbc5d8ab84b7b2afc846a0"
  integrity sha1-HBJhu+qhCoBVu8XYq4S3sq/IRqA=

"@types/events@*":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/events/download/@types/events-3.0.0.tgz#2862f3f58a9a7f7c3e78d79f130dd4d71c25c2a7"
  integrity sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc=

"@types/glob@^7.1.1":
  version "7.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/glob/download/@types/glob-7.1.1.tgz#aa59a1c6e3fbc421e07ccd31a944c30eba521575"
  integrity sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU=
  dependencies:
    "@types/events" "*"
    "@types/minimatch" "*"
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/@types/istanbul-lib-coverage/download/@types/istanbul-lib-coverage-2.0.1.tgz#42995b446db9a48a11a07ec083499a860e9138ff"
  integrity sha1-QplbRG25pIoRoH7Ag0mahg6ROP8=

"@types/istanbul-lib-report@*":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/istanbul-lib-report/download/@types/istanbul-lib-report-1.1.1.tgz#e5471e7fa33c61358dd38426189c037a58433b8c"
  integrity sha1-5Ucef6M8YTWN04QmGJwDelhDO4w=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/istanbul-reports/download/@types/istanbul-reports-1.1.1.tgz#7a8cbf6a406f36c8add871625b278eaf0b0d255a"
  integrity sha1-eoy/akBvNsit2HFiWyeOrwsNJVo=
  dependencies:
    "@types/istanbul-lib-coverage" "*"
    "@types/istanbul-lib-report" "*"

"@types/mime@2.0.1":
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/@types/mime/download/@types/mime-2.0.1.tgz#dc488842312a7f075149312905b5e3c0b054c79d"
  integrity sha1-3EiIQjEqfwdRSTEpBbXjwLBUx50=

"@types/minimatch@*":
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/minimatch/download/@types/minimatch-3.0.3.tgz#3dca0e3f33b200fc7d1139c0cd96c1268cadfd9d"
  integrity sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=

"@types/multimap@1.1.1":
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/@types/multimap/download/@types/multimap-1.1.1.tgz#a06b6031fc5668e09f3b505e207eaf1821e7d57d"
  integrity sha1-oGtgMfxWaOCfO1BeIH6vGCHn1X0=

"@types/node@*":
  version "12.7.4"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-12.7.4.tgz#64db61e0359eb5a8d99b55e05c729f130a678b04"
  integrity sha1-ZNth4DWetajZm1XgXHKfEwpniwQ=

"@types/node@13.13.5":
  version "13.13.5"
  resolved "http://registry.npm.qima-inc.com/@types/node/download/@types/node-13.13.5.tgz#96ec3b0afafd64a4ccea9107b75bf8489f0e5765"
  integrity sha1-luw7Cvr9ZKTM6pEHt1v4SJ8OV2U=

"@types/normalize-package-data@^2.4.0":
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/@types/normalize-package-data/download/@types/normalize-package-data-2.4.0.tgz#e486d0d97396d79beedd0a6e33f4534ff6b4973e"
  integrity sha1-5IbQ2XOW15vu3QpuM/RTT/a0lz4=

"@types/q@^1.5.1":
  version "1.5.2"
  resolved "http://registry.npm.qima-inc.com/@types/q/download/@types/q-1.5.2.tgz#690a1475b84f2a884fd07cd797c00f5f31356ea8"
  integrity sha1-aQoUdbhPKohP0HzXl8APXzE1bqg=

"@types/request@2.48.4":
  version "2.48.4"
  resolved "http://registry.npm.qima-inc.com/@types/request/download/@types/request-2.48.4.tgz#df3d43d7b9ed3550feaa1286c6eabf0738e6cf7e"
  integrity sha1-3z1D17ntNVD+qhKGxuq/Bzjmz34=
  dependencies:
    "@types/caseless" "*"
    "@types/node" "*"
    "@types/tough-cookie" "*"
    form-data "^2.5.0"

"@types/stack-utils@^1.0.1":
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/@types/stack-utils/download/@types/stack-utils-1.0.1.tgz#0a851d3bd96498fa25c33ab7278ed3bd65f06c3e"
  integrity sha1-CoUdO9lkmPolwzq3J47TvWXwbD4=

"@types/tough-cookie@*":
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/@types/tough-cookie/download/@types/tough-cookie-4.0.0.tgz#fef1904e4668b6e5ecee60c52cc6a078ffa6697d"
  integrity sha1-/vGQTkZotuXs7mDFLMageP+maX0=

"@types/underscore@1.10.0":
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/@types/underscore/download/@types/underscore-1.10.0.tgz#5cb0dff2a5f616fc8e0c61b482bf01fa20a03cec"
  integrity sha1-XLDf8qX2FvyODGG0gr8B+iCgPOw=

"@types/utf8@2.1.6":
  version "2.1.6"
  resolved "http://registry.npm.qima-inc.com/@types/utf8/download/@types/utf8-2.1.6.tgz#430cabb71a42d0a3613cce5621324fe4f5a25753"
  integrity sha1-QwyrtxpC0KNhPM5WITJP5PWiV1M=

"@types/yargs-parser@*":
  version "13.1.0"
  resolved "http://registry.npm.qima-inc.com/@types/yargs-parser/download/@types/yargs-parser-13.1.0.tgz#c563aa192f39350a1d18da36c5a8da382bbd8228"
  integrity sha1-xWOqGS85NQodGNo2xajaOCu9gig=

"@types/yargs@^13.0.0":
  version "13.0.3"
  resolved "http://registry.npm.qima-inc.com/@types/yargs/download/@types/yargs-13.0.3.tgz#76482af3981d4412d65371a318f992d33464a380"
  integrity sha1-dkgq85gdRBLWU3GjGPmS0zRko4A=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^15.0.0":
  version "15.0.4"
  resolved "http://registry.npm.qima-inc.com/@types/yargs/download/@types/yargs-15.0.4.tgz#7e5d0f8ca25e9d5849f2ea443cf7c402decd8299"
  integrity sha1-fl0PjKJenVhJ8upEPPfEAt7Ngpk=
  dependencies:
    "@types/yargs-parser" "*"

"@webassemblyjs/ast@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/ast/download/@webassemblyjs/ast-1.9.0.tgz#bd850604b4042459a5a41cd7d338cbed695ed964"
  integrity sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=
  dependencies:
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"

"@webassemblyjs/floating-point-hex-parser@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.9.0.tgz#3c3d3b271bddfc84deb00f71344438311d52ffb4"
  integrity sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=

"@webassemblyjs/helper-api-error@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.9.0.tgz#203f676e333b96c9da2eeab3ccef33c45928b6a2"
  integrity sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=

"@webassemblyjs/helper-buffer@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.9.0.tgz#a1442d269c5feb23fcbc9ef759dac3547f29de00"
  integrity sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=

"@webassemblyjs/helper-code-frame@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.9.0.tgz#647f8892cd2043a82ac0c8c5e75c36f1d9159f27"
  integrity sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=
  dependencies:
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/helper-fsm@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.9.0.tgz#c05256b71244214671f4b08ec108ad63b70eddb8"
  integrity sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=

"@webassemblyjs/helper-module-context@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.9.0.tgz#25d8884b76839871a08a6c6f806c3979ef712f07"
  integrity sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"

"@webassemblyjs/helper-wasm-bytecode@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.9.0.tgz#4fed8beac9b8c14f8c58b70d124d549dd1fe5790"
  integrity sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=

"@webassemblyjs/helper-wasm-section@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.9.0.tgz#5a4138d5a6292ba18b04c5ae49717e4167965346"
  integrity sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"

"@webassemblyjs/ieee754@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.9.0.tgz#15c7a0fbaae83fb26143bbacf6d6df1702ad39e4"
  integrity sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=
  dependencies:
    "@xtuc/ieee754" "^1.2.0"

"@webassemblyjs/leb128@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.9.0.tgz#f19ca0b76a6dc55623a09cffa769e838fa1e1c95"
  integrity sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=
  dependencies:
    "@xtuc/long" "4.2.2"

"@webassemblyjs/utf8@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.9.0.tgz#04d33b636f78e6a6813227e82402f7637b6229ab"
  integrity sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=

"@webassemblyjs/wasm-edit@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.9.0.tgz#3fe6d79d3f0f922183aa86002c42dd256cfee9cf"
  integrity sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/helper-wasm-section" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-opt" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    "@webassemblyjs/wast-printer" "1.9.0"

"@webassemblyjs/wasm-gen@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.9.0.tgz#50bc70ec68ded8e2763b01a1418bf43491a7a49c"
  integrity sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wasm-opt@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.9.0.tgz#2211181e5b31326443cc8112eb9f0b9028721a61"
  integrity sha1-IhEYHlsxMmRDzIES658LkChyGmE=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-buffer" "1.9.0"
    "@webassemblyjs/wasm-gen" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"

"@webassemblyjs/wasm-parser@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.9.0.tgz#9d48e44826df4a6598294aa6c87469d642fff65e"
  integrity sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-wasm-bytecode" "1.9.0"
    "@webassemblyjs/ieee754" "1.9.0"
    "@webassemblyjs/leb128" "1.9.0"
    "@webassemblyjs/utf8" "1.9.0"

"@webassemblyjs/wast-parser@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.9.0.tgz#3031115d79ac5bd261556cecc3fa90a3ef451914"
  integrity sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/floating-point-hex-parser" "1.9.0"
    "@webassemblyjs/helper-api-error" "1.9.0"
    "@webassemblyjs/helper-code-frame" "1.9.0"
    "@webassemblyjs/helper-fsm" "1.9.0"
    "@xtuc/long" "4.2.2"

"@webassemblyjs/wast-printer@1.9.0":
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.9.0.tgz#4935d54c85fef637b00ce9f52377451d00d47899"
  integrity sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/wast-parser" "1.9.0"
    "@xtuc/long" "4.2.2"

"@xtuc/ieee754@^1.2.0":
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz#eef014a3145ae477a1cbc00cd1e552336dceb790"
  integrity sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=

"@xtuc/long@4.2.2":
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/@xtuc/long/download/@xtuc/long-4.2.2.tgz#d291c6a4e97989b5c61d9acf396ae4fe133a718d"
  integrity sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=

"@youzan/babel-preset-wsc-pc@2.0.7":
  version "2.0.7"
  resolved "http://registry.npm.qima-inc.com/@youzan/babel-preset-wsc-pc/download/@youzan/babel-preset-wsc-pc-2.0.7.tgz#d7e95b3b5e6d66fa854a446fc74440dab243a1b3"
  integrity sha1-1+lbO15tZvqFSkRvx0RA2rJDobM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    babel-plugin-import "^1.11.0"
    babel-plugin-lodash "^3.3.4"
    babel-plugin-zent "2.2.2"

"@youzan/cdn-core@0.2.2-beta.1":
  version "0.2.2-beta.1"
  resolved "http://registry.npm.qima-inc.com/@youzan/cdn-core/download/@youzan/cdn-core-0.2.2-beta.1.tgz#fcf6afd0672fe3d277cbf581606cde4f5284d3f9"
  integrity sha1-/Pav0Gcv49J3y/WBYGzeT1KE0/k=
  dependencies:
    "@youzan/opm-log" "0.1.0"
    cli-progress "^3.1.0"
    file-type "^12.0.1"
    glob "^7.1.4"
    hostile "^1.3.2"
    inquirer "^6.4.1"
    lodash "^4.17.11"
    mkdirp "^0.5.1"
    ora "^3.4.0"
    promise.prototype.finally "^3.1.0"
    qiniu "^7.2.2"
    read-chunk "^3.2.0"
    upyun "3.3.4"

"@youzan/es-guard@2.0.0":
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/es-guard/download/@youzan/es-guard-2.0.0.tgz#52848570588866241751087b4e999c3137cfdcc6"
  integrity sha1-UoSFcFiIZiQXUQh7TpmcMTfP3MY=
  dependencies:
    acorn "7.2.0"
    acorn-walk "^7.1.1"
    chalk "^4.1.0"
    commander "^5.1.0"
    glob "7.1.6"
    lodash "^4.17.15"
    log-symbols "^4.0.0"
    minimatch "3.0.4"
    rxjs "^6.5.5"

"@youzan/opm-log@0.1.0":
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/opm-log/download/@youzan/opm-log-0.1.0.tgz#a2232caee53f7162c80088d8db1e7d477f429443"
  integrity sha1-oiMsruU/cWLIAIjY2x59R39ClEM=
  dependencies:
    signale "^1.4.0"

"@youzan/sentry-sourcemap-uploader@^1.0.6":
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/@youzan/sentry-sourcemap-uploader/download/@youzan/sentry-sourcemap-uploader-1.0.6.tgz#573131e74f20697438d1047c8996f3bcdd584b55"
  integrity sha1-VzEx508gaXQ40QR8iZbzvN1YS1U=
  dependencies:
    "@youzan/ufile-sdk" "1.0.3"
    lodash "4.17.15"

"@youzan/superman-cdn@3.0.0":
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/@youzan/superman-cdn/download/@youzan/superman-cdn-3.0.0.tgz#197d7f596a2a2ac11b2b13412a87b4d87c011f4e"
  integrity sha1-GX1/WWoqKsEbKxNBKoe02HwBH04=
  dependencies:
    "@youzan/cdn-core" "0.2.2-beta.1"
    commander "^2.9.0"
    consola "^1.3.0"
    glob "^7.1.1"

"@youzan/ufile-sdk@1.0.3":
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/ufile-sdk/download/@youzan/ufile-sdk-1.0.3.tgz#f7ba30008a6c3e47f0841738b487f58b1c292fae"
  integrity sha1-97owAIpsPkfwhBc4tIf1ixwpL64=
  dependencies:
    "@types/mime" "2.0.1"
    "@types/multimap" "1.1.1"
    "@types/node" "13.13.5"
    "@types/request" "2.48.4"
    "@types/underscore" "1.10.0"
    "@types/utf8" "2.1.6"
    mime "2.4.5"
    multimap "1.1.0"
    request "2.88.2"
    streamifier "0.1.1"
    underscore "1.10.2"
    utf8 "3.0.0"

"@youzan/webpack-plugin-serve@^0.12.3":
  version "0.12.3"
  resolved "http://registry.npm.qima-inc.com/@youzan/webpack-plugin-serve/download/@youzan/webpack-plugin-serve-0.12.3.tgz#7347a6e30c23c56acb80d800b59fe4e13f996a6c"
  integrity sha1-c0em4wwjxWrLgNgAtZ/k4T+Zamw=
  dependencies:
    "@hapi/joi" "^15.1.0"
    chalk "^2.4.1"
    connect-history-api-fallback "^1.5.0"
    globby "^10.0.1"
    http-proxy-middleware "^0.19.0"
    is-path-cwd "^2.2.0"
    is-promise "^2.1.0"
    koa "^2.5.3"
    koa-compress "^3.0.0"
    koa-connect "^2.0.1"
    koa-route "^3.2.0"
    koa-static "^5.0.0"
    loglevelnext "^3.0.0"
    nanoid "^2.0.0"
    onetime "^5.1.0"
    open "^6.4.0"
    p-defer "^3.0.0"
    read-pkg-up "^6.0.0"
    rimraf "^2.6.3"
    strip-ansi "^5.0.0"
    ws "^7.1.0"

abab@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/abab/download/abab-2.0.3.tgz#623e2075e02eb2d3f2475e49f99c91846467907a"
  integrity sha1-Yj4gdeAustPyR15J+ZyRhGRnkHo=

accepts@^1.3.5, accepts@~1.3.7:
  version "1.3.7"
  resolved "http://registry.npm.qima-inc.com/accepts/download/accepts-1.3.7.tgz#531bc726517a3b2b41f850021c6cc15eaab507cd"
  integrity sha1-UxvHJlF6OytB+FACHGzBXqq1B80=
  dependencies:
    mime-types "~2.1.24"
    negotiator "0.6.2"

acorn-globals@^4.3.2:
  version "4.3.4"
  resolved "http://registry.npm.qima-inc.com/acorn-globals/download/acorn-globals-4.3.4.tgz#9fa1926addc11c97308c4e66d7add0d40c3272e7"
  integrity sha1-n6GSat3BHJcwjE5m163Q1Awycuc=
  dependencies:
    acorn "^6.0.1"
    acorn-walk "^6.0.1"

acorn-jsx@^5.0.2:
  version "5.0.2"
  resolved "http://registry.npm.qima-inc.com/acorn-jsx/download/acorn-jsx-5.0.2.tgz#84b68ea44b373c4f8686023a551f61a21b7c4a4f"
  integrity sha1-hLaOpEs3PE+GhgI6VR9hoht8Sk8=

acorn-walk@^6.0.1, acorn-walk@^6.1.1:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/acorn-walk/download/acorn-walk-6.2.0.tgz#123cb8f3b84c2171f1f7fb252615b1c78a6b1a8c"
  integrity sha1-Ejy487hMIXHx9/slJhWxx4prGow=

acorn-walk@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/acorn-walk/download/acorn-walk-7.0.0.tgz#c8ba6f0f1aac4b0a9e32d1f0af12be769528f36b"
  integrity sha1-yLpvDxqsSwqeMtHwrxK+dpUo82s=

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/acorn-walk/download/acorn-walk-7.2.0.tgz#0de889a601203909b0fbe07b8938dc21d2e967bc"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@7.2.0:
  version "7.2.0"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-7.2.0.tgz#17ea7e40d7c8640ff54a694c889c26f31704effe"
  integrity sha1-F+p+QNfIZA/1SmlMiJwm8xcE7/4=

acorn@^6.0.1, acorn@^6.0.7:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-6.3.0.tgz#0087509119ffa4fc0a0041d1e93a417e68cb856e"
  integrity sha1-AIdQkRn/pPwKAEHR6TpBfmjLhW4=

acorn@^6.4.1:
  version "6.4.1"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-6.4.1.tgz#531e58ba3f51b9dacb9a6646ca4debf5b14ca474"
  integrity sha1-Ux5Yuj9RudrLmmZGyk3r9bFMpHQ=

acorn@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-7.0.0.tgz#26b8d1cd9a9b700350b71c0905546f64d1284e7a"
  integrity sha1-JrjRzZqbcANQtxwJBVRvZNEoTno=

acorn@^7.1.0:
  version "7.1.1"
  resolved "http://registry.npm.qima-inc.com/acorn/download/acorn-7.1.1.tgz#e35668de0b402f359de515c5482a1ab9f89a69bf"
  integrity sha1-41Zo3gtALzWd5RXFSCoaufiaab8=

address@>=0.0.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/address/download/address-1.1.2.tgz#bf1116c9c758c51b7a933d296b72c221ed9428b6"
  integrity sha1-vxEWycdYxRt6kz0pa3LCIe2UKLY=

agentkeepalive@3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/agentkeepalive/download/agentkeepalive-3.3.0.tgz#6d5de5829afd3be2712201a39275fd11c651857c"
  integrity sha1-bV3lgpr9O+JxIgGjknX9EcZRhXw=
  dependencies:
    humanize-ms "^1.2.1"

ajv-errors@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/ajv-errors/download/ajv-errors-1.0.1.tgz#f35986aceb91afadec4102fbd85014950cefa64d"
  integrity sha1-81mGrOuRr63sQQL72FAUlQzvpk0=

ajv-keywords@^3.1.0, ajv-keywords@^3.4.1:
  version "3.4.1"
  resolved "http://registry.npm.qima-inc.com/ajv-keywords/download/ajv-keywords-3.4.1.tgz#ef916e271c64ac12171fd8384eaae6b2345854da"
  integrity sha1-75FuJxxkrBIXH9g4TqrmsjRYVNo=

ajv@^6.1.0, ajv@^6.10.0, ajv@^6.10.2, ajv@^6.5.5:
  version "6.10.2"
  resolved "http://registry.npm.qima-inc.com/ajv/download/ajv-6.10.2.tgz#d3cea04d6b017b2894ad69040fec8b623eb4bd52"
  integrity sha1-086gTWsBeyiUrWkED+yLYj60vVI=
  dependencies:
    fast-deep-equal "^2.0.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

alphanum-sort@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/alphanum-sort/download/alphanum-sort-1.0.2.tgz#97a1119649b211ad33691d9f9f486a8ec9fbe0a3"
  integrity sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=

ansi-escapes@^3.0.0, ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1:
  version "4.3.1"
  resolved "http://registry.npm.qima-inc.com/ansi-escapes/download/ansi-escapes-4.3.1.tgz#a5c47cc43181f1f38ffd7076837700d395522a61"
  integrity sha1-pcR8xDGB8fOP/XB2g3cA05VSKmE=
  dependencies:
    type-fest "^0.11.0"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"
  integrity sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=

ansi-regex@^4.0.0, ansi-regex@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-4.1.0.tgz#8b9f8f08cf1acb843756a839ca8c7e3168c51997"
  integrity sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=

ansi-regex@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/ansi-regex/download/ansi-regex-5.0.0.tgz#388539f55179bf39339c81af30a654d69f87cb75"
  integrity sha1-OIU59VF5vzkznIGvMKZU1p+Hy3U=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.0, ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/ansi-styles/download/ansi-styles-4.2.1.tgz#90ae75c424d008d2624c5bf29ead3177ebfcf359"
  integrity sha1-kK51xCTQCNJiTFvynq0xd+v881k=
  dependencies:
    "@types/color-name" "^1.1.1"
    color-convert "^2.0.1"

any-observable@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/any-observable/download/any-observable-0.3.0.tgz#af933475e5806a67d0d7df090dd5e8bef65d119b"
  integrity sha1-r5M0deWAamfQ198JDdXovvZdEZs=

any-promise@^1.0.0, any-promise@^1.1.0, any-promise@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-2.0.0.tgz#bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb"
  integrity sha1-vLJLTzeTTZqnrBe0ra+J58du8us=
  dependencies:
    micromatch "^3.1.4"
    normalize-path "^2.1.1"

anymatch@^3.0.1:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-3.1.0.tgz#e609350e50a9313b472789b2f14ef35808ee14d6"
  integrity sha1-5gk1DlCpMTtHJ4my8U7zWAjuFNY=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

anymatch@^3.0.3, anymatch@~3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/anymatch/download/anymatch-3.1.1.tgz#c55ecf02185e2469259399310c173ce31233b142"
  integrity sha1-xV7PAhheJGklk5kxDBc84xIzsUI=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

aproba@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/aproba/download/aproba-1.2.0.tgz#6802e6264efd18c790a1b0d517f0f2627bf2c94a"
  integrity sha1-aALmJk79GMeQobDVF/DyYnvyyUo=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

arr-diff@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/arr-diff/download/arr-diff-4.0.0.tgz#d6461074febfec71e7e15235761a329a5dc7c520"
  integrity sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=

arr-flatten@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-flatten/download/arr-flatten-1.1.0.tgz#36048bbff4e7b47e136644316c99669ea5ae91f1"
  integrity sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=

arr-union@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/arr-union/download/arr-union-3.1.0.tgz#e39b09aea9def866a8f206e288af63919bae39c4"
  integrity sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=

array-equal@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/array-equal/download/array-equal-1.0.0.tgz#8c2a5ef2472fd9ea742b04c77a75093ba2757c93"
  integrity sha1-jCpe8kcv2ep0KwTHenUJO6J1fJM=

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"
  integrity sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=

array-includes@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/array-includes/download/array-includes-3.0.3.tgz#184b48f62d92d7452bb31b323165c7f8bd02266d"
  integrity sha1-GEtI9i2S10UrsxsyMWXH+L0CJm0=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.7.0"

array-union@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  integrity sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=
  dependencies:
    array-uniq "^1.0.1"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"
  integrity sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=

array-unique@^0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/array-unique/download/array-unique-0.3.2.tgz#a894b75d4bc4f6cd679ef3244a9fd8f46ae2d428"
  integrity sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=

arrify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"
  integrity sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=

asn1.js@^4.0.0:
  version "4.10.1"
  resolved "http://registry.npm.qima-inc.com/asn1.js/download/asn1.js-4.10.1.tgz#b9c2bf5805f1e64aadeed6df3a2bfafb5a73f5a0"
  integrity sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

asn1@~0.2.3:
  version "0.2.4"
  resolved "http://registry.npm.qima-inc.com/asn1/download/asn1-0.2.4.tgz#8d2475dfab553bb33e77b54e59e880bb8ce23136"
  integrity sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

assert@^1.1.1:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/assert/download/assert-1.5.0.tgz#55c109aaf6e0aefdb3dc4b71240c70bf574b18eb"
  integrity sha1-VcEJqvbgrv2z3EtxJAxwv1dLGOs=
  dependencies:
    object-assign "^4.1.1"
    util "0.10.3"

assign-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/assign-symbols/download/assign-symbols-1.0.0.tgz#59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367"
  integrity sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=

astral-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/astral-regex/download/astral-regex-1.0.0.tgz#6c8c3fb827dd43ee3918f27b82782ab7658a6fd9"
  integrity sha1-bIw/uCfdQ+45GPJ7gngqt2WKb9k=

async-each@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/async-each/download/async-each-1.0.3.tgz#b727dbf87d7651602f06f4d4ac387f47d91b0cbf"
  integrity sha1-tyfb+H12UWAvBvTUrDh/R9kbDL8=

async-limiter@^1.0.0, async-limiter@~1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/async-limiter/download/async-limiter-1.0.1.tgz#dd379e94f0db8310b08291f9d64c3209766617fd"
  integrity sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

atob@^2.1.1:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/atob/download/atob-2.1.2.tgz#6d9517eb9e030d2436666651e86bd9f6f13533c9"
  integrity sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=

autoprefixer@^9.6.1:
  version "9.6.1"
  resolved "http://registry.npm.qima-inc.com/autoprefixer/download/autoprefixer-9.6.1.tgz#51967a02d2d2300bb01866c1611ec8348d355a47"
  integrity sha1-UZZ6AtLSMAuwGGbBYR7INI01Wkc=
  dependencies:
    browserslist "^4.6.3"
    caniuse-lite "^1.0.30000980"
    chalk "^2.4.2"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^7.0.17"
    postcss-value-parser "^4.0.0"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.8.0"
  resolved "http://registry.npm.qima-inc.com/aws4/download/aws4-1.8.0.tgz#f0e003d9ca9e7f59c7a508945d7b2ef9a04a542f"
  integrity sha1-8OAD2cqef1nHpQiUXXsu+aBKVC8=

axios@^0.16.1:
  version "0.16.2"
  resolved "http://registry.npm.qima-inc.com/axios/download/axios-0.16.2.tgz#ba4f92f17167dfbab40983785454b9ac149c3c6d"
  integrity sha1-uk+S8XFn37q0CYN4VFS5rBScPG0=
  dependencies:
    follow-redirects "^1.2.3"
    is-buffer "^1.1.5"

babel-eslint@^10.0.2:
  version "10.0.3"
  resolved "http://registry.npm.qima-inc.com/babel-eslint/download/babel-eslint-10.0.3.tgz#81a2c669be0f205e19462fed2482d33e4687a88a"
  integrity sha1-gaLGab4PIF4ZRi/tJILTPkaHqIo=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@babel/parser" "^7.0.0"
    "@babel/traverse" "^7.0.0"
    "@babel/types" "^7.0.0"
    eslint-visitor-keys "^1.0.0"
    resolve "^1.12.0"

babel-jest@^25.0.0:
  version "25.3.0"
  resolved "http://registry.npm.qima-inc.com/babel-jest/download/babel-jest-25.3.0.tgz#999d0c19e8427f66b796bf9ea233eedf087b957c"
  integrity sha1-mZ0MGehCf2a3lr+eojPu3wh7lXw=
  dependencies:
    "@jest/transform" "^25.3.0"
    "@jest/types" "^25.3.0"
    "@types/babel__core" "^7.1.7"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^25.3.0"
    chalk "^3.0.0"
    slash "^3.0.0"

babel-jest@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/babel-jest/download/babel-jest-25.1.0.tgz#206093ac380a4b78c4404a05b3277391278f80fb"
  integrity sha1-IGCTrDgKS3jEQEoFsydzkSePgPs=
  dependencies:
    "@jest/transform" "^25.1.0"
    "@jest/types" "^25.1.0"
    "@types/babel__core" "^7.1.0"
    babel-plugin-istanbul "^6.0.0"
    babel-preset-jest "^25.1.0"
    chalk "^3.0.0"
    slash "^3.0.0"

babel-loader@^8.0.6:
  version "8.0.6"
  resolved "http://registry.npm.qima-inc.com/babel-loader/download/babel-loader-8.0.6.tgz#e33bdb6f362b03f4bb141a0c21ab87c501b70dfb"
  integrity sha1-4zvbbzYrA/S7FBoMIauHxQG3Dfs=
  dependencies:
    find-cache-dir "^2.0.0"
    loader-utils "^1.0.2"
    mkdirp "^0.5.1"
    pify "^4.0.1"

babel-plugin-dynamic-import-node@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.3.0.tgz#f00f507bdaa3c3e3ff6e7e5e98d90a7acab96f7f"
  integrity sha1-8A9Qe9qjw+P/bn5emNkKesq5b38=
  dependencies:
    object.assign "^4.1.0"

babel-plugin-import@^1.11.0:
  version "1.12.1"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-import/download/babel-plugin-import-1.12.1.tgz#a63b0a6f8f7484db660c59665185aa3b0c2f9f3f"
  integrity sha1-pjsKb490hNtmDFlmUYWqOwwvnz8=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0"
    "@babel/runtime" "^7.0.0"

babel-plugin-istanbul@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-istanbul/download/babel-plugin-istanbul-6.0.0.tgz#e159ccdc9af95e0b570c75b4573b7c34d671d765"
  integrity sha1-4VnM3Jr5XgtXDHW0Vzt8NNZx12U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^4.0.0"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-25.1.0.tgz#fb62d7b3b53eb36c97d1bc7fec2072f9bd115981"
  integrity sha1-+2LXs7U+s2yX0bx/7CBy+b0RWYE=
  dependencies:
    "@types/babel__traverse" "^7.0.6"

babel-plugin-jest-hoist@^25.2.6:
  version "25.2.6"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-jest-hoist/download/babel-plugin-jest-hoist-25.2.6.tgz#2af07632b8ac7aad7d414c1e58425d5fc8e84909"
  integrity sha1-KvB2Mriseq19QUweWEJdX8joSQk=
  dependencies:
    "@types/babel__traverse" "^7.0.6"

babel-plugin-lodash@^3.3.4:
  version "3.3.4"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-lodash/download/babel-plugin-lodash-3.3.4.tgz#4f6844358a1340baed182adbeffa8df9967bc196"
  integrity sha1-T2hENYoTQLrtGCrb7/qN+ZZ7wZY=
  dependencies:
    "@babel/helper-module-imports" "^7.0.0-beta.49"
    "@babel/types" "^7.0.0-beta.49"
    glob "^7.1.1"
    lodash "^4.17.10"
    require-package-name "^2.0.1"

babel-plugin-transform-amd-to-commonjs@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-transform-amd-to-commonjs/download/babel-plugin-transform-amd-to-commonjs-1.4.0.tgz#d9bc5003eaa26dbdd4e854e453f84903852af2ca"
  integrity sha1-2bxQA+qibb3U6FTkU/hJA4Uq8so=

babel-plugin-zent@2.2.2:
  version "2.2.2"
  resolved "http://registry.npm.qima-inc.com/babel-plugin-zent/download/babel-plugin-zent-2.2.2.tgz#00db78acf04a516bdfdb292f95043eecc1455a35"
  integrity sha1-ANt4rPBKUWvf2ykvlQQ+7MFFWjU=
  dependencies:
    "@babel/runtime" "^7.1.5"

babel-preset-current-node-syntax@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/babel-preset-current-node-syntax/download/babel-preset-current-node-syntax-0.1.2.tgz#fb4a4c51fe38ca60fede1dc74ab35eb843cb41d6"
  integrity sha1-+0pMUf44ymD+3h3HSrNeuEPLQdY=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.8.3"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.8.3"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.8.3"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"

babel-preset-jest@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/babel-preset-jest/download/babel-preset-jest-25.1.0.tgz#d0aebfebb2177a21cde710996fce8486d34f1d33"
  integrity sha1-0K6/67IXeiHN5xCZb86EhtNPHTM=
  dependencies:
    "@babel/plugin-syntax-bigint" "^7.0.0"
    "@babel/plugin-syntax-object-rest-spread" "^7.0.0"
    babel-plugin-jest-hoist "^25.1.0"

babel-preset-jest@^25.3.0:
  version "25.3.0"
  resolved "http://registry.npm.qima-inc.com/babel-preset-jest/download/babel-preset-jest-25.3.0.tgz#9ab40aee52a19bdc52b8b1ec2403d5914ac3d86b"
  integrity sha1-mrQK7lKhm9xSuLHsJAPVkUrD2Gs=
  dependencies:
    babel-plugin-jest-hoist "^25.2.6"
    babel-preset-current-node-syntax "^0.1.2"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"
  integrity sha1-ibTRmasr7kneFk6gK4nORi1xt2c=

base-64@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/base-64/download/base-64-0.1.0.tgz#780a99c84e7d600260361511c4877613bf24f6bb"
  integrity sha1-eAqZyE59YAJgNhURxId2E78k9rs=

base64-js@^1.0.2:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/base64-js/download/base64-js-1.3.1.tgz#58ece8cb75dd07e71ed08c736abc5fac4dbf8df1"
  integrity sha1-WOzoy3XdB+ce0IxzarxfrE2/jfE=

base@^0.11.1:
  version "0.11.2"
  resolved "http://registry.npm.qima-inc.com/base/download/base-0.11.2.tgz#7bde5ced145b6d551a90db87f83c558b4eb48a8f"
  integrity sha1-e95c7RRbbVUakNuH+DxVi060io8=
  dependencies:
    cache-base "^1.0.1"
    class-utils "^0.3.5"
    component-emitter "^1.2.1"
    define-property "^1.0.0"
    isobject "^3.0.1"
    mixin-deep "^1.2.0"
    pascalcase "^0.1.1"

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

bfj@^6.1.1:
  version "6.1.2"
  resolved "http://registry.npm.qima-inc.com/bfj/download/bfj-6.1.2.tgz#325c861a822bcb358a41c78a33b8e6e2086dde7f"
  integrity sha1-MlyGGoIryzWKQceKM7jm4ght3n8=
  dependencies:
    bluebird "^3.5.5"
    check-types "^8.0.3"
    hoopy "^0.1.4"
    tryer "^1.0.1"

big.js@^5.2.2:
  version "5.2.2"
  resolved "http://registry.npm.qima-inc.com/big.js/download/big.js-5.2.2.tgz#65f0af382f578bcdc742bd9c281e9cb2d7768328"
  integrity sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=

binary-extensions@^1.0.0:
  version "1.13.1"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-1.13.1.tgz#598afe54755b2868a5330d2aff9d4ebb53209b65"
  integrity sha1-WYr+VHVbKGilMw0q/51Ou1Mgm2U=

binary-extensions@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/binary-extensions/download/binary-extensions-2.0.0.tgz#23c0df14f6a88077f5f986c0d167ec03c3d5537c"
  integrity sha1-I8DfFPaogHf1+YbA0WfsA8PVU3w=

bindings@^1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bluebird@^3.5.5:
  version "3.5.5"
  resolved "http://registry.npm.qima-inc.com/bluebird/download/bluebird-3.5.5.tgz#a8d0afd73251effbbd5fe384a77d73003c17a71f"
  integrity sha1-qNCv1zJR7/u9X+OEp31zADwXpx8=

bn.js@^4.0.0, bn.js@^4.1.0, bn.js@^4.1.1, bn.js@^4.4.0:
  version "4.11.8"
  resolved "http://registry.npm.qima-inc.com/bn.js/download/bn.js-4.11.8.tgz#2cde09eb5ee341f484746bb0309b3253b1b1442f"
  integrity sha1-LN4J617jQfSEdGuwMJsyU7GxRC8=

body-parser@1.19.0:
  version "1.19.0"
  resolved "http://registry.npm.qima-inc.com/body-parser/download/body-parser-1.19.0.tgz#96b2709e57c9c4e09a6fd66a8fd979844f69f08a"
  integrity sha1-lrJwnlfJxOCab9Zqj9l5hE9p8Io=
  dependencies:
    bytes "3.1.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.2"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    on-finished "~2.3.0"
    qs "6.7.0"
    raw-body "2.4.0"
    type-is "~1.6.17"

boolbase@^1.0.0, boolbase@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.npm.qima-inc.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^2.3.1, braces@^2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-2.3.2.tgz#5979fd3f14cd531565e5fa2df1abfff1dfaee729"
  integrity sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=
  dependencies:
    arr-flatten "^1.1.0"
    array-unique "^0.3.2"
    extend-shallow "^2.0.1"
    fill-range "^4.0.0"
    isobject "^3.0.1"
    repeat-element "^1.1.2"
    snapdragon "^0.8.1"
    snapdragon-node "^2.0.1"
    split-string "^3.0.2"
    to-regex "^3.0.1"

braces@^3.0.1, braces@^3.0.2, braces@~3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/braces/download/braces-3.0.2.tgz#3454e1a462ee8d599e236df336cd9ea4f8afe107"
  integrity sha1-NFThpGLujVmeI23zNs2epPiv4Qc=
  dependencies:
    fill-range "^7.0.1"

brorand@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/brorand/download/brorand-1.1.0.tgz#12c25efe40a45e3c323eb8675a0a0ce57b22371f"
  integrity sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=

browser-process-hrtime@^0.1.2:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/browser-process-hrtime/download/browser-process-hrtime-0.1.3.tgz#616f00faef1df7ec1b5bf9cfe2bdc3170f26c7b4"
  integrity sha1-YW8A+u8d9+wbW/nP4r3DFw8mx7Q=

browser-resolve@^1.11.3:
  version "1.11.3"
  resolved "http://registry.npm.qima-inc.com/browser-resolve/download/browser-resolve-1.11.3.tgz#9b7cbb3d0f510e4cb86bdbd796124d28b5890af6"
  integrity sha1-m3y7PQ9RDky4a9vXlhJNKLWJCvY=
  dependencies:
    resolve "1.1.7"

browserify-aes@^1.0.0, browserify-aes@^1.0.4:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/browserify-aes/download/browserify-aes-1.2.0.tgz#326734642f403dabc3003209853bb70ad428ef48"
  integrity sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=
  dependencies:
    buffer-xor "^1.0.3"
    cipher-base "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.3"
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

browserify-cipher@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/browserify-cipher/download/browserify-cipher-1.0.1.tgz#8d6474c1b870bfdabcd3bcfcc1934a10e94f15f0"
  integrity sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=
  dependencies:
    browserify-aes "^1.0.4"
    browserify-des "^1.0.0"
    evp_bytestokey "^1.0.0"

browserify-des@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/browserify-des/download/browserify-des-1.0.2.tgz#3af4f1f59839403572f1c66204375f7a7f703e9c"
  integrity sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=
  dependencies:
    cipher-base "^1.0.1"
    des.js "^1.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

browserify-rsa@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/browserify-rsa/download/browserify-rsa-4.0.1.tgz#21e0abfaf6f2029cf2fafb133567a701d4135524"
  integrity sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=
  dependencies:
    bn.js "^4.1.0"
    randombytes "^2.0.1"

browserify-sign@^4.0.0:
  version "4.0.4"
  resolved "http://registry.npm.qima-inc.com/browserify-sign/download/browserify-sign-4.0.4.tgz#aa4eb68e5d7b658baa6bf6a57e630cbd7a93d298"
  integrity sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=
  dependencies:
    bn.js "^4.1.1"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.2"
    elliptic "^6.0.0"
    inherits "^2.0.1"
    parse-asn1 "^5.0.0"

browserify-zlib@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/browserify-zlib/download/browserify-zlib-0.2.0.tgz#2869459d9aa3be245fe8fe2ca1f46e2e7f54d73f"
  integrity sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=
  dependencies:
    pako "~1.0.5"

browserslist@^4.0.0, browserslist@^4.6.0, browserslist@^4.6.3, browserslist@^4.6.6:
  version "4.7.0"
  resolved "http://registry.npm.qima-inc.com/browserslist/download/browserslist-4.7.0.tgz#9ee89225ffc07db03409f2fee524dc8227458a17"
  integrity sha1-nuiSJf/AfbA0CfL+5STcgidFihc=
  dependencies:
    caniuse-lite "^1.0.30000989"
    electron-to-chromium "^1.3.247"
    node-releases "^1.1.29"

bs-logger@0.x:
  version "0.2.6"
  resolved "http://registry.npm.qima-inc.com/bs-logger/download/bs-logger-0.2.6.tgz#eb7d365307a72cf974cc6cda76b68354ad336bd8"
  integrity sha1-6302UwenLPl0zGzadraDVK0za9g=
  dependencies:
    fast-json-stable-stringify "2.x"

bser@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/bser/download/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-from@1.x, buffer-from@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/buffer-from/download/buffer-from-1.1.1.tgz#32713bc028f75c02fdb710d7c7bcec1f2c6070ef"
  integrity sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=

buffer-json@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/buffer-json/download/buffer-json-2.0.0.tgz#f73e13b1e42f196fe2fd67d001c7d7107edd7c23"
  integrity sha1-9z4TseQvGW/i/WfQAcfXEH7dfCM=

buffer-xor@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/buffer-xor/download/buffer-xor-1.0.3.tgz#26e61ed1422fb70dd42e6e36729ed51d855fe8d9"
  integrity sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=

buffer@^4.3.0:
  version "4.9.1"
  resolved "http://registry.npm.qima-inc.com/buffer/download/buffer-4.9.1.tgz#6d1bb601b07a4efced97094132093027c95bc298"
  integrity sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"
  integrity sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=

bytes@3.1.0, bytes@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/bytes/download/bytes-3.1.0.tgz#f6cf7933a360e0588fa9fde85651cdc7f805d1f6"
  integrity sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=

cacache@^12.0.2:
  version "12.0.3"
  resolved "http://registry.npm.qima-inc.com/cacache/download/cacache-12.0.3.tgz#be99abba4e1bf5df461cd5a2c1071fc432573390"
  integrity sha1-vpmruk4b9d9GHNWiwQcfxDJXM5A=
  dependencies:
    bluebird "^3.5.5"
    chownr "^1.1.1"
    figgy-pudding "^3.5.1"
    glob "^7.1.4"
    graceful-fs "^4.1.15"
    infer-owner "^1.0.3"
    lru-cache "^5.1.1"
    mississippi "^3.0.0"
    mkdirp "^0.5.1"
    move-concurrently "^1.0.1"
    promise-inflight "^1.0.1"
    rimraf "^2.6.3"
    ssri "^6.0.1"
    unique-filename "^1.1.1"
    y18n "^4.0.0"

cache-base@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-base/download/cache-base-1.0.1.tgz#0a7f46416831c8b662ee36fe4e7c59d76f666ab2"
  integrity sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=
  dependencies:
    collection-visit "^1.0.0"
    component-emitter "^1.2.1"
    get-value "^2.0.6"
    has-value "^1.0.0"
    isobject "^3.0.1"
    set-value "^2.0.0"
    to-object-path "^0.3.0"
    union-value "^1.0.0"
    unset-value "^1.0.0"

cache-content-type@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cache-content-type/download/cache-content-type-1.0.1.tgz#035cde2b08ee2129f4a8315ea8f00a00dba1453c"
  integrity sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=
  dependencies:
    mime-types "^2.1.18"
    ylru "^1.2.0"

cache-loader@^4.0.1:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/cache-loader/download/cache-loader-4.1.0.tgz#9948cae353aec0a1fcb1eafda2300816ec85387e"
  integrity sha1-mUjK41OuwKH8ser9ojAIFuyFOH4=
  dependencies:
    buffer-json "^2.0.0"
    find-cache-dir "^3.0.0"
    loader-utils "^1.2.3"
    mkdirp "^0.5.1"
    neo-async "^2.6.1"
    schema-utils "^2.0.0"

caller-callsite@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-callsite/download/caller-callsite-2.0.0.tgz#847e0fce0a223750a9a027c54b33731ad3154134"
  integrity sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=
  dependencies:
    callsites "^2.0.0"

caller-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/caller-path/download/caller-path-2.0.0.tgz#468f83044e369ab2010fac5f06ceee15bb2cb1f4"
  integrity sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=
  dependencies:
    caller-callsite "^2.0.0"

callsites@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-2.0.0.tgz#06eb84f00eea413da86affefacbffb36093b3c50"
  integrity sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase@^5.0.0, camelcase@^5.3.1:
  version "5.3.1"
  resolved "http://registry.npm.qima-inc.com/camelcase/download/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

caniuse-api@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/caniuse-api/download/caniuse-api-3.0.0.tgz#5e4d90e2274961d46291997df599e3ed008ee4c0"
  integrity sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30000980, caniuse-lite@^1.0.30000989:
  version "1.0.30000989"
  resolved "http://registry.npm.qima-inc.com/caniuse-lite/download/caniuse-lite-1.0.30000989.tgz#b9193e293ccf7e4426c5245134b8f2a56c0ac4b9"
  integrity sha1-uRk+KTzPfkQmxSRRNLjypWwKxLk=

capture-exit@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/capture-exit/download/capture-exit-2.0.0.tgz#fb953bfaebeb781f62898239dabb426d08a509a4"
  integrity sha1-+5U7+uvreB9iiYI52rtCbQilCaQ=
  dependencies:
    rsvp "^4.8.4"

case-sensitive-paths-webpack-plugin@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.2.0.tgz#3371ef6365ef9c25fa4b81c16ace0e9c7dc58c3e"
  integrity sha1-M3HvY2XvnCX6S4HBas4OnH3FjD4=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.qima-inc.com/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

chalk@^1.0.0, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.0.1, chalk@^2.1.0, chalk@^2.3.0, chalk@^2.3.1, chalk@^2.3.2, chalk@^2.4.1, chalk@^2.4.2:
  version "2.4.2"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/chalk/download/chalk-4.1.0.tgz#4e14870a618d9e2edd97dd8345fd9d9dc315646a"
  integrity sha1-ThSHCmGNni7dl92DRf2dncMVZGo=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

charenc@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/charenc/download/charenc-0.0.2.tgz#c0a1d2f3a7092e03774bfa83f14c0fc5790a8667"
  integrity sha1-wKHS86cJLgN3S/qD8UwPxXkKhmc=

check-types@^8.0.3:
  version "8.0.3"
  resolved "http://registry.npm.qima-inc.com/check-types/download/check-types-8.0.3.tgz#3356cca19c889544f2d7a95ed49ce508a0ecf552"
  integrity sha1-M1bMoZyIlUTy16le1JzlCKDs9VI=

"chokidar@>=2.0.0 <4.0.0":
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-3.0.2.tgz#0d1cd6d04eb2df0327446188cd13736a3367d681"
  integrity sha1-DRzW0E6y3wMnRGGIzRNzajNn1oE=
  dependencies:
    anymatch "^3.0.1"
    braces "^3.0.2"
    glob-parent "^5.0.0"
    is-binary-path "^2.1.0"
    is-glob "^4.0.1"
    normalize-path "^3.0.0"
    readdirp "^3.1.1"
  optionalDependencies:
    fsevents "^2.0.6"

chokidar@^2.1.8:
  version "2.1.8"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-2.1.8.tgz#804b3a7b6a99358c3c5c61e71d8728f041cff917"
  integrity sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=
  dependencies:
    anymatch "^2.0.0"
    async-each "^1.0.1"
    braces "^2.3.2"
    glob-parent "^3.1.0"
    inherits "^2.0.3"
    is-binary-path "^1.0.0"
    is-glob "^4.0.0"
    normalize-path "^3.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.2.1"
    upath "^1.1.1"
  optionalDependencies:
    fsevents "^1.2.7"

chokidar@^3.4.0:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/chokidar/download/chokidar-3.4.0.tgz#b30611423ce376357c765b9b8f904b9fba3c0be8"
  integrity sha1-swYRQjzjdjV8dlubj5BLn7o8C+g=
  dependencies:
    anymatch "~3.1.1"
    braces "~3.0.2"
    glob-parent "~5.1.0"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.4.0"
  optionalDependencies:
    fsevents "~2.1.2"

chownr@^1.1.1:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/chownr/download/chownr-1.1.2.tgz#a18f1e0b269c8a6a5d3c86eb298beb14c3dd7bf6"
  integrity sha1-oY8eCyacimpdPIbrKYvrFMPde/Y=

chrome-trace-event@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/chrome-trace-event/download/chrome-trace-event-1.0.2.tgz#234090ee97c7d4ad1a2c4beae27505deffc608a4"
  integrity sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=
  dependencies:
    tslib "^1.9.0"

ci-info@^1.5.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-1.6.0.tgz#2ca20dbb9ceb32d4524a683303313f0304b1e497"
  integrity sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=

ci-info@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ci-info/download/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

cipher-base@^1.0.0, cipher-base@^1.0.1, cipher-base@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/cipher-base/download/cipher-base-1.0.4.tgz#8760e4ecc272f4c363532f926d874aae2c1397de"
  integrity sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

class-utils@^0.3.5:
  version "0.3.6"
  resolved "http://registry.npm.qima-inc.com/class-utils/download/class-utils-0.3.6.tgz#f93369ae8b9a7ce02fd41faad0ca83033190c463"
  integrity sha1-+TNprouafOAv1B+q0MqDAzGQxGM=
  dependencies:
    arr-union "^3.1.0"
    define-property "^0.2.5"
    isobject "^3.0.0"
    static-extend "^0.1.1"

cli-cursor@^2.0.0, cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-progress@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/cli-progress/download/cli-progress-3.1.0.tgz#908fafcbdb41dc3c272b8accdc51677a113782af"
  integrity sha1-kI+vy9tB3DwnK4rM3FFnehE3gq8=
  dependencies:
    colors "^1.1.2"
    string-width "^2.1.1"

cli-spinners@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/cli-spinners/download/cli-spinners-2.2.0.tgz#e8b988d9206c692302d8ee834e7a85c0144d8f77"
  integrity sha1-6LmI2SBsaSMC2O6DTnqFwBRNj3c=

cli-truncate@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/cli-truncate/download/cli-truncate-0.2.1.tgz#9f15cfbb0705005369216c626ac7d05ab90dd574"
  integrity sha1-nxXPuwcFAFNpIWxiasfQWrkN1XQ=
  dependencies:
    slice-ansi "0.0.4"
    string-width "^1.0.1"

cli-width@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/cli-width/download/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"
  integrity sha1-/xnt6Kml5XkyQUewwR8PvLq+1jk=

cliui@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/cliui/download/cliui-6.0.0.tgz#511d702c0c4e41ca156d7d0e96021f23e13225b1"
  integrity sha1-UR1wLAxOQcoVbX0OlgIfI+EyJbE=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^6.2.0"

clone-deep@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/clone-deep/download/clone-deep-4.0.1.tgz#c19fd9bdbbf85942b4fd979c84dcf7d5f07c2387"
  integrity sha1-wZ/Zvbv4WUK0/ZechNz31fB8I4c=
  dependencies:
    is-plain-object "^2.0.4"
    kind-of "^6.0.2"
    shallow-clone "^3.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.qima-inc.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

coa@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/coa/download/coa-2.0.2.tgz#43f6c21151b4ef2bf57187db0d73de229e3e7ec3"
  integrity sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=
  dependencies:
    "@types/q" "^1.5.1"
    chalk "^2.4.1"
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"
  integrity sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=

collect-v8-coverage@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/collect-v8-coverage/download/collect-v8-coverage-1.0.0.tgz#150ee634ac3650b71d9c985eb7f608942334feb1"
  integrity sha1-FQ7mNKw2ULcdnJhet/YIlCM0/rE=

collection-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/collection-visit/download/collection-visit-1.0.0.tgz#4bc0373c164bc3291b4d368c829cf1a80a59dca0"
  integrity sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=
  dependencies:
    map-visit "^1.0.0"
    object-visit "^1.0.0"

color-convert@^1.9.0, color-convert@^1.9.1:
  version "1.9.3"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.5.2:
  version "1.5.3"
  resolved "http://registry.npm.qima-inc.com/color-string/download/color-string-1.5.3.tgz#c9bbc5f01b58b5492f3d6857459cb6590ce204cc"
  integrity sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color@^3.0.0:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/color/download/color-3.1.2.tgz#68148e7f85d41ad7649c5fa8c8106f098d229e10"
  integrity sha1-aBSOf4XUGtdknF+oyBBvCY0inhA=
  dependencies:
    color-convert "^1.9.1"
    color-string "^1.5.2"

colors@^1.1.2:
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/colors/download/colors-1.3.3.tgz#39e005d546afe01e01f9c4ca8fa50f686a01205d"
  integrity sha1-OeAF1Uav4B4B+cTKj6UPaGoBIF0=

combined-stream@^1.0.6, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.14.1, commander@^2.18.0, commander@^2.20.0, commander@^2.9.0:
  version "2.20.0"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-2.20.0.tgz#d58bb2b5c1ee8f87b0d340027e9e94e222c5a422"
  integrity sha1-1YuytcHuj4ew00ACfp6U4iLFpCI=

commander@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/commander/download/commander-5.1.0.tgz#46abbd1652f8e059bddaef99bbdcb2ad9cf179ae"
  integrity sha1-Rqu9FlL44Fm92u+Zu9yyrZzxea4=

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compare-versions@^3.5.0:
  version "3.5.1"
  resolved "http://registry.npm.qima-inc.com/compare-versions/download/compare-versions-3.5.1.tgz#26e1f5cf0d48a77eced5046b9f67b6b61075a393"
  integrity sha1-JuH1zw1Ip37O1QRrn2e2thB1o5M=

component-emitter@^1.2.1:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/component-emitter/download/component-emitter-1.3.0.tgz#16e4070fba8ae29b679f2215853ee181ab2eabc0"
  integrity sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=

compressible@^2.0.0:
  version "2.0.17"
  resolved "http://registry.npm.qima-inc.com/compressible/download/compressible-2.0.17.tgz#6e8c108a16ad58384a977f3a482ca20bff2f38c1"
  integrity sha1-bowQihatWDhKl386SCyiC/8vOME=
  dependencies:
    mime-db ">= 1.40.0 < 2"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.5.0:
  version "1.6.2"
  resolved "http://registry.npm.qima-inc.com/concat-stream/download/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

connect-history-api-fallback@^1.5.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

consola@^1.3.0:
  version "1.4.5"
  resolved "http://registry.npm.qima-inc.com/consola/download/consola-1.4.5.tgz#09732d07cb50af07332e54e0f42fafb92b962c4a"
  integrity sha1-CXMtB8tQrwczLlTg9C+vuSuWLEo=
  dependencies:
    chalk "^2.3.2"
    figures "^2.0.0"
    lodash "^4.17.5"
    std-env "^1.1.0"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/console-browserify/download/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  integrity sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=
  dependencies:
    date-now "^0.1.4"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"
  integrity sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=

content-disposition@0.5.3, content-disposition@~0.5.2:
  version "0.5.3"
  resolved "http://registry.npm.qima-inc.com/content-disposition/download/content-disposition-0.5.3.tgz#e130caf7e7279087c5616c2007d0485698984fbd"
  integrity sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=
  dependencies:
    safe-buffer "5.1.2"

content-type@^1.0.2, content-type@^1.0.4, content-type@~1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/content-type/download/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"
  integrity sha1-4TjMdeBAxyexlm/l5fjJruJW/js=

convert-source-map@^1.1.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/convert-source-map/download/convert-source-map-1.6.0.tgz#51b537a8c43e0f04dec1993bffcdd504e758ac20"
  integrity sha1-UbU3qMQ+DwTewZk7/83VBOdYrCA=
  dependencies:
    safe-buffer "~5.1.1"

convert-source-map@^1.4.0, convert-source-map@^1.6.0, convert-source-map@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/convert-source-map/download/convert-source-map-1.7.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fconvert-source-map%2Fdownload%2Fconvert-source-map-1.7.0.tgz#17a2cb882d7f77d3490585e2ce6c524424a3a442"
  integrity sha1-F6LLiC1/d9NJBYXizmxSRCSjpEI=
  dependencies:
    safe-buffer "~5.1.1"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"
  integrity sha1-4wOogrNCzD7oylE6eZmXNNqzriw=

cookie@0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/cookie/download/cookie-0.4.0.tgz#beb437e7022b3b6d49019d088665303ebe9c14ba"
  integrity sha1-vrQ35wIrO21JAZ0IhmUwPr6cFLo=

cookies@~0.7.1:
  version "0.7.3"
  resolved "http://registry.npm.qima-inc.com/cookies/download/cookies-0.7.3.tgz#7912ce21fbf2e8c2da70cf1c3f351aecf59dadfa"
  integrity sha1-eRLOIfvy6MLacM8cPzUa7PWdrfo=
  dependencies:
    depd "~1.1.2"
    keygrip "~1.0.3"

copy-concurrently@^1.0.0:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/copy-concurrently/download/copy-concurrently-1.0.5.tgz#92297398cae34937fcafd6ec8139c18051f0b5e0"
  integrity sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=
  dependencies:
    aproba "^1.1.1"
    fs-write-stream-atomic "^1.0.8"
    iferr "^0.1.5"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.0"

copy-descriptor@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/copy-descriptor/download/copy-descriptor-0.1.1.tgz#676f6eb3c39997c2ee1ac3a924fd6124748f578d"
  integrity sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=

core-js-compat@^3.1.1:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/core-js-compat/download/core-js-compat-3.2.1.tgz#0cbdbc2e386e8e00d3b85dc81c848effec5b8150"
  integrity sha1-DL28LjhujgDTuF3IHISO/+xbgVA=
  dependencies:
    browserslist "^4.6.6"
    semver "^6.3.0"

core-util-is@1.0.2, core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

cosmiconfig@^5.0.0, cosmiconfig@^5.0.7, cosmiconfig@^5.2.0:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/cosmiconfig/download/cosmiconfig-5.2.1.tgz#040f726809c591e77a17c0a3626ca45b4f168b1a"
  integrity sha1-BA9yaAnFked6F8CjYmykW08Wixo=
  dependencies:
    import-fresh "^2.0.0"
    is-directory "^0.3.1"
    js-yaml "^3.13.1"
    parse-json "^4.0.0"

crc32@0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/crc32/download/crc32-0.2.2.tgz#7ad220d6ffdcd119f9fc127a7772cacea390a4ba"
  integrity sha1-etIg1v/c0Rn5/BJ6d3LKzqOQpLo=

create-ecdh@^4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/create-ecdh/download/create-ecdh-4.0.3.tgz#c9111b6f33045c4697f144787f9254cdc77c45ff"
  integrity sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8=
  dependencies:
    bn.js "^4.1.0"
    elliptic "^6.0.0"

create-hash@^1.1.0, create-hash@^1.1.2:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/create-hash/download/create-hash-1.2.0.tgz#889078af11a63756bcfb59bd221996be3a9ef196"
  integrity sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=
  dependencies:
    cipher-base "^1.0.1"
    inherits "^2.0.1"
    md5.js "^1.3.4"
    ripemd160 "^2.0.1"
    sha.js "^2.4.0"

create-hmac@^1.1.0, create-hmac@^1.1.2, create-hmac@^1.1.4:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/create-hmac/download/create-hmac-1.1.7.tgz#69170c78b3ab957147b2b8b04572e47ead2243ff"
  integrity sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=
  dependencies:
    cipher-base "^1.0.3"
    create-hash "^1.1.0"
    inherits "^2.0.1"
    ripemd160 "^2.0.0"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

cross-env@^5.2.0:
  version "5.2.1"
  resolved "http://registry.npm.qima-inc.com/cross-env/download/cross-env-5.2.1.tgz#b2c76c1ca7add66dc874d11798466094f551b34d"
  integrity sha1-ssdsHKet1m3IdNEXmEZglPVRs00=
  dependencies:
    cross-spawn "^6.0.5"

cross-spawn@^6.0.0, cross-spawn@^6.0.5:
  version "6.0.5"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-6.0.5.tgz#4a5ec7c64dfae22c3a14124dbacdee846d80cbc4"
  integrity sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=
  dependencies:
    nice-try "^1.0.4"
    path-key "^2.0.1"
    semver "^5.5.0"
    shebang-command "^1.2.0"
    which "^1.2.9"

cross-spawn@^7.0.0:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/cross-spawn/download/cross-spawn-7.0.1.tgz#0ab56286e0f7c24e153d04cc2aa027e43a9a5d14"
  integrity sha1-CrVihuD3wk4VPQTMKqAn5DqaXRQ=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypt@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/crypt/download/crypt-0.0.2.tgz#88d7ff7ec0dfb86f713dc87bbb42d044d3e6c41b"
  integrity sha1-iNf/fsDfuG9xPch7u0LQRNPmxBs=

crypto-browserify@^3.11.0:
  version "3.12.0"
  resolved "http://registry.npm.qima-inc.com/crypto-browserify/download/crypto-browserify-3.12.0.tgz#396cf9f3137f03e4b8e532c58f698254e00f80ec"
  integrity sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=
  dependencies:
    browserify-cipher "^1.0.0"
    browserify-sign "^4.0.0"
    create-ecdh "^4.0.0"
    create-hash "^1.1.0"
    create-hmac "^1.1.0"
    diffie-hellman "^5.0.0"
    inherits "^2.0.1"
    pbkdf2 "^3.0.3"
    public-encrypt "^4.0.0"
    randombytes "^2.0.0"
    randomfill "^1.0.3"

css-color-names@0.0.4, css-color-names@^0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.qima-inc.com/css-color-names/download/css-color-names-0.0.4.tgz#808adc2e79cf84738069b646cb20ec27beb629e0"
  integrity sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=

css-declaration-sorter@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz#c198940f63a76d7e36c1e71018b001721054cb22"
  integrity sha1-wZiUD2OnbX42wecQGLABchBUyyI=
  dependencies:
    postcss "^7.0.1"
    timsort "^0.3.0"

css-loader@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/css-loader/download/css-loader-3.2.0.tgz#bb570d89c194f763627fcf1f80059c6832d009b2"
  integrity sha1-u1cNicGU92Nif88fgAWcaDLQCbI=
  dependencies:
    camelcase "^5.3.1"
    cssesc "^3.0.0"
    icss-utils "^4.1.1"
    loader-utils "^1.2.3"
    normalize-path "^3.0.0"
    postcss "^7.0.17"
    postcss-modules-extract-imports "^2.0.0"
    postcss-modules-local-by-default "^3.0.2"
    postcss-modules-scope "^2.1.0"
    postcss-modules-values "^3.0.0"
    postcss-value-parser "^4.0.0"
    schema-utils "^2.0.0"

css-select-base-adapter@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz#3b2ff4972cc362ab88561507a95408a1432135d7"
  integrity sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=

css-select@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/css-select/download/css-select-2.0.2.tgz#ab4386cec9e1f668855564b17c3733b43b2a5ede"
  integrity sha1-q0OGzsnh9miFVWSxfDcztDsqXt4=
  dependencies:
    boolbase "^1.0.0"
    css-what "^2.1.2"
    domutils "^1.7.0"
    nth-check "^1.0.2"

css-tree@1.0.0-alpha.29:
  version "1.0.0-alpha.29"
  resolved "http://registry.npm.qima-inc.com/css-tree/download/css-tree-1.0.0-alpha.29.tgz#3fa9d4ef3142cbd1c301e7664c1f352bd82f5a39"
  integrity sha1-P6nU7zFCy9HDAedmTB81K9gvWjk=
  dependencies:
    mdn-data "~1.1.0"
    source-map "^0.5.3"

css-tree@1.0.0-alpha.33:
  version "1.0.0-alpha.33"
  resolved "http://registry.npm.qima-inc.com/css-tree/download/css-tree-1.0.0-alpha.33.tgz#970e20e5a91f7a378ddd0fc58d0b6c8d4f3be93e"
  integrity sha1-lw4g5akfejeN3Q/FjQtsjU876T4=
  dependencies:
    mdn-data "2.0.4"
    source-map "^0.5.3"

css-unit-converter@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/css-unit-converter/download/css-unit-converter-1.1.1.tgz#d9b9281adcfd8ced935bdbaba83786897f64e996"
  integrity sha1-2bkoGtz9jO2TW9urqDeGiX9k6ZY=

css-what@^2.1.2:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/css-what/download/css-what-2.1.3.tgz#a6d7604573365fe74686c3f311c56513d88285f2"
  integrity sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI=

cssesc@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/cssesc/download/cssesc-2.0.0.tgz#3b13bd1bb1cb36e1bcb5a4dcd27f54c5dcb35703"
  integrity sha1-OxO9G7HLNuG8taTc0n9UxdyzVwM=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

cssnano-preset-default@^4.0.7:
  version "4.0.7"
  resolved "http://registry.npm.qima-inc.com/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz#51ec662ccfca0f88b396dcd9679cdb931be17f76"
  integrity sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y=
  dependencies:
    css-declaration-sorter "^4.0.1"
    cssnano-util-raw-cache "^4.0.1"
    postcss "^7.0.0"
    postcss-calc "^7.0.1"
    postcss-colormin "^4.0.3"
    postcss-convert-values "^4.0.1"
    postcss-discard-comments "^4.0.2"
    postcss-discard-duplicates "^4.0.2"
    postcss-discard-empty "^4.0.1"
    postcss-discard-overridden "^4.0.1"
    postcss-merge-longhand "^4.0.11"
    postcss-merge-rules "^4.0.3"
    postcss-minify-font-values "^4.0.2"
    postcss-minify-gradients "^4.0.2"
    postcss-minify-params "^4.0.2"
    postcss-minify-selectors "^4.0.2"
    postcss-normalize-charset "^4.0.1"
    postcss-normalize-display-values "^4.0.2"
    postcss-normalize-positions "^4.0.2"
    postcss-normalize-repeat-style "^4.0.2"
    postcss-normalize-string "^4.0.2"
    postcss-normalize-timing-functions "^4.0.2"
    postcss-normalize-unicode "^4.0.1"
    postcss-normalize-url "^4.0.1"
    postcss-normalize-whitespace "^4.0.2"
    postcss-ordered-values "^4.1.2"
    postcss-reduce-initial "^4.0.3"
    postcss-reduce-transforms "^4.0.2"
    postcss-svgo "^4.0.2"
    postcss-unique-selectors "^4.0.1"

cssnano-util-get-arguments@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz#ed3a08299f21d75741b20f3b81f194ed49cc150f"
  integrity sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=

cssnano-util-get-match@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz#c0e4ca07f5386bb17ec5e52250b4f5961365156d"
  integrity sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=

cssnano-util-raw-cache@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz#b26d5fd5f72a11dfe7a7846fb4c67260f96bf282"
  integrity sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=
  dependencies:
    postcss "^7.0.0"

cssnano-util-same-parent@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz#574082fb2859d2db433855835d9a8456ea18bbf3"
  integrity sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=

cssnano@^4.1.10:
  version "4.1.10"
  resolved "http://registry.npm.qima-inc.com/cssnano/download/cssnano-4.1.10.tgz#0ac41f0b13d13d465487e111b778d42da631b8b2"
  integrity sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI=
  dependencies:
    cosmiconfig "^5.0.0"
    cssnano-preset-default "^4.0.7"
    is-resolvable "^1.0.0"
    postcss "^7.0.0"

csso@^3.5.1:
  version "3.5.1"
  resolved "http://registry.npm.qima-inc.com/csso/download/csso-3.5.1.tgz#7b9eb8be61628973c1b261e169d2f024008e758b"
  integrity sha1-e564vmFiiXPBsmHhadLwJACOdYs=
  dependencies:
    css-tree "1.0.0-alpha.29"

cssom@^0.4.1:
  version "0.4.4"
  resolved "http://registry.npm.qima-inc.com/cssom/download/cssom-0.4.4.tgz#5a66cf93d2d0b661d80bf6a44fb65f5c2e4e0a10"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "http://registry.npm.qima-inc.com/cssom/download/cssom-0.3.8.tgz?cache=0&sync_timestamp=1573441812483&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fcssom%2Fdownload%2Fcssom-0.3.8.tgz#9f1276f5b2b463f2114d3f2c75250af8c1a36f4a"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/cssstyle/download/cssstyle-2.2.0.tgz#e4c44debccd6b7911ed617a4395e5754bba59992"
  integrity sha1-5MRN68zWt5Ee1hekOV5XVLulmZI=
  dependencies:
    cssom "~0.3.6"

cyclist@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/cyclist/download/cyclist-1.0.1.tgz#596e9698fd0c80e12038c2b82d6eb1b35b6224d9"
  integrity sha1-WW6WmP0MgOEgOMK4LW6xs1tiJNk=

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://registry.npm.qima-inc.com/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-urls@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/data-urls/download/data-urls-1.1.0.tgz#15ee0582baa5e22bb59c77140da8f9c76963bbfe"
  integrity sha1-Fe4Fgrql4iu1nHcUDaj5x2lju/4=
  dependencies:
    abab "^2.0.0"
    whatwg-mimetype "^2.2.0"
    whatwg-url "^7.0.0"

date-fns@^1.27.2:
  version "1.30.1"
  resolved "http://registry.npm.qima-inc.com/date-fns/download/date-fns-1.30.1.tgz#2e71bf0b119153dbb4cc4e88d9ea5acfb50dc05c"
  integrity sha1-LnG/CxGRU9u0zE6I2epaz7UNwFw=

date-now@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/date-now/download/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"
  integrity sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=

debug@*, debug@^4.0.1, debug@^4.1.0, debug@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-4.1.1.tgz#3b72260255109c6b589cee050f1d516139664791"
  integrity sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=
  dependencies:
    ms "^2.1.1"

debug@2.6.9, debug@^2.2.0, debug@^2.3.3, debug@^2.6.0:
  version "2.6.9"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.0.0, debug@^3.1.0:
  version "3.2.6"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.2.6.tgz#e83d17de16d8a7efb7717edbe5fb10135eee629b"
  integrity sha1-6D0X3hbYp++3cX7b5fsQE17uYps=
  dependencies:
    ms "^2.1.1"

debug@~3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

decamelize@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/decode-uri-component/download/decode-uri-component-0.2.0.tgz#eb3913333458775cb84cd1a1fae062106bb87545"
  integrity sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=

dedent@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.qima-inc.com/dedent/download/dedent-0.7.0.tgz#2495ddbaf6eb874abb0e1be9df22d2e5a544326c"
  integrity sha1-JJXduvbrh0q7Dhvp3yLS5aVEMmw=

deep-equal@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/deep-equal/download/deep-equal-1.0.1.tgz#f5d260292b660e084eff4cdbc9f08ad3247448b5"
  integrity sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=

deep-is@~0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.qima-inc.com/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"
  integrity sha1-s2nW+128E+7PUk+RsHD+7cNXzzQ=

default-user-agent@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/default-user-agent/download/default-user-agent-1.0.0.tgz#16c46efdcaba3edc45f24f2bd4868b01b7c2adc6"
  integrity sha1-FsRu/cq6PtxF8k8r1IaLAbfCrcY=
  dependencies:
    os-name "~1.0.3"

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  integrity sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2, define-properties@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/define-properties/download/define-properties-1.1.3.tgz#cf88da6cbee26fe6db7094f61d870cbd84cee9f1"
  integrity sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=
  dependencies:
    object-keys "^1.0.12"

define-property@^0.2.5:
  version "0.2.5"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-0.2.5.tgz#c35b1ef918ec3c990f9a5bc57be04aacec5c8116"
  integrity sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=
  dependencies:
    is-descriptor "^0.1.0"

define-property@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-1.0.0.tgz#769ebaaf3f4a63aad3af9e8d304c9bbe79bfb0e6"
  integrity sha1-dp66rz9KY6rTr56NMEybvnm/sOY=
  dependencies:
    is-descriptor "^1.0.0"

define-property@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/define-property/download/define-property-2.0.2.tgz#d459689e8d654ba77e02a817f8710d702cb16e9d"
  integrity sha1-1Flono1lS6d+AqgX+HENcCyxbp0=
  dependencies:
    is-descriptor "^1.0.2"
    isobject "^3.0.1"

del@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/del/download/del-3.0.0.tgz#53ecf699ffcbcb39637691ab13baf160819766e5"
  integrity sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=
  dependencies:
    globby "^6.1.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    p-map "^1.1.1"
    pify "^3.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"
  integrity sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o=

depd@^1.1.2, depd@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/depd/download/depd-1.1.2.tgz#9bcd52e14c097763e749b274c4346ed2e560b5a9"
  integrity sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=

des.js@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/des.js/download/des.js-1.0.0.tgz#c074d2e2aa6a8a9a07dbd61f9a15c2cd83ec8ecc"
  integrity sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=
  dependencies:
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"

destroy@^1.0.4, destroy@~1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"
  integrity sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/detect-libc/download/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/detect-newline/download/detect-newline-3.1.0.tgz#576f5dfc63ae1a192ff192d8ad3af6308991b651"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

diff-sequences@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/diff-sequences/download/diff-sequences-25.1.0.tgz#fd29a46f1c913fd66c22645dc75bffbe43051f32"
  integrity sha1-/SmkbxyRP9ZsImRdx1v/vkMFHzI=

diffie-hellman@^5.0.0:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/diffie-hellman/download/diffie-hellman-5.0.3.tgz#40e8ee98f55a2149607146921c63e1ae5f3d2875"
  integrity sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=
  dependencies:
    bn.js "^4.1.0"
    miller-rabin "^4.0.0"
    randombytes "^2.0.0"

digest-header@^0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/digest-header/download/digest-header-0.0.1.tgz#11ccf6deec5766ac379744d901c12cba49514be6"
  integrity sha1-Ecz23uxXZqw3l0TZAcEsuklRS+Y=
  dependencies:
    utility "0.1.11"

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-serializer@0:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/dom-serializer/download/dom-serializer-0.2.1.tgz#13650c850daffea35d8b626a4cfc4d3a17643fdb"
  integrity sha1-E2UMhQ2v/qNdi2JqTPxNOhdkP9s=
  dependencies:
    domelementtype "^2.0.1"
    entities "^2.0.0"

domain-browser@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/domain-browser/download/domain-browser-1.2.0.tgz#3d31f50191a6749dd1375a7f522e823d42e54eda"
  integrity sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=

domelementtype@1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/domelementtype/download/domelementtype-1.3.1.tgz#d048c44b37b0d10a7f2a3d5fee3f4333d790481f"
  integrity sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=

domelementtype@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/domelementtype/download/domelementtype-2.0.1.tgz#1f8bdfe91f5a78063274e803b4bdcedf6e94f94d"
  integrity sha1-H4vf6R9aeAYydOgDtL3O326U+U0=

domexception@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/domexception/download/domexception-1.0.1.tgz#937442644ca6a31261ef36e3ec677fe805582c90"
  integrity sha1-k3RCZEymoxJh7zbj7Gd/6AVYLJA=
  dependencies:
    webidl-conversions "^4.0.2"

domutils@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/domutils/download/domutils-1.7.0.tgz#56ea341e834e06e6748af7a1cb25da67ea9f8c2a"
  integrity sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=
  dependencies:
    dom-serializer "0"
    domelementtype "1"

dot-prop@^4.1.1:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/dot-prop/download/dot-prop-4.2.0.tgz#1f19e0c2e1aa0e32797c49799f2837ac6af69c57"
  integrity sha1-HxngwuGqDjJ5fEl5nyg3rGr2nFc=
  dependencies:
    is-obj "^1.0.0"

duplexer@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/duplexer/download/duplexer-0.1.1.tgz#ace6ff808c1ce66b57d1ebf97977acb02334cfc1"
  integrity sha1-rOb/gIwc5mtX0ev5eXessCM0z8E=

duplexify@^3.4.2, duplexify@^3.6.0:
  version "3.7.1"
  resolved "http://registry.npm.qima-inc.com/duplexify/download/duplexify-3.7.1.tgz#2a4df5317f6ccfd91f86d6fd25d8d8a103b88309"
  integrity sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=
  dependencies:
    end-of-stream "^1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@1.1.1, ee-first@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@^2.6.1:
  version "2.7.1"
  resolved "http://registry.npm.qima-inc.com/ejs/download/ejs-2.7.1.tgz#5b5ab57f718b79d4aca9254457afecd36fa80228"
  integrity sha1-W1q1f3GLedSsqSVEV6/s02+oAig=

electron-to-chromium@^1.3.247:
  version "1.3.253"
  resolved "http://registry.npm.qima-inc.com/electron-to-chromium/download/electron-to-chromium-1.3.253.tgz#bc3b2c94c2a109c08d37b04f526dc05fdabcbb5b"
  integrity sha1-vDsslMKhCcCNN7BPUm3AX9q8u1s=

elegant-spinner@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/elegant-spinner/download/elegant-spinner-1.0.1.tgz#db043521c95d7e303fd8f345bedc3349cfb0729e"
  integrity sha1-2wQ1IcldfjA/2PNFvtwzSc+wcp4=

elliptic@^6.0.0:
  version "6.5.1"
  resolved "http://registry.npm.qima-inc.com/elliptic/download/elliptic-6.5.1.tgz#c380f5f909bf1b9b4428d028cd18d3b0efd6b52b"
  integrity sha1-w4D1+Qm/G5tEKNAozRjTsO/WtSs=
  dependencies:
    bn.js "^4.4.0"
    brorand "^1.0.1"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.0"

emoji-regex@^7.0.1:
  version "7.0.3"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-7.0.3.tgz#933a04052860c85e83c122479c4748a8e4c72156"
  integrity sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/emojis-list/download/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"
  integrity sha1-TapNnbAPmBmIDHn6RXrlsJof04k=

encodeurl@^1.0.1, encodeurl@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/encodeurl/download/encodeurl-1.0.2.tgz#ad3ff4c86ec2d029322f5a02c3a9a606c95b3f59"
  integrity sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=

end-of-stream@^1.0.0, end-of-stream@^1.1.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/end-of-stream/download/end-of-stream-1.4.1.tgz#ed29634d19baba463b6ce6b80a37213eab71ec43"
  integrity sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=
  dependencies:
    once "^1.4.0"

enhanced-resolve@^4.0.0, enhanced-resolve@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/enhanced-resolve/download/enhanced-resolve-4.1.0.tgz#41c7e0bfdfe74ac1ffe1e57ad6a5c6c9f3742a7f"
  integrity sha1-Qcfgv9/nSsH/4eV61qXGyfN0Kn8=
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.4.0"
    tapable "^1.0.0"

entities@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/entities/download/entities-2.0.0.tgz#68d6084cab1b079767540d80e56a39b423e4abf4"
  integrity sha1-aNYITKsbB5dnVA2A5Wo5tCPkq/Q=

errno@^0.1.3, errno@~0.1.7:
  version "0.1.7"
  resolved "http://registry.npm.qima-inc.com/errno/download/errno-0.1.7.tgz#4684d71779ad39af177e3f007996f7c67c852618"
  integrity sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

error-inject@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/error-inject/download/error-inject-1.0.0.tgz#e2b3d91b54aed672f309d950d154850fa11d4f37"
  integrity sha1-4rPZG1Su1nLzCdlQ0VSFD6EdTzc=

es-abstract@^1.11.0, es-abstract@^1.12.0, es-abstract@^1.13.0, es-abstract@^1.5.1, es-abstract@^1.7.0:
  version "1.14.2"
  resolved "http://registry.npm.qima-inc.com/es-abstract/download/es-abstract-1.14.2.tgz#7ce108fad83068c8783c3cdf62e504e084d8c497"
  integrity sha1-fOEI+tgwaMh4PDzfYuUE4ITYxJc=
  dependencies:
    es-to-primitive "^1.2.0"
    function-bind "^1.1.1"
    has "^1.0.3"
    has-symbols "^1.0.0"
    is-callable "^1.1.4"
    is-regex "^1.0.4"
    object-inspect "^1.6.0"
    object-keys "^1.1.1"
    string.prototype.trimleft "^2.0.0"
    string.prototype.trimright "^2.0.0"

es-to-primitive@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/es-to-primitive/download/es-to-primitive-1.2.0.tgz#edf72478033456e8dda8ef09e00ad9650707f377"
  integrity sha1-7fckeAM0VujdqO8J4ArZZQcH83c=
  dependencies:
    is-callable "^1.1.4"
    is-date-object "^1.0.1"
    is-symbol "^1.0.2"

escape-html@^1.0.3, escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"
  integrity sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.4, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escodegen@^1.11.1:
  version "1.14.1"
  resolved "http://registry.npm.qima-inc.com/escodegen/download/escodegen-1.14.1.tgz#ba01d0c8278b5e95a9a45350142026659027a457"
  integrity sha1-ugHQyCeLXpWppFNQFCAmZZAnpFc=
  dependencies:
    esprima "^4.0.1"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    optionator "^0.8.1"
  optionalDependencies:
    source-map "~0.6.1"

eslint-config-google@^0.14.0:
  version "0.14.0"
  resolved "http://registry.npm.qima-inc.com/eslint-config-google/download/eslint-config-google-0.14.0.tgz#4f5f8759ba6e11b424294a219dbfa18c508bcc1a"
  integrity sha1-T1+HWbpuEbQkKUohnb+hjFCLzBo=

eslint-config-prettier@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/eslint-config-prettier/download/eslint-config-prettier-6.2.0.tgz#80e0b8714e3f6868c4ac2a25fbf39c02e73527a7"
  integrity sha1-gOC4cU4/aGjErCol+/OcAuc1J6c=
  dependencies:
    get-stdin "^6.0.0"

eslint-plugin-prettier@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.1.0.tgz#8695188f95daa93b0dc54b249347ca3b79c4686d"
  integrity sha1-hpUYj5XaqTsNxUskk0fKO3nEaG0=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react@^7.14.3:
  version "7.14.3"
  resolved "http://registry.npm.qima-inc.com/eslint-plugin-react/download/eslint-plugin-react-7.14.3.tgz#911030dd7e98ba49e1b2208599571846a66bdf13"
  integrity sha1-kRAw3X6YuknhsiCFmVcYRqZr3xM=
  dependencies:
    array-includes "^3.0.3"
    doctrine "^2.1.0"
    has "^1.0.3"
    jsx-ast-utils "^2.1.0"
    object.entries "^1.1.0"
    object.fromentries "^2.0.0"
    object.values "^1.1.0"
    prop-types "^15.7.2"
    resolve "^1.10.1"

eslint-scope@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-4.0.3.tgz#ca03833310f6889a3264781aa82e63eb9cfe7848"
  integrity sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-scope@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/eslint-scope/download/eslint-scope-5.0.0.tgz#e87c8887c73e8d1ec84f1ca591645c358bfc8fb9"
  integrity sha1-6HyIh8c+jR7ITxylkWRcNYv8j7k=
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-utils@^1.4.2:
  version "1.4.2"
  resolved "http://registry.npm.qima-inc.com/eslint-utils/download/eslint-utils-1.4.2.tgz#166a5180ef6ab7eb462f162fd0e6f2463d7309ab"
  integrity sha1-FmpRgO9qt+tGLxYv0ObyRj1zCas=
  dependencies:
    eslint-visitor-keys "^1.0.0"

eslint-visitor-keys@^1.0.0, eslint-visitor-keys@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/eslint-visitor-keys/download/eslint-visitor-keys-1.1.0.tgz#e2a82cea84ff246ad6fb57f9bde5b46621459ec2"
  integrity sha1-4qgs6oT/JGrW+1f5veW0ZiFFnsI=

eslint@^6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/eslint/download/eslint-6.3.0.tgz#1f1a902f67bfd4c354e7288b81e40654d927eb6a"
  integrity sha1-HxqQL2e/1MNU5yiLgeQGVNkn62o=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    ajv "^6.10.0"
    chalk "^2.1.0"
    cross-spawn "^6.0.5"
    debug "^4.0.1"
    doctrine "^3.0.0"
    eslint-scope "^5.0.0"
    eslint-utils "^1.4.2"
    eslint-visitor-keys "^1.1.0"
    espree "^6.1.1"
    esquery "^1.0.1"
    esutils "^2.0.2"
    file-entry-cache "^5.0.1"
    functional-red-black-tree "^1.0.1"
    glob-parent "^5.0.0"
    globals "^11.7.0"
    ignore "^4.0.6"
    import-fresh "^3.0.0"
    imurmurhash "^0.1.4"
    inquirer "^6.4.1"
    is-glob "^4.0.0"
    js-yaml "^3.13.1"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.14"
    minimatch "^3.0.4"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    progress "^2.0.0"
    regexpp "^2.0.1"
    semver "^6.1.2"
    strip-ansi "^5.2.0"
    strip-json-comments "^3.0.1"
    table "^5.2.3"
    text-table "^0.2.0"
    v8-compile-cache "^2.0.3"

espree@^6.1.1:
  version "6.1.1"
  resolved "http://registry.npm.qima-inc.com/espree/download/espree-6.1.1.tgz#7f80e5f7257fc47db450022d723e356daeb1e5de"
  integrity sha1-f4Dl9yV/xH20UAItcj41ba6x5d4=
  dependencies:
    acorn "^7.0.0"
    acorn-jsx "^5.0.2"
    eslint-visitor-keys "^1.1.0"

esprima@^4.0.0, esprima@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/esquery/download/esquery-1.0.1.tgz#406c51658b1f5991a5f9b62b1dc25b00e3e5c708"
  integrity sha1-QGxRZYsfWZGl+bYrHcJbAOPlxwg=
  dependencies:
    estraverse "^4.0.0"

esrecurse@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.qima-inc.com/esrecurse/download/esrecurse-4.2.1.tgz#007a3b9fdbc2b3bb87e4879ea19c92fdbd3942cf"
  integrity sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=
  dependencies:
    estraverse "^4.1.0"

estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.3.0"
  resolved "http://registry.npm.qima-inc.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

esutils@^2.0.0, esutils@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

etag@~1.8.1:
  version "1.8.1"
  resolved "http://registry.npm.qima-inc.com/etag/download/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"
  integrity sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=

eventemitter3@^3.0.0:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/eventemitter3/download/eventemitter3-3.1.2.tgz#2d3d48f9c346698fce83a85d7d664e98535df6e7"
  integrity sha1-LT1I+cNGaY/Og6hdfWZOmFNd9uc=

events@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/events/download/events-3.0.0.tgz#9a0a0dfaf62893d92b875b8f2698ca4114973e88"
  integrity sha1-mgoN+vYok9krh1uPJpjKQRSXPog=

evp_bytestokey@^1.0.0, evp_bytestokey@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz#7fcbdb198dc71959432efe13842684e0525acb02"
  integrity sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=
  dependencies:
    md5.js "^1.3.4"
    safe-buffer "^5.1.1"

exec-sh@^0.3.2:
  version "0.3.4"
  resolved "http://registry.npm.qima-inc.com/exec-sh/download/exec-sh-0.3.4.tgz#3a018ceb526cc6f6df2bb504b2bfe8e3a4934ec5"
  integrity sha1-OgGM61JsxvbfK7UEsr/o46STTsU=

execa@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-1.0.0.tgz#c6236a5bb4df6d6f15e88e7f017798216749ddd8"
  integrity sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=
  dependencies:
    cross-spawn "^6.0.0"
    get-stream "^4.0.0"
    is-stream "^1.1.0"
    npm-run-path "^2.0.0"
    p-finally "^1.0.0"
    signal-exit "^3.0.0"
    strip-eof "^1.0.0"

execa@^3.2.0:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/execa/download/execa-3.4.0.tgz#c08ed4550ef65d858fac269ffc8572446f37eb89"
  integrity sha1-wI7UVQ72XYWPrCaf/IVyRG8364k=
  dependencies:
    cross-spawn "^7.0.0"
    get-stream "^5.0.0"
    human-signals "^1.1.1"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.0"
    onetime "^5.1.0"
    p-finally "^2.0.0"
    signal-exit "^3.0.2"
    strip-final-newline "^2.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expand-brackets@^2.1.4:
  version "2.1.4"
  resolved "http://registry.npm.qima-inc.com/expand-brackets/download/expand-brackets-2.1.4.tgz#b77735e315ce30f6b6eff0f83b04151a22449622"
  integrity sha1-t3c14xXOMPa27/D4OwQVGiJEliI=
  dependencies:
    debug "^2.3.3"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    posix-character-classes "^0.1.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

expect@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/expect/download/expect-25.1.0.tgz#7e8d7b06a53f7d66ec927278db3304254ee683ee"
  integrity sha1-fo17BqU/fWbsknJ42zMEJU7mg+4=
  dependencies:
    "@jest/types" "^25.1.0"
    ansi-styles "^4.0.0"
    jest-get-type "^25.1.0"
    jest-matcher-utils "^25.1.0"
    jest-message-util "^25.1.0"
    jest-regex-util "^25.1.0"

express@^4.16.3:
  version "4.17.1"
  resolved "http://registry.npm.qima-inc.com/express/download/express-4.17.1.tgz#4491fc38605cf51f8629d39c2b5d026f98a4c134"
  integrity sha1-RJH8OGBc9R+GKdOcK10Cb5ikwTQ=
  dependencies:
    accepts "~1.3.7"
    array-flatten "1.1.1"
    body-parser "1.19.0"
    content-disposition "0.5.3"
    content-type "~1.0.4"
    cookie "0.4.0"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.2"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "~1.1.2"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.5"
    qs "6.7.0"
    range-parser "~1.2.1"
    safe-buffer "5.1.2"
    send "0.17.1"
    serve-static "1.14.1"
    setprototypeof "1.1.1"
    statuses "~1.5.0"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  integrity sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=
  dependencies:
    is-extendable "^0.1.0"

extend-shallow@^3.0.0, extend-shallow@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend-shallow/download/extend-shallow-3.0.2.tgz#26a71aaf073b39fb2127172746131c2704028db8"
  integrity sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=
  dependencies:
    assign-symbols "^1.0.0"
    is-extendable "^1.0.1"

extend@~3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extglob@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/extglob/download/extglob-2.0.4.tgz#ad00fe4dc612a9232e8718711dc5cb5ab0285543"
  integrity sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=
  dependencies:
    array-unique "^0.3.2"
    define-property "^1.0.0"
    expand-brackets "^2.1.4"
    extend-shallow "^2.0.1"
    fragment-cache "^0.2.1"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/extsprintf/download/extsprintf-1.4.0.tgz#e2689f8f356fad62cca65a3a91c5df5f9551692f"
  integrity sha1-4mifjzVvrWLMplo6kcXfX5VRaS8=

fast-deep-equal@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz#7b05218ddf9667bf7f370bf7fdb2cb15fdd0aa49"
  integrity sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=

fast-diff@^1.1.2:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/fast-diff/download/fast-diff-1.2.0.tgz#73ee11982d86caaf7959828d519cfe927fac5f03"
  integrity sha1-c+4RmC2Gyq95WYKNUZz+kn+sXwM=

fast-glob@^3.0.3:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/fast-glob/download/fast-glob-3.0.4.tgz#d484a41005cb6faeb399b951fd1bd70ddaebb602"
  integrity sha1-1ISkEAXLb66zmblR/RvXDdrrtgI=
  dependencies:
    "@nodelib/fs.stat" "^2.0.1"
    "@nodelib/fs.walk" "^1.2.1"
    glob-parent "^5.0.0"
    is-glob "^4.0.1"
    merge2 "^1.2.3"
    micromatch "^4.0.2"

fast-json-stable-stringify@2.x:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"
  integrity sha1-1RQsDK7msRifh9OnYREGT4bIu/I=

fast-levenshtein@~2.0.4, fast-levenshtein@~2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fastq@^1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/fastq/download/fastq-1.6.0.tgz#4ec8a38f4ac25f21492673adb7eae9cfef47d1c2"
  integrity sha1-Tsijj0rCXyFJJnOtt+rpz+9H0cI=
  dependencies:
    reusify "^1.0.0"

fb-watchman@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/fb-watchman/download/fb-watchman-2.0.0.tgz#54e9abf7dfa2f26cd9b1636c588c1afc05de5d58"
  integrity sha1-VOmr99+i8mzZsWNsWIwa/AXeXVg=
  dependencies:
    bser "^2.0.0"

fibers@^4.0.1:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/fibers/download/fibers-4.0.2.tgz#d04f9ccd0aba179588202202faeb4fed65d497f5"
  integrity sha1-0E+czQq6F5WIICIC+utP7WXUl/U=
  dependencies:
    detect-libc "^1.0.3"

figgy-pudding@^3.5.1:
  version "3.5.1"
  resolved "http://registry.npm.qima-inc.com/figgy-pudding/download/figgy-pudding-3.5.1.tgz#862470112901c727a0e495a80744bd5baa1d6790"
  integrity sha1-hiRwESkBxyeg5JWoB0S9W6odZ5A=

figures@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  integrity sha1-y+Hjr/zxzUS4DK3+0o3Hk6lwHS4=
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

figures@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/file-entry-cache/download/file-entry-cache-5.0.1.tgz#ca0f6efa6dd3d561333fb14515065c2fafdf439c"
  integrity sha1-yg9u+m3T1WEzP7FFFQZcL6/fQ5w=
  dependencies:
    flat-cache "^2.0.1"

file-type@^12.0.1:
  version "12.3.0"
  resolved "http://registry.npm.qima-inc.com/file-type/download/file-type-12.3.0.tgz#74d755e5dc9c5cbc7ee6f182529b453906ac88c2"
  integrity sha1-dNdV5dycXLx+5vGCUptFOQasiMI=

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filesize@^3.6.1:
  version "3.6.1"
  resolved "http://registry.npm.qima-inc.com/filesize/download/filesize-3.6.1.tgz#090bb3ee01b6f801a8a8be99d31710b3422bb317"
  integrity sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=

fill-range@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-4.0.0.tgz#d544811d428f98eb06a63dc402d2403c328c38f7"
  integrity sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=
  dependencies:
    extend-shallow "^2.0.1"
    is-number "^3.0.0"
    repeat-string "^1.6.1"
    to-regex-range "^2.1.0"

fill-range@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/fill-range/download/fill-range-7.0.1.tgz#1919a6a7c75fe38b2c7c77e5198535da9acdda40"
  integrity sha1-GRmmp8df44ssfHflGYU12prN2kA=
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/finalhandler/download/finalhandler-1.1.2.tgz#b7e7d000ffd11938d0fdb053506f6ebabe9f587d"
  integrity sha1-t+fQAP/RGTjQ/bBTUG9uur6fWH0=
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-cache-dir@^2.0.0, find-cache-dir@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/find-cache-dir/download/find-cache-dir-2.1.0.tgz#8d0f94cd13fe43c6c7c261a0d86115ca918c05f7"
  integrity sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=
  dependencies:
    commondir "^1.0.1"
    make-dir "^2.0.0"
    pkg-dir "^3.0.0"

find-cache-dir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/find-cache-dir/download/find-cache-dir-3.0.0.tgz#cd4b7dd97b7185b7e17dbfe2d6e4115ee3eeb8fc"
  integrity sha1-zUt92Xtxhbfhfb/i1uQRXuPuuPw=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.0"
    pkg-dir "^4.1.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  integrity sha1-RdG35QbHF93UgndaK3eSCjwMV6c=
  dependencies:
    locate-path "^2.0.0"

find-up@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-3.0.0.tgz#49169f1d7993430646da61ecc5ae355c21c97b73"
  integrity sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=
  dependencies:
    locate-path "^3.0.0"

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/find-up/download/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

flat-cache@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/flat-cache/download/flat-cache-2.0.1.tgz#5d296d6f04bda44a4630a301413bdbc2ec085ec0"
  integrity sha1-XSltbwS9pEpGMKMBQTvbwuwIXsA=
  dependencies:
    flatted "^2.0.0"
    rimraf "2.6.3"
    write "1.0.3"

flatted@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/flatted/download/flatted-2.0.1.tgz#69e57caa8f0eacbc281d2e2cb458d46fdb449e08"
  integrity sha1-aeV8qo8OrLwoHS4stFjUb9tEngg=

flush-write-stream@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/flush-write-stream/download/flush-write-stream-1.1.1.tgz#8dd7d873a1babc207d94ead0c2e0e44276ebf2e8"
  integrity sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.3.6"

fn-name@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/fn-name/download/fn-name-2.0.1.tgz#5214d7537a4d06a4a301c0cc262feb84188002e7"
  integrity sha1-UhTXU3pNBqSjAcDMJi/rhBiAAuc=

follow-redirects@^1.0.0, follow-redirects@^1.2.3:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/follow-redirects/download/follow-redirects-1.9.0.tgz#8d5bcdc65b7108fe1508649c79c12d732dcedb4f"
  integrity sha1-jVvNxltxCP4VCGScecEtcy3O208=
  dependencies:
    debug "^3.0.0"

for-in@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"
  integrity sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^2.1.4, form-data@^2.5.0:
  version "2.5.1"
  resolved "http://registry.npm.qima-inc.com/form-data/download/form-data-2.5.1.tgz#f2cbec57b5e59e23716e128fe44d4e5dd23895f4"
  integrity sha1-8svsV7XlniNxbhKP5E1OXdI4lfQ=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://registry.npm.qima-inc.com/form-data/download/form-data-2.3.3.tgz?cache=0&sync_timestamp=1573027040291&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fform-data%2Fdownload%2Fform-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

formstream@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/formstream/download/formstream-1.1.0.tgz#51f3970f26136eb0ad44304de4cebb50207b4479"
  integrity sha1-UfOXDyYTbrCtRDBN5M67UCB7RHk=
  dependencies:
    destroy "^1.0.4"
    mime "^1.3.4"
    pause-stream "~0.0.11"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/forwarded/download/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"
  integrity sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=

fragment-cache@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/fragment-cache/download/fragment-cache-0.2.1.tgz#4290fad27f13e89be7f33799c6bc5a0abfff0d19"
  integrity sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=
  dependencies:
    map-cache "^0.2.2"

fresh@0.5.2, fresh@~0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/fresh/download/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"
  integrity sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=

from2@^2.1.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/from2/download/from2-2.3.0.tgz#8bfb5502bde4a4d36cfdeea007fcca21d7e382af"
  integrity sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=
  dependencies:
    inherits "^2.0.1"
    readable-stream "^2.0.0"

fs-write-stream-atomic@^1.0.8:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  integrity sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^1.2.7:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-1.2.13.tgz#f325cb0455592428bcf11b383370ef70e3bfcc38"
  integrity sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=
  dependencies:
    bindings "^1.5.0"
    nan "^2.12.1"

fsevents@^2.0.6:
  version "2.0.7"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.0.7.tgz#382c9b443c6cbac4c57187cdda23aa3bf1ccfc2a"
  integrity sha1-OCybRDxsusTFcYfN2iOqO/HM/Co=

fsevents@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.1.2.tgz#4c0a1fb34bc68e543b4b82a9ec392bfbda840805"
  integrity sha1-TAofs0vGjlQ7S4Kp7Dkr+9qECAU=

fsevents@~2.1.2:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/fsevents/download/fsevents-2.1.3.tgz#fb738703ae8d2f9fe900c33836ddebee8b97f23e"
  integrity sha1-+3OHA66NL5/pAMM4Nt3r7ouX8j4=

function-bind@^1.0.2, function-bind@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/function-bind/download/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"
  integrity sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/functional-red-black-tree/download/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"
  integrity sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc=

g-status@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/g-status/download/g-status-2.0.2.tgz#270fd32119e8fc9496f066fe5fe88e0a6bc78b97"
  integrity sha1-Jw/TIRno/JSW8Gb+X+iOCmvHi5c=
  dependencies:
    arrify "^1.0.1"
    matcher "^1.0.0"
    simple-git "^1.85.0"

gensync@^1.0.0-beta.1:
  version "1.0.0-beta.1"
  resolved "http://registry.npm.qima-inc.com/gensync/download/gensync-1.0.0-beta.1.tgz#58f4361ff987e5ff6e1e7a210827aa371eaac269"
  integrity sha1-WPQ2H/mH5f9uHnohCCeqNx6qwmk=

get-caller-file@^2.0.1:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-own-enumerable-property-symbols@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/get-own-enumerable-property-symbols/download/get-own-enumerable-property-symbols-3.0.0.tgz#b877b49a5c16aefac3655f2ed2ea5b684df8d203"
  integrity sha1-uHe0mlwWrvrDZV8u0upbaE340gM=

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^4.0.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-4.1.0.tgz#c1b255575f3dc21d59bfc79cd3d2b46b1c3a54b5"
  integrity sha1-wbJVV189wh1Zv8ec09K0axw6VLU=
  dependencies:
    pump "^3.0.0"

get-stream@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/get-stream/download/get-stream-5.1.0.tgz#01203cdc92597f9b909067c3e656cc1f4d3c4dc9"
  integrity sha1-ASA83JJZf5uQkGfD5lbMH008Tck=
  dependencies:
    pump "^3.0.0"

get-value@^2.0.3, get-value@^2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.qima-inc.com/get-value/download/get-value-2.0.6.tgz#dc15ca1c672387ca76bd37ac0a395ba2042a2c28"
  integrity sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://registry.npm.qima-inc.com/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

glob-parent@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  integrity sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-parent@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-5.0.0.tgz#1dc99f0f39b006d3e92c2c284068382f0c20e954"
  integrity sha1-HcmfDzmwBtPpLCwoQGg4Lwwg6VQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@~5.1.0:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/glob-parent/download/glob-parent-5.1.1.tgz#b6c1ef417c4e5663ea498f1c45afac6916bbc229"
  integrity sha1-tsHvQXxOVmPqSY8cRa+saRa7wik=
  dependencies:
    is-glob "^4.0.1"

glob@7.1.6, glob@^7.1.2:
  version "7.1.6"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.1.6.tgz#141f33b81a7c2492e125594307480c46679278a6"
  integrity sha1-FB8zuBp8JJLhJVlDB0gMRmeSeKY=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.3, glob@^7.1.1, glob@^7.1.3, glob@^7.1.4:
  version "7.1.4"
  resolved "http://registry.npm.qima-inc.com/glob/download/glob-7.1.4.tgz#aa608a2f6c577ad357e1ae5a5c26d9a8d1969255"
  integrity sha1-qmCKL2xXetNX4a5aXCbZqNGWklU=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0, globals@^11.7.0:
  version "11.12.0"
  resolved "http://registry.npm.qima-inc.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globby@^10.0.1:
  version "10.0.1"
  resolved "http://registry.npm.qima-inc.com/globby/download/globby-10.0.1.tgz#4782c34cb75dd683351335c5829cc3420e606b22"
  integrity sha1-R4LDTLdd1oM1EzXFgpzDQg5gayI=
  dependencies:
    "@types/glob" "^7.1.1"
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.0.3"
    glob "^7.1.3"
    ignore "^5.1.1"
    merge2 "^1.2.3"
    slash "^3.0.0"

globby@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/globby/download/globby-6.1.0.tgz#f5a6d70e8395e21c858fb0489d64df02424d506c"
  integrity sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=
  dependencies:
    array-union "^1.0.1"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

graceful-fs@^4.1.11, graceful-fs@^4.1.15, graceful-fs@^4.1.2:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/graceful-fs/download/graceful-fs-4.2.2.tgz#6f0952605d0140c1cfdb138ed005775b92d67b02"
  integrity sha1-bwlSYF0BQMHP2xOO0AV3W5LWewI=

graceful-fs@^4.2.3:
  version "4.2.3"
  resolved "http://registry.npm.qima-inc.com/graceful-fs/download/graceful-fs-4.2.3.tgz#4a12ff1b60376ef09862c2093edd908328be8423"
  integrity sha1-ShL/G2A3bvCYYsIJPt2Qgyi+hCM=

growly@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/growly/download/growly-1.3.0.tgz#f10748cbe76af964b7c96c93c6bcc28af120c081"
  integrity sha1-8QdIy+dq+WS3yWyTxrzCivEgwIE=

gzip-size@^5.0.0:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/gzip-size/download/gzip-size-5.1.1.tgz#cb9bee692f87c0612b232840a873904e4c135274"
  integrity sha1-y5vuaS+HwGErIyhAqHOQTkwTUnQ=
  dependencies:
    duplexer "^0.1.1"
    pify "^4.0.1"

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.3"
  resolved "http://registry.npm.qima-inc.com/har-validator/download/har-validator-5.1.3.tgz#1ef89ebd3e4996557675eed9893110dc350fa080"
  integrity sha1-HvievT5JllV2de7ZiTEQ3DUPoIA=
  dependencies:
    ajv "^6.5.5"
    har-schema "^2.0.0"

harmony-reflect@^1.4.6:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/harmony-reflect/download/harmony-reflect-1.6.1.tgz#c108d4f2bb451efef7a37861fdbdae72c9bdefa9"
  integrity sha1-wQjU8rtFHv73o3hh/b2ucsm976k=

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-symbols@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-symbols/download/has-symbols-1.0.0.tgz#ba1a8f1af2a0fc39650f5c850367704122063b44"
  integrity sha1-uhqPGvKg/DllD1yFA2dwQSIGO0Q=

has-value@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-0.3.1.tgz#7b1f58bada62ca827ec0a2078025654845995e1f"
  integrity sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=
  dependencies:
    get-value "^2.0.3"
    has-values "^0.1.4"
    isobject "^2.0.0"

has-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-value/download/has-value-1.0.0.tgz#18b281da585b1c5c51def24c930ed29a0be6b177"
  integrity sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=
  dependencies:
    get-value "^2.0.6"
    has-values "^1.0.0"
    isobject "^3.0.0"

has-values@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-0.1.4.tgz#6d61de95d91dfca9b9a02089ad384bff8f62b771"
  integrity sha1-bWHeldkd/Km5oCCJrThL/49it3E=

has-values@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/has-values/download/has-values-1.0.0.tgz#95b0b63fec2146619a6fe57fe75628d5a39efe4f"
  integrity sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

has@^1.0.0, has@^1.0.1, has@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/has/download/has-1.0.3.tgz#722d7cbfc1f6aa8241f16dd814e011e1f41e8796"
  integrity sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=
  dependencies:
    function-bind "^1.1.1"

hash-base@^3.0.0:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/hash-base/download/hash-base-3.0.4.tgz#5fc8686847ecd73499403319a6b0a3f3f6ae4918"
  integrity sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

hash.js@^1.0.0, hash.js@^1.0.3:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/hash.js/download/hash.js-1.1.7.tgz#0babca538e8d4ee4a0f8988d68866537a003cf42"
  integrity sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hex-color-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/hex-color-regex/download/hex-color-regex-1.1.0.tgz#4c06fccb4602fe2602b3c93df82d7e7dbf1a8a8e"
  integrity sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=

hmac-drbg@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/hmac-drbg/download/hmac-drbg-1.0.1.tgz#d2745701025a6c775a6c545793ed502fc0c649a1"
  integrity sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

hmacsha1@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/hmacsha1/download/hmacsha1-1.0.0.tgz#c1b7ae03a4ea116348090af14f8148c12938a917"
  integrity sha1-wbeuA6TqEWNICQrxT4FIwSk4qRc=

hoopy@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/hoopy/download/hoopy-0.1.4.tgz#609207d661100033a9a9402ad3dea677381c1b1d"
  integrity sha1-YJIH1mEQADOpqUAq096mdzgcGx0=

hosted-git-info@^2.1.4:
  version "2.8.4"
  resolved "http://registry.npm.qima-inc.com/hosted-git-info/download/hosted-git-info-2.8.4.tgz#44119abaf4bc64692a16ace34700fed9c03e2546"
  integrity sha1-RBGauvS8ZGkqFqzjRwD+2cA+JUY=

hostile@^1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/hostile/download/hostile-1.3.2.tgz#bb92e4688bc1733d5f8222ef2f77c4463d819294"
  integrity sha1-u5LkaIvBcz1fgiLvL3fERj2BkpQ=
  dependencies:
    chalk "^2.3.1"
    minimist "^1.1.0"
    once "^1.3.0"
    split "^1.0.0"
    through "^2.3.4"

hsl-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/hsl-regex/download/hsl-regex-1.0.0.tgz#d49330c789ed819e276a4c0d272dffa30b18fe6e"
  integrity sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=

hsla-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/hsla-regex/download/hsla-regex-1.0.0.tgz#c1ce7a3168c8c6614033a4b5f7877f3b225f9c38"
  integrity sha1-wc56MWjIxmFAM6S194d/OyJfnDg=

html-comment-regex@^1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/html-comment-regex/download/html-comment-regex-1.1.2.tgz#97d4688aeb5c81886a364faa0cad1dda14d433a7"
  integrity sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=

html-encoding-sniffer@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/html-encoding-sniffer/download/html-encoding-sniffer-1.0.2.tgz#e70d84b94da53aa375e11fe3a351be6642ca46f8"
  integrity sha1-5w2EuU2lOqN14R/jo1G+ZkLKRvg=
  dependencies:
    whatwg-encoding "^1.0.1"

html-escaper@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/html-escaper/download/html-escaper-2.0.0.tgz#71e87f931de3fe09e56661ab9a29aadec707b491"
  integrity sha1-ceh/kx3j/gnlZmGrmimq3scHtJE=

http-assert@^1.3.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/http-assert/download/http-assert-1.4.1.tgz#c5f725d677aa7e873ef736199b89686cceb37878"
  integrity sha1-xfcl1neqfoc+9zYZm4lobM6zeHg=
  dependencies:
    deep-equal "~1.0.1"
    http-errors "~1.7.2"

http-errors@1.7.2:
  version "1.7.2"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.7.2.tgz#4f5029cf13239f31036e5b2e55292bcfbcc85c8f"
  integrity sha1-T1ApzxMjnzEDblsuVSkrz7zIXI8=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@^1.6.3, http-errors@~1.7.2:
  version "1.7.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.7.3.tgz#6c619e4f9c60308c38519498c14fbb10aacebb06"
  integrity sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.4"
    setprototypeof "1.1.1"
    statuses ">= 1.5.0 < 2"
    toidentifier "1.0.0"

http-errors@~1.6.2:
  version "1.6.3"
  resolved "http://registry.npm.qima-inc.com/http-errors/download/http-errors-1.6.3.tgz#8b55680bb4be283a0b5bf4ea2e38580be1d9320d"
  integrity sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=
  dependencies:
    depd "~1.1.2"
    inherits "2.0.3"
    setprototypeof "1.1.0"
    statuses ">= 1.4.0 < 2"

http-proxy-middleware@^0.19.0:
  version "0.19.1"
  resolved "http://registry.npm.qima-inc.com/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz#183c7dc4aa1479150306498c210cdaf96080a43a"
  integrity sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=
  dependencies:
    http-proxy "^1.17.0"
    is-glob "^4.0.0"
    lodash "^4.17.11"
    micromatch "^3.1.10"

http-proxy@^1.17.0:
  version "1.17.0"
  resolved "http://registry.npm.qima-inc.com/http-proxy/download/http-proxy-1.17.0.tgz#7ad38494658f84605e2f6db4436df410f4e5be9a"
  integrity sha1-etOElGWPhGBeL220Q230EPTlvpo=
  dependencies:
    eventemitter3 "^3.0.0"
    follow-redirects "^1.0.0"
    requires-port "^1.0.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/http-signature/download/http-signature-1.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fhttp-signature%2Fdownload%2Fhttp-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/https-browserify/download/https-browserify-1.0.0.tgz#ec06c10e0a34c0f2faf199f7fd7fc78fffd03c73"
  integrity sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=

human-signals@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/human-signals/download/human-signals-1.1.1.tgz#c5b1cd14f50aeae09ab6c59fe63ba3395fe4dfa3"
  integrity sha1-xbHNFPUK6uCatsWf5jujOV/k36M=

humanize-ms@^1.2.0, humanize-ms@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/humanize-ms/download/humanize-ms-1.2.1.tgz#c46e3159a293f6b896da29316d8b6fe8bb79bbed"
  integrity sha1-xG4xWaKT9riW2ikxbYtv6Lt5u+0=
  dependencies:
    ms "^2.0.0"

husky@^1.3.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/husky/download/husky-1.3.1.tgz#26823e399300388ca2afff11cfa8a86b0033fae0"
  integrity sha1-JoI+OZMAOIyir/8Rz6ioawAz+uA=
  dependencies:
    cosmiconfig "^5.0.7"
    execa "^1.0.0"
    find-up "^3.0.0"
    get-stdin "^6.0.0"
    is-ci "^2.0.0"
    pkg-dir "^3.0.0"
    please-upgrade-node "^3.1.1"
    read-pkg "^4.0.1"
    run-node "^1.0.0"
    slash "^2.0.0"

iconv-lite@0.4.24, iconv-lite@^0.4.15, iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "http://registry.npm.qima-inc.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

icss-utils@^4.0.0, icss-utils@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/icss-utils/download/icss-utils-4.1.1.tgz#21170b53789ee27447c2f47dd683081403f9a467"
  integrity sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=
  dependencies:
    postcss "^7.0.14"

identity-obj-proxy@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/identity-obj-proxy/download/identity-obj-proxy-3.0.0.tgz#94d2bda96084453ef36fbc5aaec37e0f79f1fc14"
  integrity sha1-lNK9qWCERT7zb7xarsN+D3nx/BQ=
  dependencies:
    harmony-reflect "^1.4.6"

ieee754@^1.1.4:
  version "1.1.13"
  resolved "http://registry.npm.qima-inc.com/ieee754/download/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

iferr@^0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.qima-inc.com/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"
  integrity sha1-xg7taebY/bazEEofy8ocGS3FtQE=

ignore@^4.0.6:
  version "4.0.6"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-4.0.6.tgz#750e3db5862087b4737ebac8207ffd1ef27b25fc"
  integrity sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=

ignore@^5.1.1:
  version "5.1.4"
  resolved "http://registry.npm.qima-inc.com/ignore/download/ignore-5.1.4.tgz#84b7b3dbe64552b6ef0eca99f6743dbec6d97adf"
  integrity sha1-hLez2+ZFUrbvDsqZ9nQ9vsbZet8=

import-fresh@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-2.0.0.tgz#d81355c15612d386c61f9ddd3922d4304822a546"
  integrity sha1-2BNVwVYS04bGH53dOSLUMEgipUY=
  dependencies:
    caller-path "^2.0.0"
    resolve-from "^3.0.0"

import-fresh@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/import-fresh/download/import-fresh-3.1.0.tgz#6d33fa1dcef6df930fae003446f33415af905118"
  integrity sha1-bTP6Hc7235MPrgA0RvM0Fa+QURg=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-local@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/import-local/download/import-local-3.0.2.tgz#a8cfd0431d1de4a2199703d003e3e62364fa6db6"
  integrity sha1-qM/QQx0d5KIZlwPQA+PmI2T6bbY=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/indent-string/download/indent-string-3.2.0.tgz#4a5fd6d27cc332f37e5419a504dbb837105c9289"
  integrity sha1-Sl/W0nzDMvN+VBmlBNu4NxBckok=

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/indexes-of/download/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"
  integrity sha1-8w9xbI4r00bHtn0985FVZqfAVgc=

infer-owner@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/infer-owner/download/infer-owner-1.0.4.tgz#c4cefcaa8e51051c2a40ba2ce8a3d27295af9467"
  integrity sha1-xM78qo5RBRwqQLos6KPScpWvlGc=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.4, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

inherits@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"
  integrity sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=

inherits@2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"
  integrity sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=

inquirer@^6.4.1, inquirer@^6.5.0:
  version "6.5.2"
  resolved "http://registry.npm.qima-inc.com/inquirer/download/inquirer-6.5.2.tgz#ad50942375d036d327ff528c08bd5fab089928ca"
  integrity sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

invariant@^2.2.2:
  version "2.2.4"
  resolved "http://registry.npm.qima-inc.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

ip-regex@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/ip-regex/download/ip-regex-2.1.0.tgz#fa78bf5d2e6913c911ce9f819ee5146bb6d844e9"
  integrity sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=

ipaddr.js@1.9.0:
  version "1.9.0"
  resolved "http://registry.npm.qima-inc.com/ipaddr.js/download/ipaddr.js-1.9.0.tgz#37df74e430a0e47550fe54a2defe30d8acd95f65"
  integrity sha1-N9905DCg5HVQ/lSi3v4w2KzZX2U=

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-absolute-url/download/is-absolute-url-2.1.0.tgz#50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6"
  integrity sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=

is-accessor-descriptor@^0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz#a9e12cb3ae8d876727eeef3843f8a0897b5c98d6"
  integrity sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=
  dependencies:
    kind-of "^3.0.2"

is-accessor-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz#169c2f6d3df1f992618072365c9b0ea1f6878656"
  integrity sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=
  dependencies:
    kind-of "^6.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/is-arrayish/download/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  integrity sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=
  dependencies:
    binary-extensions "^1.0.0"

is-binary-path@^2.1.0, is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-buffer@^1.1.5, is-buffer@~1.1.1:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-callable@^1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/is-callable/download/is-callable-1.1.4.tgz#1e1adf219e1eeb684d691f9d6a05ff0d30a24d75"
  integrity sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU=

is-ci@^1.1.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-1.2.1.tgz#e3779c8ee17fccf428488f6e281187f2e632841c"
  integrity sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=
  dependencies:
    ci-info "^1.5.0"

is-ci@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-ci/download/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-color-stop@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-color-stop/download/is-color-stop-1.1.0.tgz#cfff471aee4dd5c9e158598fbe12967b5cdad345"
  integrity sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=
  dependencies:
    css-color-names "^0.0.4"
    hex-color-regex "^1.1.0"
    hsl-regex "^1.0.0"
    hsla-regex "^1.0.0"
    rgb-regex "^1.0.1"
    rgba-regex "^1.0.0"

is-data-descriptor@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz#0b5ee648388e2c860282e793f1856fec3f301b56"
  integrity sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=
  dependencies:
    kind-of "^3.0.2"

is-data-descriptor@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz#d84876321d0e7add03990406abbbbd36ba9268c7"
  integrity sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=
  dependencies:
    kind-of "^6.0.0"

is-date-object@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-date-object/download/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"
  integrity sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY=

is-descriptor@^0.1.0:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-0.1.6.tgz#366d8240dde487ca51823b1ab9f07a10a78251ca"
  integrity sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=
  dependencies:
    is-accessor-descriptor "^0.1.6"
    is-data-descriptor "^0.1.4"
    kind-of "^5.0.0"

is-descriptor@^1.0.0, is-descriptor@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-descriptor/download/is-descriptor-1.0.2.tgz#3b159746a66604b04f8c81524ba365c5f14d86ec"
  integrity sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=
  dependencies:
    is-accessor-descriptor "^1.0.0"
    is-data-descriptor "^1.0.0"
    kind-of "^6.0.2"

is-directory@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.qima-inc.com/is-directory/download/is-directory-0.3.1.tgz#61339b6f2475fc772fd9c9d83f5c8575dc154ae1"
  integrity sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"
  integrity sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=

is-extendable@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-extendable/download/is-extendable-1.0.1.tgz#a7470f9e426733d81bd81e1155264e3a3507cab4"
  integrity sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=
  dependencies:
    is-plain-object "^2.0.4"

is-extglob@^2.1.0, is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  integrity sha1-754xOG8DGn8NZDr4L95QxFfvAMs=
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-generator-fn/download/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-generator-function@^1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/is-generator-function/download/is-generator-function-1.0.7.tgz#d2132e529bb0000a7f80794d4bdf5cd5e5813522"
  integrity sha1-0hMuUpuwAAp/gHlNS99c1eWBNSI=

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  integrity sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=
  dependencies:
    is-extglob "^2.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/is-glob/download/is-glob-4.0.1.tgz#7567dbe9f2f5e2467bc77ab83c4a29482407a5dc"
  integrity sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=
  dependencies:
    is-extglob "^2.1.1"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  integrity sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=
  dependencies:
    kind-of "^3.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://registry.npm.qima-inc.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-obj@^1.0.0, is-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"
  integrity sha1-PkcprB9f3gJc19g6iW2rn09n2w8=

is-observable@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-observable/download/is-observable-1.1.0.tgz#b3e986c8f44de950867cab5403f5a3465005975e"
  integrity sha1-s+mGyPRN6VCGfKtUA/WjRlAFl14=
  dependencies:
    symbol-observable "^1.1.0"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-path-cwd/download/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"
  integrity sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=

is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/is-path-cwd/download/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-in-cwd@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz#5ac48b345ef675339bd6c7a48a912110b241cf52"
  integrity sha1-WsSLNF72dTOb1sekipEhELJBz1I=
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/is-path-inside/download/is-path-inside-1.0.1.tgz#8ef5b7de50437a3fdca6b4e865ef7aa55cb48036"
  integrity sha1-jvW33lBDej/cprToZe96pVy0gDY=
  dependencies:
    path-is-inside "^1.0.1"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"
  integrity sha1-caUMhCnfync8kqOQpKA7OfzVHT4=

is-plain-object@^2.0.3, is-plain-object@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/is-plain-object/download/is-plain-object-2.0.4.tgz#2c163b3fafb1b606d9d17928f05c2a1c38e07677"
  integrity sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=
  dependencies:
    isobject "^3.0.1"

is-promise@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/is-promise/download/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"
  integrity sha1-eaKp7OfwlugPNtKy87wWwf9L8/o=

is-regex@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/is-regex/download/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
  integrity sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=
  dependencies:
    has "^1.0.1"

is-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-regexp/download/is-regexp-1.0.0.tgz#fd2d883545c46bac5a633e7b9a09e87fa2cb5069"
  integrity sha1-/S2INUXEa6xaYz57mgnof6LLUGk=

is-resolvable@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-resolvable/download/is-resolvable-1.1.0.tgz#fb18f87ce1feb925169c9a407c19318a3206ed88"
  integrity sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=

is-stream@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"
  integrity sha1-EtSj3U5o4Lec6428hBc66A2RykQ=

is-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/is-stream/download/is-stream-2.0.0.tgz#bde9c32680d6fae04129d6ac9d921ce7815f78e3"
  integrity sha1-venDJoDW+uBBKdasnZIc54FfeOM=

is-svg@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/is-svg/download/is-svg-3.0.0.tgz#9321dbd29c212e5ca99c4fa9794c714bcafa2f75"
  integrity sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-symbol/download/is-symbol-1.0.2.tgz#a055f6ae57192caee329e7a860118b497a950f38"
  integrity sha1-oFX2rlcZLK7jKeeoYBGLSXqVDzg=
  dependencies:
    has-symbols "^1.0.0"

is-typedarray@^1.0.0, is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-windows@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/is-windows/download/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

is-wsl@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/is-wsl/download/is-wsl-2.1.1.tgz#4a1c152d429df3d441669498e2486d3596ebaf1d"
  integrity sha1-ShwVLUKd89RBZpSY4khtNZbrrx0=

isarray@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  integrity sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=
  dependencies:
    isarray "1.0.0"

isobject@^3.0.0, isobject@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/isobject/download/isobject-3.0.1.tgz#4e431e92b11a9731636aa1f9c8d1ccbcfdab78df"
  integrity sha1-TkMekrEalzFjaqH5yNHMvP2reN8=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

istanbul-lib-coverage@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-coverage/download/istanbul-lib-coverage-3.0.0.tgz#f5944a37c70b550b02a78a5c3b2055b280cec8ec"
  integrity sha1-9ZRKN8cLVQsCp4pcOyBVsoDOyOw=

istanbul-lib-instrument@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-instrument/download/istanbul-lib-instrument-4.0.1.tgz#61f13ac2c96cfefb076fe7131156cc05907874e6"
  integrity sha1-YfE6wsls/vsHb+cTEVbMBZB4dOY=
  dependencies:
    "@babel/core" "^7.7.5"
    "@babel/parser" "^7.7.5"
    "@babel/template" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.0.0"
    semver "^6.3.0"

istanbul-lib-report@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-report/download/istanbul-lib-report-3.0.0.tgz#7518fe52ea44de372f460a76b5ecda9ffb73d8a6"
  integrity sha1-dRj+UupE3jcvRgp2tezan/tz2KY=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^3.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-lib-source-maps/download/istanbul-lib-source-maps-4.0.0.tgz#75743ce6d96bb86dc7ee4352cf6366a23f0b1ad9"
  integrity sha1-dXQ85tlruG3H7kNSz2Nmoj8LGtk=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-reports@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/istanbul-reports/download/istanbul-reports-3.0.0.tgz#d4d16d035db99581b6194e119bbf36c963c5eb70"
  integrity sha1-1NFtA125lYG2GU4Rm782yWPF63A=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jest-changed-files@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-changed-files/download/jest-changed-files-25.1.0.tgz#73dae9a7d9949fdfa5c278438ce8f2ff3ec78131"
  integrity sha1-c9rpp9mUn9+lwnhDjOjy/z7HgTE=
  dependencies:
    "@jest/types" "^25.1.0"
    execa "^3.2.0"
    throat "^5.0.0"

jest-cli@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-cli/download/jest-cli-25.1.0.tgz#75f0b09cf6c4f39360906bf78d580be1048e4372"
  integrity sha1-dfCwnPbE85NgkGv3jVgL4QSOQ3I=
  dependencies:
    "@jest/core" "^25.1.0"
    "@jest/test-result" "^25.1.0"
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    exit "^0.1.2"
    import-local "^3.0.2"
    is-ci "^2.0.0"
    jest-config "^25.1.0"
    jest-util "^25.1.0"
    jest-validate "^25.1.0"
    prompts "^2.0.1"
    realpath-native "^1.1.0"
    yargs "^15.0.0"

jest-config@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-config/download/jest-config-25.1.0.tgz#d114e4778c045d3ef239452213b7ad3ec1cbea90"
  integrity sha1-0RTkd4wEXT7yOUUiE7etPsHL6pA=
  dependencies:
    "@babel/core" "^7.1.0"
    "@jest/test-sequencer" "^25.1.0"
    "@jest/types" "^25.1.0"
    babel-jest "^25.1.0"
    chalk "^3.0.0"
    glob "^7.1.1"
    jest-environment-jsdom "^25.1.0"
    jest-environment-node "^25.1.0"
    jest-get-type "^25.1.0"
    jest-jasmine2 "^25.1.0"
    jest-regex-util "^25.1.0"
    jest-resolve "^25.1.0"
    jest-util "^25.1.0"
    jest-validate "^25.1.0"
    micromatch "^4.0.2"
    pretty-format "^25.1.0"
    realpath-native "^1.1.0"

jest-diff@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-diff/download/jest-diff-25.1.0.tgz#58b827e63edea1bc80c1de952b80cec9ac50e1ad"
  integrity sha1-WLgn5j7eobyAwd6VK4DOyaxQ4a0=
  dependencies:
    chalk "^3.0.0"
    diff-sequences "^25.1.0"
    jest-get-type "^25.1.0"
    pretty-format "^25.1.0"

jest-docblock@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-docblock/download/jest-docblock-25.1.0.tgz#0f44bea3d6ca6dfc38373d465b347c8818eccb64"
  integrity sha1-D0S+o9bKbfw4Nz1GWzR8iBjsy2Q=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-each/download/jest-each-25.1.0.tgz#a6b260992bdf451c2d64a0ccbb3ac25e9b44c26a"
  integrity sha1-prJgmSvfRRwtZKDMuzrCXptEwmo=
  dependencies:
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    jest-get-type "^25.1.0"
    jest-util "^25.1.0"
    pretty-format "^25.1.0"

jest-environment-jsdom@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-environment-jsdom/download/jest-environment-jsdom-25.1.0.tgz#6777ab8b3e90fd076801efd3bff8e98694ab43c3"
  integrity sha1-Z3eriz6Q/QdoAe/Tv/jphpSrQ8M=
  dependencies:
    "@jest/environment" "^25.1.0"
    "@jest/fake-timers" "^25.1.0"
    "@jest/types" "^25.1.0"
    jest-mock "^25.1.0"
    jest-util "^25.1.0"
    jsdom "^15.1.1"

jest-environment-node@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-environment-node/download/jest-environment-node-25.1.0.tgz#797bd89b378cf0bd794dc8e3dca6ef21126776db"
  integrity sha1-eXvYmzeM8L15Tcjj3KbvIRJndts=
  dependencies:
    "@jest/environment" "^25.1.0"
    "@jest/fake-timers" "^25.1.0"
    "@jest/types" "^25.1.0"
    jest-mock "^25.1.0"
    jest-util "^25.1.0"

jest-get-type@^24.9.0:
  version "24.9.0"
  resolved "http://registry.npm.qima-inc.com/jest-get-type/download/jest-get-type-24.9.0.tgz#1684a0c8a50f2e4901b6644ae861f579eed2ef0e"
  integrity sha1-FoSgyKUPLkkBtmRK6GH1ee7S7w4=

jest-get-type@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-get-type/download/jest-get-type-25.1.0.tgz#1cfe5fc34f148dc3a8a3b7275f6b9ce9e2e8a876"
  integrity sha1-HP5fw08UjcOoo7cnX2uc6eLoqHY=

jest-haste-map@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-haste-map/download/jest-haste-map-25.1.0.tgz#ae12163d284f19906260aa51fd405b5b2e5a4ad3"
  integrity sha1-rhIWPShPGZBiYKpR/UBbWy5aStM=
  dependencies:
    "@jest/types" "^25.1.0"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.3"
    jest-serializer "^25.1.0"
    jest-util "^25.1.0"
    jest-worker "^25.1.0"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
  optionalDependencies:
    fsevents "^2.1.2"

jest-haste-map@^25.3.0:
  version "25.3.0"
  resolved "http://registry.npm.qima-inc.com/jest-haste-map/download/jest-haste-map-25.3.0.tgz#b7683031c9c9ddc0521d311564108b244b11e4c6"
  integrity sha1-t2gwMcnJ3cBSHTEVZBCLJEsR5MY=
  dependencies:
    "@jest/types" "^25.3.0"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.3"
    jest-serializer "^25.2.6"
    jest-util "^25.3.0"
    jest-worker "^25.2.6"
    micromatch "^4.0.2"
    sane "^4.0.3"
    walker "^1.0.7"
    which "^2.0.2"
  optionalDependencies:
    fsevents "^2.1.2"

jest-jasmine2@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-jasmine2/download/jest-jasmine2-25.1.0.tgz#681b59158a430f08d5d0c1cce4f01353e4b48137"
  integrity sha1-aBtZFYpDDwjV0MHM5PATU+S0gTc=
  dependencies:
    "@babel/traverse" "^7.1.0"
    "@jest/environment" "^25.1.0"
    "@jest/source-map" "^25.1.0"
    "@jest/test-result" "^25.1.0"
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    co "^4.6.0"
    expect "^25.1.0"
    is-generator-fn "^2.0.0"
    jest-each "^25.1.0"
    jest-matcher-utils "^25.1.0"
    jest-message-util "^25.1.0"
    jest-runtime "^25.1.0"
    jest-snapshot "^25.1.0"
    jest-util "^25.1.0"
    pretty-format "^25.1.0"
    throat "^5.0.0"

jest-junit@^10.0.0:
  version "10.0.0"
  resolved "http://registry.npm.qima-inc.com/jest-junit/download/jest-junit-10.0.0.tgz#c94b91c24920a327c9d2a075e897b2dba4af494b"
  integrity sha1-yUuRwkkgoyfJ0qB16Jey26SvSUs=
  dependencies:
    jest-validate "^24.9.0"
    mkdirp "^0.5.1"
    strip-ansi "^5.2.0"
    uuid "^3.3.3"
    xml "^1.0.1"

jest-leak-detector@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-leak-detector/download/jest-leak-detector-25.1.0.tgz#ed6872d15aa1c72c0732d01bd073dacc7c38b5c6"
  integrity sha1-7Why0VqhxywHMtAb0HPazHw4tcY=
  dependencies:
    jest-get-type "^25.1.0"
    pretty-format "^25.1.0"

jest-matcher-utils@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-matcher-utils/download/jest-matcher-utils-25.1.0.tgz#fa5996c45c7193a3c24e73066fc14acdee020220"
  integrity sha1-+lmWxFxxk6PCTnMGb8FKze4CAiA=
  dependencies:
    chalk "^3.0.0"
    jest-diff "^25.1.0"
    jest-get-type "^25.1.0"
    pretty-format "^25.1.0"

jest-message-util@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-message-util/download/jest-message-util-25.1.0.tgz#702a9a5cb05c144b9aa73f06e17faa219389845e"
  integrity sha1-cCqaXLBcFEuapz8G4X+qIZOJhF4=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    "@jest/test-result" "^25.1.0"
    "@jest/types" "^25.1.0"
    "@types/stack-utils" "^1.0.1"
    chalk "^3.0.0"
    micromatch "^4.0.2"
    slash "^3.0.0"
    stack-utils "^1.0.1"

jest-mock@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-mock/download/jest-mock-25.1.0.tgz#411d549e1b326b7350b2e97303a64715c28615fd"
  integrity sha1-QR1Unhsya3NQsulzA6ZHFcKGFf0=
  dependencies:
    "@jest/types" "^25.1.0"

jest-pnp-resolver@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/jest-pnp-resolver/download/jest-pnp-resolver-1.2.1.tgz#ecdae604c077a7fbc70defb6d517c3c1c898923a"
  integrity sha1-7NrmBMB3p/vHDe+21RfDwciYkjo=

jest-regex-util@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-regex-util/download/jest-regex-util-25.1.0.tgz#efaf75914267741838e01de24da07b2192d16d87"
  integrity sha1-7691kUJndBg44B3iTaB7IZLRbYc=

jest-regex-util@^25.2.6:
  version "25.2.6"
  resolved "http://registry.npm.qima-inc.com/jest-regex-util/download/jest-regex-util-25.2.6.tgz#d847d38ba15d2118d3b06390056028d0f2fd3964"
  integrity sha1-2EfTi6FdIRjTsGOQBWAo0PL9OWQ=

jest-resolve-dependencies@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-resolve-dependencies/download/jest-resolve-dependencies-25.1.0.tgz#8a1789ec64eb6aaa77fd579a1066a783437e70d2"
  integrity sha1-iheJ7GTraqp3/VeaEGang0N+cNI=
  dependencies:
    "@jest/types" "^25.1.0"
    jest-regex-util "^25.1.0"
    jest-snapshot "^25.1.0"

jest-resolve@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-resolve/download/jest-resolve-25.1.0.tgz#23d8b6a4892362baf2662877c66aa241fa2eaea3"
  integrity sha1-I9i2pIkjYrryZih3xmqiQfourqM=
  dependencies:
    "@jest/types" "^25.1.0"
    browser-resolve "^1.11.3"
    chalk "^3.0.0"
    jest-pnp-resolver "^1.2.1"
    realpath-native "^1.1.0"

jest-runner@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-runner/download/jest-runner-25.1.0.tgz#fef433a4d42c89ab0a6b6b268e4a4fbe6b26e812"
  integrity sha1-/vQzpNQsiasKa2smjkpPvmsm6BI=
  dependencies:
    "@jest/console" "^25.1.0"
    "@jest/environment" "^25.1.0"
    "@jest/test-result" "^25.1.0"
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.3"
    jest-config "^25.1.0"
    jest-docblock "^25.1.0"
    jest-haste-map "^25.1.0"
    jest-jasmine2 "^25.1.0"
    jest-leak-detector "^25.1.0"
    jest-message-util "^25.1.0"
    jest-resolve "^25.1.0"
    jest-runtime "^25.1.0"
    jest-util "^25.1.0"
    jest-worker "^25.1.0"
    source-map-support "^0.5.6"
    throat "^5.0.0"

jest-runtime@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-runtime/download/jest-runtime-25.1.0.tgz#02683218f2f95aad0f2ec1c9cdb28c1dc0ec0314"
  integrity sha1-AmgyGPL5Wq0PLsHJzbKMHcDsAxQ=
  dependencies:
    "@jest/console" "^25.1.0"
    "@jest/environment" "^25.1.0"
    "@jest/source-map" "^25.1.0"
    "@jest/test-result" "^25.1.0"
    "@jest/transform" "^25.1.0"
    "@jest/types" "^25.1.0"
    "@types/yargs" "^15.0.0"
    chalk "^3.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.3"
    jest-config "^25.1.0"
    jest-haste-map "^25.1.0"
    jest-message-util "^25.1.0"
    jest-mock "^25.1.0"
    jest-regex-util "^25.1.0"
    jest-resolve "^25.1.0"
    jest-snapshot "^25.1.0"
    jest-util "^25.1.0"
    jest-validate "^25.1.0"
    realpath-native "^1.1.0"
    slash "^3.0.0"
    strip-bom "^4.0.0"
    yargs "^15.0.0"

jest-serializer@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-serializer/download/jest-serializer-25.1.0.tgz#73096ba90e07d19dec4a0c1dd89c355e2f129e5d"
  integrity sha1-cwlrqQ4H0Z3sSgwd2Jw1Xi8Snl0=

jest-serializer@^25.2.6:
  version "25.2.6"
  resolved "http://registry.npm.qima-inc.com/jest-serializer/download/jest-serializer-25.2.6.tgz#3bb4cc14fe0d8358489dbbefbb8a4e708ce039b7"
  integrity sha1-O7TMFP4Ng1hInbvvu4pOcIzgObc=

jest-snapshot@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-snapshot/download/jest-snapshot-25.1.0.tgz#d5880bd4b31faea100454608e15f8d77b9d221d9"
  integrity sha1-1YgL1LMfrqEARUYI4V+Nd7nSIdk=
  dependencies:
    "@babel/types" "^7.0.0"
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    expect "^25.1.0"
    jest-diff "^25.1.0"
    jest-get-type "^25.1.0"
    jest-matcher-utils "^25.1.0"
    jest-message-util "^25.1.0"
    jest-resolve "^25.1.0"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    pretty-format "^25.1.0"
    semver "^7.1.1"

jest-util@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-util/download/jest-util-25.1.0.tgz#7bc56f7b2abd534910e9fa252692f50624c897d9"
  integrity sha1-e8Vveyq9U0kQ6folJpL1BiTIl9k=
  dependencies:
    "@jest/types" "^25.1.0"
    chalk "^3.0.0"
    is-ci "^2.0.0"
    mkdirp "^0.5.1"

jest-util@^25.3.0:
  version "25.3.0"
  resolved "http://registry.npm.qima-inc.com/jest-util/download/jest-util-25.3.0.tgz#e3b0064165818f10d78514696fd25efba82cf049"
  integrity sha1-47AGQWWBjxDXhRRpb9Je+6gs8Ek=
  dependencies:
    "@jest/types" "^25.3.0"
    chalk "^3.0.0"
    is-ci "^2.0.0"
    make-dir "^3.0.0"

jest-validate@^24.9.0:
  version "24.9.0"
  resolved "http://registry.npm.qima-inc.com/jest-validate/download/jest-validate-24.9.0.tgz#0775c55360d173cd854e40180756d4ff52def8ab"
  integrity sha1-B3XFU2DRc82FTkAYB1bU/1Le+Ks=
  dependencies:
    "@jest/types" "^24.9.0"
    camelcase "^5.3.1"
    chalk "^2.0.1"
    jest-get-type "^24.9.0"
    leven "^3.1.0"
    pretty-format "^24.9.0"

jest-validate@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-validate/download/jest-validate-25.1.0.tgz#1469fa19f627bb0a9a98e289f3e9ab6a668c732a"
  integrity sha1-FGn6GfYnuwqamOKJ8+mramaMcyo=
  dependencies:
    "@jest/types" "^25.1.0"
    camelcase "^5.3.1"
    chalk "^3.0.0"
    jest-get-type "^25.1.0"
    leven "^3.1.0"
    pretty-format "^25.1.0"

jest-watcher@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-watcher/download/jest-watcher-25.1.0.tgz#97cb4a937f676f64c9fad2d07b824c56808e9806"
  integrity sha1-l8tKk39nb2TJ+tLQe4JMVoCOmAY=
  dependencies:
    "@jest/test-result" "^25.1.0"
    "@jest/types" "^25.1.0"
    ansi-escapes "^4.2.1"
    chalk "^3.0.0"
    jest-util "^25.1.0"
    string-length "^3.1.0"

jest-worker@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest-worker/download/jest-worker-25.1.0.tgz#75d038bad6fdf58eba0d2ec1835856c497e3907a"
  integrity sha1-ddA4utb99Y66DS7Bg1hWxJfjkHo=
  dependencies:
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest-worker@^25.2.6:
  version "25.2.6"
  resolved "http://registry.npm.qima-inc.com/jest-worker/download/jest-worker-25.2.6.tgz#d1292625326794ce187c38f51109faced3846c58"
  integrity sha1-0SkmJTJnlM4YfDj1EQn6ztOEbFg=
  dependencies:
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jest@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/jest/download/jest-25.1.0.tgz#b85ef1ddba2fdb00d295deebbd13567106d35be9"
  integrity sha1-uF7x3bov2wDSld7rvRNWcQbTW+k=
  dependencies:
    "@jest/core" "^25.1.0"
    import-local "^3.0.2"
    jest-cli "^25.1.0"

js-levenshtein@^1.1.3:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/js-levenshtein/download/js-levenshtein-1.1.6.tgz#c6cee58eb3550372df8deb85fad5ce66ce01d59d"
  integrity sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.13.1"
  resolved "http://registry.npm.qima-inc.com/js-yaml/download/js-yaml-3.13.1.tgz#aff151b30bfdfa8e49e05da22e7415e9dfa37847"
  integrity sha1-r/FRswv9+o5J4F2iLnQV6d+jeEc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsdom@^15.1.1:
  version "15.2.1"
  resolved "http://registry.npm.qima-inc.com/jsdom/download/jsdom-15.2.1.tgz#d2feb1aef7183f86be521b8c6833ff5296d07ec5"
  integrity sha1-0v6xrvcYP4a+UhuMaDP/UpbQfsU=
  dependencies:
    abab "^2.0.0"
    acorn "^7.1.0"
    acorn-globals "^4.3.2"
    array-equal "^1.0.0"
    cssom "^0.4.1"
    cssstyle "^2.0.0"
    data-urls "^1.1.0"
    domexception "^1.0.1"
    escodegen "^1.11.1"
    html-encoding-sniffer "^1.0.2"
    nwsapi "^2.2.0"
    parse5 "5.1.0"
    pn "^1.1.0"
    request "^2.88.0"
    request-promise-native "^1.0.7"
    saxes "^3.1.9"
    symbol-tree "^3.2.2"
    tough-cookie "^3.0.1"
    w3c-hr-time "^1.0.1"
    w3c-xmlserializer "^1.1.2"
    webidl-conversions "^4.0.2"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^7.0.0"
    ws "^7.0.0"
    xml-name-validator "^3.0.0"

jsesc@^2.5.1:
  version "2.5.2"
  resolved "http://registry.npm.qima-inc.com/jsesc/download/jsesc-2.5.2.tgz#80564d2e483dacf6e8ef209650a67df3f0c283a4"
  integrity sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"
  integrity sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=

json-parse-better-errors@^1.0.1, json-parse-better-errors@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz#bb867cfb3450e69107c131d1c514bab3dc8bcaa9"
  integrity sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.qima-inc.com/json-schema/download/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"
  integrity sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json5@2.x:
  version "2.1.3"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-2.1.3.tgz#c9b0f7fa9233bfe5807fe66fcf3a5617ed597d43"
  integrity sha1-ybD3+pIzv+WAf+ZvzzpWF+1ZfUM=
  dependencies:
    minimist "^1.2.5"

json5@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-1.0.1.tgz#779fb0018604fa854eacbf6252180d83543e3dbe"
  integrity sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=
  dependencies:
    minimist "^1.2.0"

json5@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/json5/download/json5-2.1.0.tgz#e7a0c62c48285c628d20a10b85c89bb807c32850"
  integrity sha1-56DGLEgoXGKNIKELhcibuAfDKFA=
  dependencies:
    minimist "^1.2.0"

jsprim@^1.2.2:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/jsprim/download/jsprim-1.4.1.tgz#313e66bc1e5cc06e438bc1b7499c2e5c56acb6a2"
  integrity sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.2.3"
    verror "1.10.0"

jsx-ast-utils@^2.1.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/jsx-ast-utils/download/jsx-ast-utils-2.2.1.tgz#4d4973ebf8b9d2837ee91a8208cc66f3a2776cfb"
  integrity sha1-TUlz6/i50oN+6RqCCMxm86J3bPs=
  dependencies:
    array-includes "^3.0.3"
    object.assign "^4.1.0"

keygrip@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/keygrip/download/keygrip-1.0.3.tgz#399d709f0aed2bab0a059e0cdd3a5023a053e1dc"
  integrity sha1-OZ1wnwrtK6sKBZ4M3TpQI6BT4dw=

kind-of@^3.0.2, kind-of@^3.0.3, kind-of@^3.2.0:
  version "3.2.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  integrity sha1-IIE989cSkosgc3hpGkUGb65y3Vc=
  dependencies:
    is-buffer "^1.1.5"

kind-of@^5.0.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-5.1.0.tgz#729c91e2d857b7a419a1f9aa65685c4c33f5845d"
  integrity sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=

kind-of@^6.0.0, kind-of@^6.0.2:
  version "6.0.2"
  resolved "http://registry.npm.qima-inc.com/kind-of/download/kind-of-6.0.2.tgz#01146b36a6218e64e58f3a8d66de5d7fc6f6d051"
  integrity sha1-ARRrNqYhjmTljzqNZt5df8b20FE=

kleur@^3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/kleur/download/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-3.2.1.tgz#a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7"
  integrity sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=
  dependencies:
    any-promise "^1.1.0"

koa-compose@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/koa-compose/download/koa-compose-4.1.0.tgz#507306b9371901db41121c812e923d0d67d3e877"
  integrity sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc=

koa-compress@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-compress/download/koa-compress-3.0.0.tgz#3194059c215cbc24e59bbc84c2c7453a4c88564f"
  integrity sha1-MZQFnCFcvCTlm7yEwsdFOkyIVk8=
  dependencies:
    bytes "^3.0.0"
    compressible "^2.0.0"
    koa-is-json "^1.0.0"
    statuses "^1.0.0"

koa-connect@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/koa-connect/download/koa-connect-2.0.1.tgz#2acad159c33862de1d73aa4562a48de13f137c0f"
  integrity sha1-KsrRWcM4Yt4dc6pFYqSN4T8TfA8=

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/koa-convert/download/koa-convert-1.2.0.tgz#da40875df49de0539098d1700b50820cebcd21d0"
  integrity sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

koa-is-json@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-is-json/download/koa-is-json-1.0.0.tgz#273c07edcdcb8df6a2c1ab7d59ee76491451ec14"
  integrity sha1-JzwH7c3Ljfaiwat9We52SRRR7BQ=

koa-route@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/koa-route/download/koa-route-3.2.0.tgz#76298b99a6bcfa9e38cab6fe5c79a8733e758bce"
  integrity sha1-dimLmaa8+p44yrb+XHmocz51i84=
  dependencies:
    debug "*"
    methods "~1.1.0"
    path-to-regexp "^1.2.0"

koa-send@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-send/download/koa-send-5.0.0.tgz#5e8441e07ef55737734d7ced25b842e50646e7eb"
  integrity sha1-XoRB4H71VzdzTXztJbhC5QZG5+s=
  dependencies:
    debug "^3.1.0"
    http-errors "^1.6.3"
    mz "^2.7.0"
    resolve-path "^1.4.0"

koa-static@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/koa-static/download/koa-static-5.0.0.tgz#5e92fc96b537ad5219f425319c95b64772776943"
  integrity sha1-XpL8lrU3rVIZ9CUxnJW2R3J3aUM=
  dependencies:
    debug "^3.1.0"
    koa-send "^5.0.0"

koa@^2.5.3:
  version "2.8.1"
  resolved "http://registry.npm.qima-inc.com/koa/download/koa-2.8.1.tgz#98e13b267ab8a1868f015a4b41b5a52e31457ce5"
  integrity sha1-mOE7Jnq4oYaPAVpLQbWlLjFFfOU=
  dependencies:
    accepts "^1.3.5"
    cache-content-type "^1.0.0"
    content-disposition "~0.5.2"
    content-type "^1.0.4"
    cookies "~0.7.1"
    debug "~3.1.0"
    delegates "^1.0.0"
    depd "^1.1.2"
    destroy "^1.0.4"
    error-inject "^1.0.0"
    escape-html "^1.0.3"
    fresh "~0.5.2"
    http-assert "^1.3.0"
    http-errors "^1.6.3"
    is-generator-function "^1.0.7"
    koa-compose "^4.1.0"
    koa-convert "^1.2.0"
    koa-is-json "^1.0.0"
    on-finished "^2.3.0"
    only "~0.0.2"
    parseurl "^1.3.2"
    statuses "^1.5.0"
    type-is "^1.6.16"
    vary "^1.1.2"

last-call-webpack-plugin@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/last-call-webpack-plugin/download/last-call-webpack-plugin-3.0.0.tgz#9742df0e10e3cf46e5c0381c2de90d3a7a2d7555"
  integrity sha1-l0LfDhDjz0blwDgcLekNOnotdVU=
  dependencies:
    lodash "^4.17.5"
    webpack-sources "^1.1.0"

leven@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/leven/download/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  integrity sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

lines-and-columns@^1.1.6:
  version "1.1.6"
  resolved "http://registry.npm.qima-inc.com/lines-and-columns/download/lines-and-columns-1.1.6.tgz#1c00c743b433cd0a4e80758f7b64a57440d9ff00"
  integrity sha1-HADHQ7QzzQpOgHWPe2SldEDZ/wA=

lint-staged@^8.1.0:
  version "8.2.1"
  resolved "http://registry.npm.qima-inc.com/lint-staged/download/lint-staged-8.2.1.tgz#752fcf222d9d28f323a3b80f1e668f3654ff221f"
  integrity sha1-dS/PIi2dKPMjo7gPHmaPNlT/Ih8=
  dependencies:
    chalk "^2.3.1"
    commander "^2.14.1"
    cosmiconfig "^5.2.0"
    debug "^3.1.0"
    dedent "^0.7.0"
    del "^3.0.0"
    execa "^1.0.0"
    g-status "^2.0.2"
    is-glob "^4.0.0"
    is-windows "^1.0.2"
    listr "^0.14.2"
    listr-update-renderer "^0.5.0"
    lodash "^4.17.11"
    log-symbols "^2.2.0"
    micromatch "^3.1.8"
    npm-which "^3.0.1"
    p-map "^1.1.1"
    path-is-inside "^1.0.2"
    pify "^3.0.0"
    please-upgrade-node "^3.0.2"
    staged-git-files "1.1.2"
    string-argv "^0.0.2"
    stringify-object "^3.2.2"
    yup "^0.27.0"

listr-silent-renderer@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/listr-silent-renderer/download/listr-silent-renderer-1.1.1.tgz#924b5a3757153770bf1a8e3fbf74b8bbf3f9242e"
  integrity sha1-kktaN1cVN3C/Go4/v3S4u/P5JC4=

listr-update-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/listr-update-renderer/download/listr-update-renderer-0.5.0.tgz#4ea8368548a7b8aecb7e06d8c95cb45ae2ede6a2"
  integrity sha1-Tqg2hUinuK7LfgbYyVy0WuLt5qI=
  dependencies:
    chalk "^1.1.3"
    cli-truncate "^0.2.1"
    elegant-spinner "^1.0.1"
    figures "^1.7.0"
    indent-string "^3.0.0"
    log-symbols "^1.0.2"
    log-update "^2.3.0"
    strip-ansi "^3.0.1"

listr-verbose-renderer@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/listr-verbose-renderer/download/listr-verbose-renderer-0.5.0.tgz#f1132167535ea4c1261102b9f28dac7cba1e03db"
  integrity sha1-8RMhZ1NepMEmEQK58o2sfLoeA9s=
  dependencies:
    chalk "^2.4.1"
    cli-cursor "^2.1.0"
    date-fns "^1.27.2"
    figures "^2.0.0"

listr@^0.14.2:
  version "0.14.3"
  resolved "http://registry.npm.qima-inc.com/listr/download/listr-0.14.3.tgz#2fea909604e434be464c50bddba0d496928fa586"
  integrity sha1-L+qQlgTkNL5GTFC926DUlpKPpYY=
  dependencies:
    "@samverschueren/stream-to-observable" "^0.3.0"
    is-observable "^1.1.0"
    is-promise "^2.1.0"
    is-stream "^1.1.0"
    listr-silent-renderer "^1.1.1"
    listr-update-renderer "^0.5.0"
    listr-verbose-renderer "^0.5.0"
    p-map "^2.0.0"
    rxjs "^6.3.3"

load-json-file@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/load-json-file/download/load-json-file-4.0.0.tgz#2f5f45ab91e33216234fd53adab668eb4ec0993b"
  integrity sha1-L19Fq5HjMhYjT9U62rZo607AmTs=
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^4.0.0"
    pify "^3.0.0"
    strip-bom "^3.0.0"

loader-runner@^2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/loader-runner/download/loader-runner-2.4.0.tgz#ed47066bfe534d7e84c4c7b9998c2a75607d9357"
  integrity sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=

loader-utils@^1.0.2, loader-utils@^1.1.0, loader-utils@^1.2.3:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/loader-utils/download/loader-utils-1.2.3.tgz#1ff5dc6911c9f0a062531a4c04b609406108c2c7"
  integrity sha1-H/XcaRHJ8KBiUxpMBLYJQGEIwsc=
  dependencies:
    big.js "^5.2.2"
    emojis-list "^2.0.0"
    json5 "^1.0.1"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  integrity sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

locate-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-3.0.0.tgz#dbec3b3ab759758071b58fe59fc41871af21400e"
  integrity sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=
  dependencies:
    p-locate "^3.0.0"
    path-exists "^3.0.0"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/locate-path/download/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

lodash.memoize@4.x, lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"
  integrity sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "http://registry.npm.qima-inc.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.qima-inc.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash@4.17.15, lodash@^4.17.10, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.13, lodash@^4.17.14, lodash@^4.17.15, lodash@^4.17.5:
  version "4.17.15"
  resolved "http://registry.npm.qima-inc.com/lodash/download/lodash-4.17.15.tgz#b447f6670a0455bbfeedd11392eff330ea097548"
  integrity sha1-tEf2ZwoEVbv+7dETku/zMOoJdUg=

log-symbols@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-1.0.2.tgz#376ff7b58ea3086a0f09facc74617eca501e1a18"
  integrity sha1-N2/3tY6jCGoPCfrMdGF+ylAeGhg=
  dependencies:
    chalk "^1.0.0"

log-symbols@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-2.2.0.tgz#5740e1c5d6f0dfda4ad9323b5332107ef6b4c40a"
  integrity sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=
  dependencies:
    chalk "^2.0.1"

log-symbols@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-3.0.0.tgz#f3a08516a5dea893336a7dee14d18a1cfdab77c4"
  integrity sha1-86CFFqXeqJMzan3uFNGKHP2rd8Q=
  dependencies:
    chalk "^2.4.2"

log-symbols@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/log-symbols/download/log-symbols-4.0.0.tgz#69b3cc46d20f448eccdb75ea1fa733d9e821c920"
  integrity sha1-abPMRtIPRI7M23XqH6cz2eghySA=
  dependencies:
    chalk "^4.0.0"

log-update@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/log-update/download/log-update-2.3.0.tgz#88328fd7d1ce7938b29283746f0b1bc126b24708"
  integrity sha1-iDKP19HOeTiykoN0bwsbwSayRwg=
  dependencies:
    ansi-escapes "^3.0.0"
    cli-cursor "^2.0.0"
    wrap-ansi "^3.0.1"

loglevelnext@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/loglevelnext/download/loglevelnext-3.0.1.tgz#e3e4659c4061c09264f6812c33586dc55a009a04"
  integrity sha1-4+RlnEBhwJJk9oEsM1htxVoAmgQ=

lolex@^5.0.0:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/lolex/download/lolex-5.1.2.tgz#953694d098ce7c07bc5ed6d0e42bc6c0c6d5a367"
  integrity sha1-lTaU0JjOfAe8XtbQ5CvGwMbVo2c=
  dependencies:
    "@sinonjs/commons" "^1.7.0"

loose-envify@^1.0.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://registry.npm.qima-inc.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

make-dir@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

make-dir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/make-dir/download/make-dir-3.0.0.tgz#1b5f39f6b9270ed33f9f054c5c0f84304989f801"
  integrity sha1-G1859rknDtM/nwVMXA+EMEmJ+AE=
  dependencies:
    semver "^6.0.0"

make-error@1.x:
  version "1.3.6"
  resolved "http://registry.npm.qima-inc.com/make-error/download/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.x:
  version "1.0.11"
  resolved "http://registry.npm.qima-inc.com/makeerror/download/makeerror-1.0.11.tgz#e01a5c9109f2af79660e4e8b9587790184f5a96c"
  integrity sha1-4BpckQnyr3lmDk6LlYd5AYT1qWw=
  dependencies:
    tmpl "1.0.x"

map-cache@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/map-cache/download/map-cache-0.2.2.tgz#c32abd0bd6525d9b051645bb4f26ac5dc98a0dbf"
  integrity sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=

map-visit@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/map-visit/download/map-visit-1.0.0.tgz#ecdca8f13144e660f1b5bd41f12f3479d98dfb8f"
  integrity sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=
  dependencies:
    object-visit "^1.0.0"

matcher@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/matcher/download/matcher-1.1.1.tgz#51d8301e138f840982b338b116bb0c09af62c1c2"
  integrity sha1-UdgwHhOPhAmCszixFrsMCa9iwcI=
  dependencies:
    escape-string-regexp "^1.0.4"

md5.js@^1.3.4:
  version "1.3.5"
  resolved "http://registry.npm.qima-inc.com/md5.js/download/md5.js-1.3.5.tgz#b5d07b8e3216e3e27cd728d72f70d1e6a342005f"
  integrity sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"
    safe-buffer "^5.1.2"

md5@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/md5/download/md5-2.2.1.tgz#53ab38d5fe3c8891ba465329ea23fac0540126f9"
  integrity sha1-U6s41f48iJG6RlMp6iP6wFQBJvk=
  dependencies:
    charenc "~0.0.1"
    crypt "~0.0.1"
    is-buffer "~1.1.1"

mdn-data@2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/mdn-data/download/mdn-data-2.0.4.tgz#699b3c38ac6f1d728091a64650b65d388502fd5b"
  integrity sha1-aZs8OKxvHXKAkaZGULZdOIUC/Vs=

mdn-data@~1.1.0:
  version "1.1.4"
  resolved "http://registry.npm.qima-inc.com/mdn-data/download/mdn-data-1.1.4.tgz#50b5d4ffc4575276573c4eedb8780812a8419f01"
  integrity sha1-ULXU/8RXUnZXPE7tuHgIEqhBnwE=

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"
  integrity sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=

memory-fs@^0.4.0, memory-fs@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.qima-inc.com/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  integrity sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"
  integrity sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.2.3:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/merge2/download/merge2-1.2.4.tgz#c9269589e6885a60cf80605d9522d4b67ca646e3"
  integrity sha1-ySaVieaIWmDPgGBdlSLUtnymRuM=

methods@~1.1.0, methods@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"
  integrity sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=

micromatch@4.x, micromatch@^4.0.0, micromatch@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-4.0.2.tgz#4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259"
  integrity sha1-T8sJmb+fvC/L3SEvbWKbmlbDklk=
  dependencies:
    braces "^3.0.1"
    picomatch "^2.0.5"

micromatch@^3.1.10, micromatch@^3.1.4, micromatch@^3.1.8:
  version "3.1.10"
  resolved "http://registry.npm.qima-inc.com/micromatch/download/micromatch-3.1.10.tgz#70859bc95c9840952f359a068a3fc49f9ecfac23"
  integrity sha1-cIWbyVyYQJUvNZoGij/En57PrCM=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    braces "^2.3.1"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    extglob "^2.0.4"
    fragment-cache "^0.2.1"
    kind-of "^6.0.2"
    nanomatch "^1.2.9"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.2"

miller-rabin@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/miller-rabin/download/miller-rabin-4.0.1.tgz#f080351c865b0dc562a8462966daa53543c78a4d"
  integrity sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=
  dependencies:
    bn.js "^4.0.0"
    brorand "^1.0.1"

mime-db@1.40.0:
  version "1.40.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.40.0.tgz#a65057e998db090f732a68f6c276d387d4126c32"
  integrity sha1-plBX6ZjbCQ9zKmj2wnbTh9QSbDI=

mime-db@1.42.0:
  version "1.42.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.42.0.tgz?cache=0&sync_timestamp=1569468742433&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-db%2Fdownload%2Fmime-db-1.42.0.tgz#3e252907b4c7adb906597b4b65636272cf9e7bac"
  integrity sha1-PiUpB7THrbkGWXtLZWNics+ee6w=

"mime-db@>= 1.40.0 < 2":
  version "1.41.0"
  resolved "http://registry.npm.qima-inc.com/mime-db/download/mime-db-1.41.0.tgz#9110408e1f6aa1b34aef51f2c9df3caddf46b6a0"
  integrity sha1-kRBAjh9qobNK71Hyyd88rd9GtqA=

mime-types@^2.1.12, mime-types@^2.1.15, mime-types@^2.1.18, mime-types@~2.1.24:
  version "2.1.24"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-2.1.24.tgz#b6f8d0b3e951efb77dedeca194cff6d16f676f81"
  integrity sha1-tvjQs+lR77d97eyhlM/20W9nb4E=
  dependencies:
    mime-db "1.40.0"

mime-types@~2.1.19:
  version "2.1.25"
  resolved "http://registry.npm.qima-inc.com/mime-types/download/mime-types-2.1.25.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fmime-types%2Fdownload%2Fmime-types-2.1.25.tgz#39772d46621f93e2a80a856c53b86a62156a6437"
  integrity sha1-OXctRmIfk+KoCoVsU7hqYhVqZDc=
  dependencies:
    mime-db "1.42.0"

mime@1.6.0, mime@^1.3.4:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mime@2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.3.1.tgz#b1621c54d63b97c47d3cfe7f7215f7d64517c369"
  integrity sha1-sWIcVNY7l8R9PP5/chX31kUXw2k=

mime@2.4.5:
  version "2.4.5"
  resolved "http://registry.npm.qima-inc.com/mime/download/mime-2.4.5.tgz#d8de2ecb92982dedbb6541c9b6841d7f218ea009"
  integrity sha1-2N4uy5KYLe27ZUHJtoQdfyGOoAk=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mini-css-extract-plugin@^0.8.0:
  version "0.8.0"
  resolved "http://registry.npm.qima-inc.com/mini-css-extract-plugin/download/mini-css-extract-plugin-0.8.0.tgz#81d41ec4fe58c713a96ad7c723cdb2d0bd4d70e1"
  integrity sha1-gdQexP5YxxOpatfHI82y0L1NcOE=
  dependencies:
    loader-utils "^1.1.0"
    normalize-url "1.9.1"
    schema-utils "^1.0.0"
    webpack-sources "^1.1.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz#2e194de044626d4a10e7f7fbc00ce73e83e4d5c7"
  integrity sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=

minimalistic-crypto-utils@^1.0.0, minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz#f6c00c1c0b082246e5c4d99dfb8c7c083b2b582a"
  integrity sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=

minimatch@3.0.4, minimatch@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  integrity sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"
  integrity sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=

minimist@^1.1.0, minimist@^1.1.1, minimist@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"
  integrity sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=

minimist@^1.2.5:
  version "1.2.5"
  resolved "http://registry.npm.qima-inc.com/minimist/download/minimist-1.2.5.tgz#67d66014b66a6a8aaa0c083c5fd58df4e4e97602"
  integrity sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI=

mississippi@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/mississippi/download/mississippi-3.0.0.tgz#ea0a3291f97e0b5e8776b363d5f0a12d94c67022"
  integrity sha1-6goykfl+C16HdrNj1fChLZTGcCI=
  dependencies:
    concat-stream "^1.5.0"
    duplexify "^3.4.2"
    end-of-stream "^1.1.0"
    flush-write-stream "^1.0.0"
    from2 "^2.1.0"
    parallel-transform "^1.1.0"
    pump "^3.0.0"
    pumpify "^1.3.3"
    stream-each "^1.1.0"
    through2 "^2.0.0"

mixin-deep@^1.2.0:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/mixin-deep/download/mixin-deep-1.3.2.tgz#1120b43dc359a785dce65b55b82e257ccf479566"
  integrity sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=
  dependencies:
    for-in "^1.0.2"
    is-extendable "^1.0.1"

mkdirp@1.x:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mkdirp@^0.5.1, mkdirp@~0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  integrity sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=
  dependencies:
    minimist "0.0.8"

mkdirp@^0.5.3:
  version "0.5.5"
  resolved "http://registry.npm.qima-inc.com/mkdirp/download/mkdirp-0.5.5.tgz#d91cefd62d1436ca0f41620e251288d420099def"
  integrity sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=
  dependencies:
    minimist "^1.2.5"

move-concurrently@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/move-concurrently/download/move-concurrently-1.0.1.tgz#be2c005fda32e0b29af1f05d7c4b33214c701f92"
  integrity sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=
  dependencies:
    aproba "^1.1.1"
    copy-concurrently "^1.0.0"
    fs-write-stream-atomic "^1.0.8"
    mkdirp "^0.5.1"
    rimraf "^2.5.4"
    run-queue "^1.0.3"

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.1.tgz#30a5864eb3ebb0a66f2ebe6d727af06a09d86e0a"
  integrity sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo=

ms@^2.0.0, ms@^2.1.1:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/ms/download/ms-2.1.2.tgz#d09d1f357b443f493382a8eb3ccd183872ae6009"
  integrity sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=

multimap@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/multimap/download/multimap-1.1.0.tgz#5263febc085a1791c33b59bb3afc6a76a2a10ca8"
  integrity sha1-UmP+vAhaF5HDO1m7OvxqdqKhDKg=

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://registry.npm.qima-inc.com/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mz@^2.7.0:
  version "2.7.0"
  resolved "http://registry.npm.qima-inc.com/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nan@^2.12.1:
  version "2.14.0"
  resolved "http://registry.npm.qima-inc.com/nan/download/nan-2.14.0.tgz#7818f722027b2459a86f0295d434d1fc2336c52c"
  integrity sha1-eBj3IgJ7JFmobwKV1DTR/CM2xSw=

nanoid@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/nanoid/download/nanoid-2.1.0.tgz#3de3dbd68cfb2f3bd52550e2bfd439cf75040eb2"
  integrity sha1-PePb1oz7LzvVJVDiv9Q5z3UEDrI=

nanomatch@^1.2.9:
  version "1.2.13"
  resolved "http://registry.npm.qima-inc.com/nanomatch/download/nanomatch-1.2.13.tgz#b87a8aa4fc0de8fe6be88895b38983ff265bd119"
  integrity sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=
  dependencies:
    arr-diff "^4.0.0"
    array-unique "^0.3.2"
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    fragment-cache "^0.2.1"
    is-windows "^1.0.2"
    kind-of "^6.0.2"
    object.pick "^1.3.0"
    regex-not "^1.0.0"
    snapdragon "^0.8.1"
    to-regex "^3.0.1"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

negotiator@0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.qima-inc.com/negotiator/download/negotiator-0.6.2.tgz#feacf7ccf525a77ae9634436a64883ffeca346fb"
  integrity sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=

neo-async@^2.5.0, neo-async@^2.6.1:
  version "2.6.1"
  resolved "http://registry.npm.qima-inc.com/neo-async/download/neo-async-2.6.1.tgz#ac27ada66167fa8849a6addd837f6b189ad2081c"
  integrity sha1-rCetpmFn+ohJpq3dg39rGJrSCBw=

nice-try@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/nice-try/download/nice-try-1.0.5.tgz#a3378a7696ce7d223e88fc9b764bd7ef1089e366"
  integrity sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-libs-browser@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/node-libs-browser/download/node-libs-browser-2.2.1.tgz#b64f513d18338625f90346d27b0d235e631f6425"
  integrity sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.2.0"
    buffer "^4.3.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "^3.11.0"
    domain-browser "^1.1.1"
    events "^3.0.0"
    https-browserify "^1.0.0"
    os-browserify "^0.3.0"
    path-browserify "0.0.1"
    process "^0.11.10"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.3.3"
    stream-browserify "^2.0.1"
    stream-http "^2.7.2"
    string_decoder "^1.0.0"
    timers-browserify "^2.0.4"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.11.0"
    vm-browserify "^1.0.1"

node-modules-regexp@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/node-modules-regexp/download/node-modules-regexp-1.0.0.tgz#8d9dbe28964a4ac5712e9131642107c71e90ec40"
  integrity sha1-jZ2+KJZKSsVxLpExZCEHxx6Q7EA=

node-notifier@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/node-notifier/download/node-notifier-6.0.0.tgz#cea319e06baa16deec8ce5cd7f133c4a46b68e12"
  integrity sha1-zqMZ4GuqFt7sjOXNfxM8Ska2jhI=
  dependencies:
    growly "^1.3.0"
    is-wsl "^2.1.1"
    semver "^6.3.0"
    shellwords "^0.1.1"
    which "^1.3.1"

node-releases@^1.1.29:
  version "1.1.30"
  resolved "http://registry.npm.qima-inc.com/node-releases/download/node-releases-1.1.30.tgz#35eebf129c63baeb6d8ddeda3c35b05abfd37f7f"
  integrity sha1-Ne6/Epxjuuttjd7aPDWwWr/Tf38=
  dependencies:
    semver "^5.3.0"

node-source-walk@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/node-source-walk/download/node-source-walk-4.2.0.tgz#c2efe731ea8ba9c03c562aa0a9d984e54f27bc2c"
  integrity sha1-wu/nMeqLqcA8ViqgqdmE5U8nvCw=
  dependencies:
    "@babel/parser" "^7.0.0"

normalize-package-data@^2.3.2, normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  integrity sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

normalize-url@1.9.1:
  version "1.9.1"
  resolved "http://registry.npm.qima-inc.com/normalize-url/download/normalize-url-1.9.1.tgz#2cc0d66b31ea23036458436e3620d85954c66c3c"
  integrity sha1-LMDWazHqIwNkWENuNiDYWVTGbDw=
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

normalize-url@^3.0.0:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/normalize-url/download/normalize-url-3.3.0.tgz#b2e1c4dc4f7c6d57743df733a4f5978d18650559"
  integrity sha1-suHE3E98bVd0PfczpPWXjRhlBVk=

npm-path@^2.0.2:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/npm-path/download/npm-path-2.0.4.tgz#c641347a5ff9d6a09e4d9bce5580c4f505278e64"
  integrity sha1-xkE0el/51qCeTZvOVYDE9QUnjmQ=
  dependencies:
    which "^1.2.10"

npm-run-path@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-2.0.2.tgz#35a9232dfa35d7067b4cb2ddf2357b1871536c5f"
  integrity sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=
  dependencies:
    path-key "^2.0.0"

npm-run-path@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/npm-run-path/download/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

npm-which@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/npm-which/download/npm-which-3.0.1.tgz#9225f26ec3a285c209cae67c3b11a6b4ab7140aa"
  integrity sha1-kiXybsOihcIJyuZ8OxGmtKtxQKo=
  dependencies:
    commander "^2.9.0"
    npm-path "^2.0.2"
    which "^1.2.10"

nth-check@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/nth-check/download/nth-check-1.0.2.tgz#b2bd295c37e3dd58a3bf0700376663ba4d9cf05c"
  integrity sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=
  dependencies:
    boolbase "~1.0.0"

num2fraction@^1.2.2:
  version "1.2.2"
  resolved "http://registry.npm.qima-inc.com/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"
  integrity sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"
  integrity sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=

nwsapi@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/nwsapi/download/nwsapi-2.2.0.tgz#204879a9e3d068ff2a55139c2c772780681a38b7"
  integrity sha1-IEh5qePQaP8qVROcLHcngGgaOLc=

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://registry.npm.qima-inc.com/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.0, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://registry.npm.qima-inc.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-copy@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/object-copy/download/object-copy-0.1.0.tgz#7e7d858b781bd7c991a41ba975ed3812754e998c"
  integrity sha1-fn2Fi3gb18mRpBupde04EnVOmYw=
  dependencies:
    copy-descriptor "^0.1.0"
    define-property "^0.2.5"
    kind-of "^3.0.3"

object-inspect@^1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/object-inspect/download/object-inspect-1.6.0.tgz#c70b6cbf72f274aab4c34c0c82f5167bf82cf15b"
  integrity sha1-xwtsv3LydKq0w0wMgvUWe/gs8Vs=

object-keys@^1.0.11, object-keys@^1.0.12, object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object-visit@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/object-visit/download/object-visit-1.0.1.tgz#f79c4493af0c5377b59fe39d395e41042dd045bb"
  integrity sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=
  dependencies:
    isobject "^3.0.0"

object.assign@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/object.assign/download/object.assign-4.1.0.tgz#968bf1100d7956bb3ca086f006f846b3bc4008da"
  integrity sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.1"
    has-symbols "^1.0.0"
    object-keys "^1.0.11"

object.entries@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/object.entries/download/object.entries-1.1.0.tgz#2024fc6d6ba246aee38bdb0ffd5cfbcf371b7519"
  integrity sha1-ICT8bWuiRq7ji9sP/Vz7zzcbdRk=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.12.0"
    function-bind "^1.1.1"
    has "^1.0.3"

object.fromentries@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/object.fromentries/download/object.fromentries-2.0.0.tgz#49a543d92151f8277b3ac9600f1e930b189d30ab"
  integrity sha1-SaVD2SFR+Cd7OslgDx6TCxidMKs=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.11.0"
    function-bind "^1.1.1"
    has "^1.0.1"

object.getownpropertydescriptors@^2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.0.3.tgz#8758c846f5b407adab0f236e0986f14b051caa16"
  integrity sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY=
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.5.1"

object.pick@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/object.pick/download/object.pick-1.3.0.tgz#87a10ac4c1694bd2e1cbf53591a66141fb5dd747"
  integrity sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=
  dependencies:
    isobject "^3.0.1"

object.values@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/object.values/download/object.values-1.1.0.tgz#bf6810ef5da3e5325790eaaa2be213ea84624da9"
  integrity sha1-v2gQ712j5TJXkOqqK+IT6oRiTak=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.12.0"
    function-bind "^1.1.1"
    has "^1.0.3"

on-finished@^2.3.0, on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  integrity sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/onetime/download/onetime-5.1.0.tgz#fff0f3c91617fe62bb50189636e99ac8a6df7be5"
  integrity sha1-//DzyRYX/mK7UBiWNumayKbfe+U=
  dependencies:
    mimic-fn "^2.1.0"

only@~0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/only/download/only-0.0.2.tgz#2afde84d03e50b9a8edc444e30610a70295edfb4"
  integrity sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q=

open@^6.4.0:
  version "6.4.0"
  resolved "http://registry.npm.qima-inc.com/open/download/open-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

opener@^1.5.1:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/opener/download/opener-1.5.1.tgz#6d2f0e77f1a0af0032aca716c2c1fbb8e7e8abed"
  integrity sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0=

optimize-css-assets-webpack-plugin@^5.0.3:
  version "5.0.3"
  resolved "http://registry.npm.qima-inc.com/optimize-css-assets-webpack-plugin/download/optimize-css-assets-webpack-plugin-5.0.3.tgz#e2f1d4d94ad8c0af8967ebd7cf138dcb1ef14572"
  integrity sha1-4vHU2UrYwK+JZ+vXzxONyx7xRXI=
  dependencies:
    cssnano "^4.1.10"
    last-call-webpack-plugin "^3.0.0"

optionator@^0.8.1:
  version "0.8.3"
  resolved "http://registry.npm.qima-inc.com/optionator/download/optionator-0.8.3.tgz?cache=0&sync_timestamp=1573078228088&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Foptionator%2Fdownload%2Foptionator-0.8.3.tgz#84fa1d036fe9d3c7e21d99884b601167ec8fb495"
  integrity sha1-hPodA2/p08fiHZmIS2ARZ+yPtJU=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.6"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    word-wrap "~1.2.3"

optionator@^0.8.2:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/optionator/download/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  integrity sha1-NkxeQJ0/TWMB1sC0wFu6UBgK62Q=
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

ora@^3.4.0:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/ora/download/ora-3.4.0.tgz#bf0752491059a3ef3ed4c85097531de9fdbcd318"
  integrity sha1-vwdSSRBZo+8+1MhQl1Md6f280xg=
  dependencies:
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-spinners "^2.0.0"
    log-symbols "^2.2.0"
    strip-ansi "^5.2.0"
    wcwidth "^1.0.1"

os-browserify@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/os-browserify/download/os-browserify-0.3.0.tgz#854373c7f5c2315914fc9bfc6bd8238fdda1ec27"
  integrity sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=

os-name@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/os-name/download/os-name-1.0.3.tgz#1b379f64835af7c5a7f498b357cb95215c159edf"
  integrity sha1-GzefZINa98Wn9JizV8uVIVwVnt8=
  dependencies:
    osx-release "^1.0.0"
    win-release "^1.0.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

osx-release@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/osx-release/download/osx-release-1.1.0.tgz#f217911a28136949af1bf9308b241e2737d3cd6c"
  integrity sha1-8heRGigTaUmvG/kwiyQeJzfTzWw=
  dependencies:
    minimist "^1.1.0"

p-defer@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/p-defer/download/p-defer-3.0.0.tgz#d1dceb4ee9b2b604b1d94ffec83760175d4e6f83"
  integrity sha1-0dzrTumytgSx2U/+yDdgF11Ob4M=

p-each-series@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/p-each-series/download/p-each-series-2.1.0.tgz#961c8dd3f195ea96c747e636b262b800a6b1af48"
  integrity sha1-lhyN0/GV6pbHR+Y2smK4AKaxr0g=

p-finally@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-finally/download/p-finally-1.0.0.tgz#3fbcfb15b899a44123b34b6dcc18b724336a2cae"
  integrity sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=

p-finally@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/p-finally/download/p-finally-2.0.1.tgz#bd6fcaa9c559a096b680806f4d657b3f0f240561"
  integrity sha1-vW/KqcVZoJa2gIBvTWV7Pw8kBWE=

p-limit@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-1.3.0.tgz#b86bd5f0c25690911c7590fcbfc2010d54b3ccb8"
  integrity sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=
  dependencies:
    p-try "^1.0.0"

p-limit@^2.0.0, p-limit@^2.2.0:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/p-limit/download/p-limit-2.2.1.tgz#aa07a788cc3151c939b5131f63570f0dd2009537"
  integrity sha1-qgeniMwxUck5tRMfY1cPDdIAlTc=
  dependencies:
    p-try "^2.0.0"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  integrity sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=
  dependencies:
    p-limit "^1.1.0"

p-locate@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-3.0.0.tgz#322d69a05c0264b25997d9f40cd8a891ab0064a4"
  integrity sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=
  dependencies:
    p-limit "^2.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/p-locate/download/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-map@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-1.2.0.tgz#e4e94f311eabbc8633a1e79908165fca26241b6b"
  integrity sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=

p-map@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/p-map/download/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-try@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-1.0.0.tgz#cbc79cdbaf8fd4228e13f621f2b1a237c1b207b3"
  integrity sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=

p-try@^2.0.0, p-try@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/p-try/download/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

pako@~1.0.5:
  version "1.0.10"
  resolved "http://registry.npm.qima-inc.com/pako/download/pako-1.0.10.tgz#4328badb5086a426aa90f541977d4955da5c9732"
  integrity sha1-Qyi621CGpCaqkPVBl31JVdpclzI=

parallel-transform@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/parallel-transform/download/parallel-transform-1.2.0.tgz#9049ca37d6cb2182c3b1d2c720be94d14a5814fc"
  integrity sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=
  dependencies:
    cyclist "^1.0.1"
    inherits "^2.0.3"
    readable-stream "^2.1.5"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-asn1@^5.0.0:
  version "5.1.4"
  resolved "http://registry.npm.qima-inc.com/parse-asn1/download/parse-asn1-5.1.4.tgz#37f6628f823fbdeb2273b4d540434a22f3ef1fcc"
  integrity sha1-N/Zij4I/vesic7TVQENKIvPvH8w=
  dependencies:
    asn1.js "^4.0.0"
    browserify-aes "^1.0.0"
    create-hash "^1.1.0"
    evp_bytestokey "^1.0.0"
    pbkdf2 "^3.0.3"
    safe-buffer "^5.1.1"

parse-json@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-4.0.0.tgz#be35f5425be1f7f6c747184f98a788cb99477ee0"
  integrity sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=
  dependencies:
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"

parse-json@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/parse-json/download/parse-json-5.0.0.tgz#73e5114c986d143efa3712d4ea24db9a4266f60f"
  integrity sha1-c+URTJhtFD76NxLU6iTbmkJm9g8=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-better-errors "^1.0.1"
    lines-and-columns "^1.1.6"

parse5@5.1.0:
  version "5.1.0"
  resolved "http://registry.npm.qima-inc.com/parse5/download/parse5-5.1.0.tgz#c59341c9723f414c452975564c7c00a68d58acd2"
  integrity sha1-xZNByXI/QUxFKXVWTHwApo1YrNI=

parseurl@^1.3.2, parseurl@~1.3.3:
  version "1.3.3"
  resolved "http://registry.npm.qima-inc.com/parseurl/download/parseurl-1.3.3.tgz#9da19e7bee8d12dff0513ed5b76957793bc2e8d4"
  integrity sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=

pascalcase@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/pascalcase/download/pascalcase-0.1.1.tgz#b363e55e8006ca6fe21784d2db22bd15d7917f14"
  integrity sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=

path-browserify@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.qima-inc.com/path-browserify/download/path-browserify-0.0.1.tgz#e6c4ddd7ed3aa27c68a20cc4e50e1a4ee83bbc4a"
  integrity sha1-5sTd1+06onxoogzE5Q4aTug7vEo=

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"
  integrity sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=

path-exists@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"
  integrity sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@1.0.1, path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"
  integrity sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=

path-key@^2.0.0, path-key@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-2.0.1.tgz#411cadb574c5a140d3a4b1910d40d80cc9f40b40"
  integrity sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.qima-inc.com/path-parse/download/path-parse-1.0.6.tgz#d62dbb5679405d72c4737ec58600e9ddcf06d24c"
  integrity sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"
  integrity sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=

path-to-regexp@^1.2.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/path-to-regexp/download/path-to-regexp-1.7.0.tgz#59fde0f435badacba103a84e9d3bc64e96b9937d"
  integrity sha1-Wf3g9DW62suhA6hOnTvGTpa5k30=
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pause-stream@~0.0.11:
  version "0.0.11"
  resolved "http://registry.npm.qima-inc.com/pause-stream/download/pause-stream-0.0.11.tgz#fe5a34b0cbce12b5aa6a2b403ee2e73b602f1445"
  integrity sha1-/lo0sMvOErWqaitAPuLnO2AvFEU=
  dependencies:
    through "~2.3"

pbkdf2@^3.0.3:
  version "3.0.17"
  resolved "http://registry.npm.qima-inc.com/pbkdf2/download/pbkdf2-3.0.17.tgz#976c206530617b14ebb32114239f7b09336e93a6"
  integrity sha1-l2wgZTBhexTrsyEUI597CTNuk6Y=
  dependencies:
    create-hash "^1.1.2"
    create-hmac "^1.1.4"
    ripemd160 "^2.0.1"
    safe-buffer "^5.0.1"
    sha.js "^2.4.8"

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picomatch@^2.0.4, picomatch@^2.0.5:
  version "2.0.7"
  resolved "http://registry.npm.qima-inc.com/picomatch/download/picomatch-2.0.7.tgz#514169d8c7cd0bdbeecc8a2609e34a7163de69f6"
  integrity sha1-UUFp2MfNC9vuzIomCeNKcWPeafY=

picomatch@^2.2.1:
  version "2.2.2"
  resolved "http://registry.npm.qima-inc.com/picomatch/download/picomatch-2.2.2.tgz#21f333e9b6b8eaff02468f5146ea406d345f4dad"
  integrity sha1-IfMz6ba46v8CRo9RRupAbTRfTa0=

pify@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-3.0.0.tgz#e5a4acd2c101fdf3d9a4d07f0dbc4db49dd28176"
  integrity sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  integrity sha1-ITXW36ejWMBprJsXh3YogihFD/o=
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.qima-inc.com/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"
  integrity sha1-clVrgM+g1IqXToDnckjoDtT3+HA=

pirates@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/pirates/download/pirates-4.0.1.tgz#643a92caf894566f91b2b986d2c66950a8e2fb87"
  integrity sha1-ZDqSyviUVm+RsrmG0sZpUKji+4c=
  dependencies:
    node-modules-regexp "^1.0.0"

pkg-conf@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/pkg-conf/download/pkg-conf-2.1.0.tgz#2126514ca6f2abfebd168596df18ba57867f0058"
  integrity sha1-ISZRTKbyq/69FoWW3xi6V4Z/AFg=
  dependencies:
    find-up "^2.0.0"
    load-json-file "^4.0.0"

pkg-dir@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-3.0.0.tgz#2749020f239ed990881b1f71210d51eb6523bea3"
  integrity sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=
  dependencies:
    find-up "^3.0.0"

pkg-dir@^4.1.0, pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/pkg-dir/download/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

please-upgrade-node@^3.0.2, please-upgrade-node@^3.1.1:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/please-upgrade-node/download/please-upgrade-node-3.2.0.tgz#aeddd3f994c933e4ad98b99d9a556efa0e2fe942"
  integrity sha1-rt3T+ZTJM+StmLmdmlVu+g4v6UI=
  dependencies:
    semver-compare "^1.0.0"

pn@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/pn/download/pn-1.1.0.tgz#e2f4cef0e219f463c179ab37463e4e1ecdccbafb"
  integrity sha1-4vTO8OIZ9GPBeas3Rj5OHs3Muvs=

posix-character-classes@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/posix-character-classes/download/posix-character-classes-0.1.1.tgz#01eac0fe3b5af71a2a6c02feabb8c1fef7e00eab"
  integrity sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=

postcss-calc@^7.0.1:
  version "7.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-calc/download/postcss-calc-7.0.1.tgz#36d77bab023b0ecbb9789d84dcb23c4941145436"
  integrity sha1-Ntd7qwI7Dsu5eJ2E3LI8SUEUVDY=
  dependencies:
    css-unit-converter "^1.1.1"
    postcss "^7.0.5"
    postcss-selector-parser "^5.0.0-rc.4"
    postcss-value-parser "^3.3.1"

postcss-colormin@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/postcss-colormin/download/postcss-colormin-4.0.3.tgz#ae060bce93ed794ac71264f08132d550956bd381"
  integrity sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=
  dependencies:
    browserslist "^4.0.0"
    color "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-convert-values@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz#ca3813ed4da0f812f9d43703584e449ebe189a7f"
  integrity sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-discard-comments@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz#1fbabd2c246bff6aaad7997b2b0918f4d7af4033"
  integrity sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=
  dependencies:
    postcss "^7.0.0"

postcss-discard-duplicates@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz#3fe133cd3c82282e550fc9b239176a9207b784eb"
  integrity sha1-P+EzzTyCKC5VD8myORdqkge3hOs=
  dependencies:
    postcss "^7.0.0"

postcss-discard-empty@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz#c8c951e9f73ed9428019458444a02ad90bb9f765"
  integrity sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=
  dependencies:
    postcss "^7.0.0"

postcss-discard-overridden@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz#652aef8a96726f029f5e3e00146ee7a4e755ff57"
  integrity sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=
  dependencies:
    postcss "^7.0.0"

postcss-merge-longhand@^4.0.11:
  version "4.0.11"
  resolved "http://registry.npm.qima-inc.com/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz#62f49a13e4a0ee04e7b98f42bb16062ca2549e24"
  integrity sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=
  dependencies:
    css-color-names "0.0.4"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    stylehacks "^4.0.0"

postcss-merge-rules@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz#362bea4ff5a1f98e4075a713c6cb25aefef9a650"
  integrity sha1-NivqT/Wh+Y5AdacTxsslrv75plA=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    cssnano-util-same-parent "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"
    vendors "^1.0.0"

postcss-minify-font-values@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz#cd4c344cce474343fac5d82206ab2cbcb8afd5a6"
  integrity sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-gradients@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz#93b29c2ff5099c535eecda56c4aa6e665a663471"
  integrity sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    is-color-stop "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-minify-params@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz#6b9cef030c11e35261f95f618c90036d680db874"
  integrity sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=
  dependencies:
    alphanum-sort "^1.0.0"
    browserslist "^4.0.0"
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    uniqs "^2.0.0"

postcss-minify-selectors@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz#e2e5eb40bfee500d0cd9243500f5f8ea4262fbd8"
  integrity sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=
  dependencies:
    alphanum-sort "^1.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

postcss-modules-extract-imports@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/postcss-modules-extract-imports/download/postcss-modules-extract-imports-2.0.0.tgz#818719a1ae1da325f9832446b01136eeb493cd7e"
  integrity sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=
  dependencies:
    postcss "^7.0.5"

postcss-modules-local-by-default@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-modules-local-by-default/download/postcss-modules-local-by-default-3.0.2.tgz#e8a6561be914aaf3c052876377524ca90dbb7915"
  integrity sha1-6KZWG+kUqvPAUodjd1JMqQ27eRU=
  dependencies:
    icss-utils "^4.1.1"
    postcss "^7.0.16"
    postcss-selector-parser "^6.0.2"
    postcss-value-parser "^4.0.0"

postcss-modules-scope@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/postcss-modules-scope/download/postcss-modules-scope-2.1.0.tgz#ad3f5bf7856114f6fcab901b0502e2a2bc39d4eb"
  integrity sha1-rT9b94VhFPb8q5AbBQLiorw51Os=
  dependencies:
    postcss "^7.0.6"
    postcss-selector-parser "^6.0.0"

postcss-modules-values@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/postcss-modules-values/download/postcss-modules-values-3.0.0.tgz#5b5000d6ebae29b4255301b4a3a54574423e7f10"
  integrity sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=
  dependencies:
    icss-utils "^4.0.0"
    postcss "^7.0.6"

postcss-normalize-charset@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz#8b35add3aee83a136b0471e0d59be58a50285dd4"
  integrity sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=
  dependencies:
    postcss "^7.0.0"

postcss-normalize-display-values@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz#0dbe04a4ce9063d4667ed2be476bb830c825935a"
  integrity sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-positions@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz#05f757f84f260437378368a91f8932d4b102917f"
  integrity sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-repeat-style@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz#c4ebbc289f3991a028d44751cbdd11918b17910c"
  integrity sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-string@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz#cd44c40ab07a0c7a36dc5e99aace1eca4ec2690c"
  integrity sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=
  dependencies:
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-timing-functions@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz#8e009ca2a3949cdaf8ad23e6b6ab99cb5e7d28d9"
  integrity sha1-jgCcoqOUnNr4rSPmtquZy159KNk=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-unicode@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz#841bd48fdcf3019ad4baa7493a3d363b52ae1cfb"
  integrity sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-url@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz#10e437f86bc7c7e58f7b9652ed878daaa95faae1"
  integrity sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-normalize-whitespace@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz#bf1d4070fe4fcea87d1348e825d8cc0c5faa7d82"
  integrity sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=
  dependencies:
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-ordered-values@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz#0cf75c820ec7d5c4d280189559e0b571ebac0eee"
  integrity sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=
  dependencies:
    cssnano-util-get-arguments "^4.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-reduce-initial@^4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz#7fd42ebea5e9c814609639e2c2e84ae270ba48df"
  integrity sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=
  dependencies:
    browserslist "^4.0.0"
    caniuse-api "^3.0.0"
    has "^1.0.0"
    postcss "^7.0.0"

postcss-reduce-transforms@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz#17efa405eacc6e07be3414a5ca2d1074681d4e29"
  integrity sha1-F++kBerMbge+NBSlyi0QdGgdTik=
  dependencies:
    cssnano-util-get-match "^4.0.0"
    has "^1.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"

postcss-selector-parser@^3.0.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/postcss-selector-parser/download/postcss-selector-parser-3.1.1.tgz#4f875f4afb0c96573d5cf4d74011aee250a7e865"
  integrity sha1-T4dfSvsMllc9XPTXQBGu4lCn6GU=
  dependencies:
    dot-prop "^4.1.1"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^5.0.0-rc.4:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/postcss-selector-parser/download/postcss-selector-parser-5.0.0.tgz#249044356697b33b64f1a8f7c80922dddee7195c"
  integrity sha1-JJBENWaXsztk8aj3yAki3d7nGVw=
  dependencies:
    cssesc "^2.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^6.0.0, postcss-selector-parser@^6.0.2:
  version "6.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-selector-parser/download/postcss-selector-parser-6.0.2.tgz#934cf799d016c83411859e09dcecade01286ec5c"
  integrity sha1-k0z3mdAWyDQRhZ4J3Oyt4BKG7Fw=
  dependencies:
    cssesc "^3.0.0"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-svgo@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-svgo/download/postcss-svgo-4.0.2.tgz#17b997bc711b333bab143aaed3b8d3d6e3d38258"
  integrity sha1-F7mXvHEbMzurFDqu07jT1uPTglg=
  dependencies:
    is-svg "^3.0.0"
    postcss "^7.0.0"
    postcss-value-parser "^3.0.0"
    svgo "^1.0.0"

postcss-unique-selectors@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz#9446911f3289bfd64c6d680f073c03b1f9ee4bac"
  integrity sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=
  dependencies:
    alphanum-sort "^1.0.0"
    postcss "^7.0.0"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.0, postcss-value-parser@^3.3.1:
  version "3.3.1"
  resolved "http://registry.npm.qima-inc.com/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz#9ff822547e2893213cf1c30efa51ac5fd1ba8281"
  integrity sha1-n/giVH4okyE88cMO+lGsX9G6goE=

postcss-value-parser@^4.0.0:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/postcss-value-parser/download/postcss-value-parser-4.0.2.tgz#482282c09a42706d1fc9a069b73f44ec08391dc9"
  integrity sha1-SCKCwJpCcG0fyaBptz9E7Ag5Hck=

postcss@^7.0.0, postcss@^7.0.1, postcss@^7.0.14, postcss@^7.0.16, postcss@^7.0.17, postcss@^7.0.5, postcss@^7.0.6:
  version "7.0.18"
  resolved "http://registry.npm.qima-inc.com/postcss/download/postcss-7.0.18.tgz#4b9cda95ae6c069c67a4d933029eddd4838ac233"
  integrity sha1-S5zala5sBpxnpNkzAp7d1IOKwjM=
  dependencies:
    chalk "^2.4.2"
    source-map "^0.6.1"
    supports-color "^6.1.0"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"
  integrity sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=

prepend-http@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"
  integrity sha1-1PRWKwzjaW5BrFLQ4ALlemNdxtw=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^1.14.2:
  version "1.18.2"
  resolved "http://registry.npm.qima-inc.com/prettier/download/prettier-1.18.2.tgz#6823e7c5900017b4bd3acf46fe9ac4b4d7bda9ea"
  integrity sha1-aCPnxZAAF7S9Os9G/prEtNe9qeo=

pretty-format@^24.9.0:
  version "24.9.0"
  resolved "http://registry.npm.qima-inc.com/pretty-format/download/pretty-format-24.9.0.tgz#12fac31b37019a4eea3c11aa9a959eb7628aa7c9"
  integrity sha1-EvrDGzcBmk7qPBGqmpWet2KKp8k=
  dependencies:
    "@jest/types" "^24.9.0"
    ansi-regex "^4.0.0"
    ansi-styles "^3.2.0"
    react-is "^16.8.4"

pretty-format@^25.1.0:
  version "25.1.0"
  resolved "http://registry.npm.qima-inc.com/pretty-format/download/pretty-format-25.1.0.tgz#ed869bdaec1356fc5ae45de045e2c8ec7b07b0c8"
  integrity sha1-7Yab2uwTVvxa5F3gReLI7HsHsMg=
  dependencies:
    "@jest/types" "^25.1.0"
    ansi-regex "^5.0.0"
    ansi-styles "^4.0.0"
    react-is "^16.12.0"

private@^0.1.6:
  version "0.1.8"
  resolved "http://registry.npm.qima-inc.com/private/download/private-0.1.8.tgz#2381edb3689f7a53d653190060fcf822d2f368ff"
  integrity sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "http://registry.npm.qima-inc.com/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

progress@^2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.qima-inc.com/progress/download/progress-2.0.3.tgz#7e8cf8d8f5b8f239c1bc68beb4eb78567d572ef8"
  integrity sha1-foz42PW48jnBvGi+tOt4Vn1XLvg=

promise-inflight@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/promise-inflight/download/promise-inflight-1.0.1.tgz#98472870bf228132fcbdd868129bad12c3c029e3"
  integrity sha1-mEcocL8igTL8vdhoEputEsPAKeM=

promise.prototype.finally@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/promise.prototype.finally/download/promise.prototype.finally-3.1.1.tgz#cb279d3a5020ca6403b3d92357f8e22d50ed92aa"
  integrity sha1-yyedOlAgymQDs9kjV/jiLVDtkqo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.13.0"
    function-bind "^1.1.1"

prompts@^2.0.1:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/prompts/download/prompts-2.3.0.tgz#a444e968fa4cc7e86689a74050685ac8006c4cc4"
  integrity sha1-pETpaPpMx+hmiadAUGhayABsTMQ=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.3"

prop-types@^15.7.2:
  version "15.7.2"
  resolved "http://registry.npm.qima-inc.com/prop-types/download/prop-types-15.7.2.tgz#52c41e75b8c87e72b9d9360e0206b99dcbffa6c5"
  integrity sha1-UsQedbjIfnK52TYOAga5ncv/psU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.8.1"

property-expr@^1.5.0:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/property-expr/download/property-expr-1.5.1.tgz#22e8706894a0c8e28d58735804f6ba3a3673314f"
  integrity sha1-IuhwaJSgyOKNWHNYBPa6OjZzMU8=

proxy-addr@~2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/proxy-addr/download/proxy-addr-2.0.5.tgz#34cbd64a2d81f4b1fd21e76f9f06c8a45299ee34"
  integrity sha1-NMvWSi2B9LH9IedvnwbIpFKZ7jQ=
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.9.0"

prr@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

psl@^1.1.28:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/psl/download/psl-1.4.0.tgz?cache=0&sync_timestamp=1568040694709&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fpsl%2Fdownload%2Fpsl-1.4.0.tgz#5dd26156cdb69fa1fdb8ab1991667d3f80ced7c2"
  integrity sha1-XdJhVs22n6H9uKsZkWZ9P4DO18I=

public-encrypt@^4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/public-encrypt/download/public-encrypt-4.0.3.tgz#4fcc9d77a07e48ba7527e7cbe0de33d0701331e0"
  integrity sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=
  dependencies:
    bn.js "^4.1.0"
    browserify-rsa "^4.0.0"
    create-hash "^1.1.0"
    parse-asn1 "^5.0.0"
    randombytes "^2.0.1"
    safe-buffer "^5.1.2"

pump@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-2.0.1.tgz#12399add6e4cf7526d973cbc8b5ce2e2908b3909"
  integrity sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pump@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/pump/download/pump-3.0.0.tgz#b4a2116815bde2f4e1ea602354e8c75565107a64"
  integrity sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

pumpify@^1.3.3:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/pumpify/download/pumpify-1.5.1.tgz#36513be246ab27570b1a374a5ce278bfd74370ce"
  integrity sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=
  dependencies:
    duplexify "^3.6.0"
    inherits "^2.0.3"
    pump "^2.0.0"

punycode@1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^1.2.4:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"
  integrity sha1-wNWmOycYgArY4esPpSachN1BhF4=

punycode@^2.1.0, punycode@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/punycode/download/punycode-2.1.1.tgz#b58b010ac40c22c5657616c8d2c2c02c7bf479ec"
  integrity sha1-tYsBCsQMIsVldhbI0sLALHv0eew=

q@^1.1.2:
  version "1.5.1"
  resolved "http://registry.npm.qima-inc.com/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qiniu@^7.2.2:
  version "7.2.2"
  resolved "http://registry.npm.qima-inc.com/qiniu/download/qiniu-7.2.2.tgz#94425193f6b1dbd093fd78583070a64dde6cc5e7"
  integrity sha1-lEJRk/ax29CT/XhYMHCmTd5sxec=
  dependencies:
    agentkeepalive "3.3.0"
    crc32 "0.2.2"
    encodeurl "^1.0.1"
    formstream "1.1.0"
    mime "2.3.1"
    tunnel-agent "0.6.0"
    urllib "2.22.0"

qs@6.7.0:
  version "6.7.0"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.7.0.tgz#41dc1a015e3d581f1621776be31afb2876a9b1bc"
  integrity sha1-QdwaAV49WB8WIXdr4xr7KHapsbw=

qs@^6.4.0:
  version "6.8.0"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.8.0.tgz#87b763f0d37ca54200334cd57bb2ef8f68a1d081"
  integrity sha1-h7dj8NN8pUIAM0zVe7Lvj2ih0IE=

qs@~6.5.2:
  version "6.5.2"
  resolved "http://registry.npm.qima-inc.com/qs/download/qs-6.5.2.tgz?cache=0&sync_timestamp=1573195631718&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fqs%2Fdownload%2Fqs-6.5.2.tgz#cb3ae806e8740444584ef154ce8ee98d403f3e36"
  integrity sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=

query-string@^4.1.0:
  version "4.3.4"
  resolved "http://registry.npm.qima-inc.com/query-string/download/query-string-4.3.4.tgz#bbb693b9ca915c232515b228b1a02b609043dbeb"
  integrity sha1-u7aTucqRXCMlFbIosaArYJBD2+s=
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"
  integrity sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=

querystring@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

randombytes@^2.0.0, randombytes@^2.0.1, randombytes@^2.0.5:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

randomfill@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/randomfill/download/randomfill-1.0.4.tgz#c92196fc86ab42be983f1bf31778224931d61458"
  integrity sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=
  dependencies:
    randombytes "^2.0.5"
    safe-buffer "^5.1.0"

range-parser@~1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/range-parser/download/range-parser-1.2.1.tgz#3cf37023d199e1c24d1a55b84800c2f3e6468031"
  integrity sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=

raw-body@2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.qima-inc.com/raw-body/download/raw-body-2.4.0.tgz#a1ce6fb9c9bc356ca52e89256ab59059e13d0332"
  integrity sha1-oc5vucm8NWylLoklarWQWeE9AzI=
  dependencies:
    bytes "3.1.0"
    http-errors "1.7.2"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

react-is@^16.12.0, react-is@^16.8.4:
  version "16.13.0"
  resolved "http://registry.npm.qima-inc.com/react-is/download/react-is-16.13.0.tgz#0f37c3613c34fe6b37cd7f763a0d6293ab15c527"
  integrity sha1-DzfDYTw0/ms3zX92Og1ik6sVxSc=

react-is@^16.8.1:
  version "16.9.0"
  resolved "http://registry.npm.qima-inc.com/react-is/download/react-is-16.9.0.tgz#21ca9561399aad0ff1a7701c01683e8ca981edcb"
  integrity sha1-IcqVYTmarQ/xp3AcAWg+jKmB7cs=

read-chunk@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.qima-inc.com/read-chunk/download/read-chunk-3.2.0.tgz#2984afe78ca9bfbbdb74b19387bf9e86289c16ca"
  integrity sha1-KYSv54ypv7vbdLGTh7+ehiicFso=
  dependencies:
    pify "^4.0.1"
    with-open-file "^0.1.6"

read-pkg-up@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg-up/download/read-pkg-up-6.0.0.tgz#da75ce72762f2fa1f20c5a40d4dd80c77db969e3"
  integrity sha1-2nXOcnYvL6HyDFpA1N2Ax325aeM=
  dependencies:
    find-up "^4.0.0"
    read-pkg "^5.1.1"
    type-fest "^0.5.0"

read-pkg@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-4.0.1.tgz#963625378f3e1c4d48c85872b5a6ec7d5d093237"
  integrity sha1-ljYlN48+HE1IyFhytabsfV0JMjc=
  dependencies:
    normalize-package-data "^2.3.2"
    parse-json "^4.0.0"
    pify "^3.0.0"

read-pkg@^5.1.1:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/read-pkg/download/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.3.3, readable-stream@^2.3.6, readable-stream@~2.3.6:
  version "2.3.6"
  resolved "http://registry.npm.qima-inc.com/readable-stream/download/readable-stream-2.3.6.tgz#b11c27d88b8ff1fbe070643cf94b0c79ae1b0aaf"
  integrity sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-2.2.1.tgz#0e87622a3325aa33e892285caf8b4e846529a525"
  integrity sha1-DodiKjMlqjPokihcr4tOhGUppSU=
  dependencies:
    graceful-fs "^4.1.11"
    micromatch "^3.1.10"
    readable-stream "^2.0.2"

readdirp@^3.1.1:
  version "3.1.2"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-3.1.2.tgz#fa85d2d14d4289920e4671dead96431add2ee78a"
  integrity sha1-+oXS0U1CiZIORnHerZZDGt0u54o=
  dependencies:
    picomatch "^2.0.4"

readdirp@~3.4.0:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/readdirp/download/readdirp-3.4.0.tgz#9fdccdf9e9155805449221ac645e8303ab5b9ada"
  integrity sha1-n9zN+ekVWAVEkiGsZF6DA6tbmto=
  dependencies:
    picomatch "^2.2.1"

realpath-native@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/realpath-native/download/realpath-native-1.1.0.tgz#2003294fea23fb0672f2476ebe22fcf498a2d65c"
  integrity sha1-IAMpT+oj+wZy8kduviL89Jii1lw=
  dependencies:
    util.promisify "^1.0.0"

realpath-native@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/realpath-native/download/realpath-native-2.0.0.tgz#7377ac429b6e1fd599dc38d08ed942d0d7beb866"
  integrity sha1-c3esQptuH9WZ3DjQjtlC0Ne+uGY=

regenerate-unicode-properties@^8.1.0:
  version "8.1.0"
  resolved "http://registry.npm.qima-inc.com/regenerate-unicode-properties/download/regenerate-unicode-properties-8.1.0.tgz#ef51e0f0ea4ad424b77bf7cb41f3e015c70a3f0e"
  integrity sha1-71Hg8OpK1CS3e/fLQfPgFccKPw4=
  dependencies:
    regenerate "^1.4.0"

regenerate@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/regenerate/download/regenerate-1.4.0.tgz#4a856ec4b56e4077c557589cae85e7a4c8869a11"
  integrity sha1-SoVuxLVuQHfFV1icroXnpMiGmhE=

regenerator-runtime@^0.13.2:
  version "0.13.3"
  resolved "http://registry.npm.qima-inc.com/regenerator-runtime/download/regenerator-runtime-0.13.3.tgz#7cf6a77d8f5c6f60eb73c5fc1955b2ceb01e6bf5"
  integrity sha1-fPanfY9cb2Drc8X8GVWyzrAea/U=

regenerator-transform@^0.14.0:
  version "0.14.1"
  resolved "http://registry.npm.qima-inc.com/regenerator-transform/download/regenerator-transform-0.14.1.tgz#3b2fce4e1ab7732c08f665dfdb314749c7ddd2fb"
  integrity sha1-Oy/OThq3cywI9mXf2zFHScfd0vs=
  dependencies:
    private "^0.1.6"

regex-not@^1.0.0, regex-not@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/regex-not/download/regex-not-1.0.2.tgz#1f4ece27e00b0b65e0247a6810e6a85d83a5752c"
  integrity sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=
  dependencies:
    extend-shallow "^3.0.2"
    safe-regex "^1.1.0"

regexp-tree@^0.1.13:
  version "0.1.13"
  resolved "http://registry.npm.qima-inc.com/regexp-tree/download/regexp-tree-0.1.13.tgz#5b19ab9377edc68bc3679256840bb29afc158d7f"
  integrity sha1-Wxmrk3ftxovDZ5JWhAuymvwVjX8=

regexpp@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/regexpp/download/regexpp-2.0.1.tgz#8d19d31cf632482b589049f8281f93dbcba4d07f"
  integrity sha1-jRnTHPYySCtYkEn4KB+T28uk0H8=

regexpu-core@^4.5.4:
  version "4.5.5"
  resolved "http://registry.npm.qima-inc.com/regexpu-core/download/regexpu-core-4.5.5.tgz#aaffe61c2af58269b3e516b61a73790376326411"
  integrity sha1-qv/mHCr1gmmz5Ra2GnN5A3YyZBE=
  dependencies:
    regenerate "^1.4.0"
    regenerate-unicode-properties "^8.1.0"
    regjsgen "^0.5.0"
    regjsparser "^0.6.0"
    unicode-match-property-ecmascript "^1.0.4"
    unicode-match-property-value-ecmascript "^1.1.0"

regjsgen@^0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.qima-inc.com/regjsgen/download/regjsgen-0.5.0.tgz#a7634dc08f89209c2049adda3525711fb97265dd"
  integrity sha1-p2NNwI+JIJwgSa3aNSVxH7lyZd0=

regjsparser@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/regjsparser/download/regjsparser-0.6.0.tgz#f1e6ae8b7da2bae96c99399b868cd6c933a2ba9c"
  integrity sha1-8eaui32iuulsmTmbhozWyTOiupw=
  dependencies:
    jsesc "~0.5.0"

remove-trailing-separator@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz#c24bce2a283adad5bc3f58e0d48249b92379d8ef"
  integrity sha1-wkvOKig62tW8P1jg1IJJuSN52O8=

repeat-element@^1.1.2:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/repeat-element/download/repeat-element-1.1.3.tgz#782e0d825c0c5a3bb39731f84efee6b742e6b1ce"
  integrity sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=

repeat-string@^1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.qima-inc.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request-promise-core@1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/request-promise-core/download/request-promise-core-1.1.3.tgz#e9a3c081b51380dfea677336061fea879a829ee9"
  integrity sha1-6aPAgbUTgN/qZ3M2Bh/qh5qCnuk=
  dependencies:
    lodash "^4.17.15"

request-promise-native@^1.0.7:
  version "1.0.8"
  resolved "http://registry.npm.qima-inc.com/request-promise-native/download/request-promise-native-1.0.8.tgz?cache=0&sync_timestamp=1572829683581&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Frequest-promise-native%2Fdownload%2Frequest-promise-native-1.0.8.tgz#a455b960b826e44e2bf8999af64dff2bfe58cb36"
  integrity sha1-pFW5YLgm5E4r+Jma9k3/K/5YyzY=
  dependencies:
    request-promise-core "1.1.3"
    stealthy-require "^1.1.1"
    tough-cookie "^2.3.3"

request@2.88.2, request@^2.88.0:
  version "2.88.2"
  resolved "http://registry.npm.qima-inc.com/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-main-filename@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/require-main-filename/download/require-main-filename-2.0.0.tgz#d0b329ecc7cc0f61649f62215be69af54aa8989b"
  integrity sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=

require-package-name@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/require-package-name/download/require-package-name-2.0.1.tgz#c11e97276b65b8e2923f75dabf5fb2ef0c3841b9"
  integrity sha1-wR6XJ2tluOKSP3Xav1+y7ww4Qbk=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-cwd/download/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-path@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/resolve-path/download/resolve-path-1.4.0.tgz#c4bda9f5efb2fce65247873ab36bb4d834fe16f7"
  integrity sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=
  dependencies:
    http-errors "~1.6.2"
    path-is-absolute "1.0.1"

resolve-url@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.qima-inc.com/resolve-url/download/resolve-url-0.2.1.tgz#2c637fe77c893afd2a663fe21aa9080068e2052a"
  integrity sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=

resolve@1.1.7:
  version "1.1.7"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fresolve%2Fdownload%2Fresolve-1.1.7.tgz#203114d82ad2c5ed9e8e0411b3932875e889e97b"
  integrity sha1-IDEU2CrSxe2ejgQRs5ModeiJ6Xs=

resolve@1.x:
  version "1.15.1"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.15.1.tgz#27bdcdeffeaf2d6244b95bb0f9f4b4653451f3e8"
  integrity sha1-J73N7/6vLWJEuVuw+fS0ZTRR8+g=
  dependencies:
    path-parse "^1.0.6"

resolve@^1.10.0, resolve@^1.10.1, resolve@^1.12.0, resolve@^1.3.2, resolve@^1.8.1:
  version "1.12.0"
  resolved "http://registry.npm.qima-inc.com/resolve/download/resolve-1.12.0.tgz#3fc644a35c84a48554609ff26ec52b66fa577df6"
  integrity sha1-P8ZEo1yEpIVUYJ/ybsUrZvpXffY=
  dependencies:
    path-parse "^1.0.6"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

ret@~0.1.10:
  version "0.1.15"
  resolved "http://registry.npm.qima-inc.com/ret/download/ret-0.1.15.tgz#b8a4825d5bdb1fc3f6f53c2bc33f81388681c7bc"
  integrity sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=

reusify@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

rgb-regex@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/rgb-regex/download/rgb-regex-1.0.1.tgz#c0e0d6882df0e23be254a475e8edd41915feaeb1"
  integrity sha1-wODWiC3w4jviVKR16O3UGRX+rrE=

rgba-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/rgba-regex/download/rgba-regex-1.0.0.tgz#43374e2e2ca0968b0ef1523460b7d730ff22eeb3"
  integrity sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=

rimraf@2.6.3:
  version "2.6.3"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.6.3.tgz#b2d104fe0d8fb27cf9e0a1cda8262dd3833c6cab"
  integrity sha1-stEE/g2Psnz54KHNqCYt04M8bKs=
  dependencies:
    glob "^7.1.3"

rimraf@^2.2.8, rimraf@^2.5.4, rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

ripemd160@^2.0.0, ripemd160@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/ripemd160/download/ripemd160-2.0.2.tgz#a1c1a6f624751577ba5d07914cbc92850585890c"
  integrity sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=
  dependencies:
    hash-base "^3.0.0"
    inherits "^2.0.1"

rsvp@^4.8.4:
  version "4.8.5"
  resolved "http://registry.npm.qima-inc.com/rsvp/download/rsvp-4.8.5.tgz#c8f155311d167f68f21e168df71ec5b083113734"
  integrity sha1-yPFVMR0Wf2jyHhaN9x7FsIMRNzQ=

run-async@^2.2.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/run-async/download/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
  integrity sha1-A3GrSuC91yDUFm19/aZP96RFpsA=
  dependencies:
    is-promise "^2.1.0"

run-node@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/run-node/download/run-node-1.0.0.tgz#46b50b946a2aa2d4947ae1d886e9856fd9cabe5e"
  integrity sha1-RrULlGoqotSUeuHYhumFb9nKvl4=

run-parallel@^1.1.9:
  version "1.1.9"
  resolved "http://registry.npm.qima-inc.com/run-parallel/download/run-parallel-1.1.9.tgz#c9dd3a7cf9f4b2c4b6244e173a6ed866e61dd679"
  integrity sha1-yd06fPn0ssS2JE4XOm7YZuYd1nk=

run-queue@^1.0.0, run-queue@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/run-queue/download/run-queue-1.0.3.tgz#e848396f057d223f24386924618e25694161ec47"
  integrity sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=
  dependencies:
    aproba "^1.1.1"

rxjs@^6.3.3, rxjs@^6.4.0:
  version "6.5.3"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-6.5.3.tgz#510e26317f4db91a7eb1de77d9dd9ba0a4899a3a"
  integrity sha1-UQ4mMX9NuRp+sd532d2boKSJmjo=
  dependencies:
    tslib "^1.9.0"

rxjs@^6.5.5:
  version "6.5.5"
  resolved "http://registry.npm.qima-inc.com/rxjs/download/rxjs-6.5.5.tgz#c5c884e3094c8cfee31bf27eb87e54ccfc87f9ec"
  integrity sha1-xciE4wlMjP7jG/J+uH5UzPyH+ew=
  dependencies:
    tslib "^1.9.0"

safe-buffer@5.1.2, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.1, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/safe-buffer/download/safe-buffer-5.2.0.tgz#b74daec49b1148f88c64b68d49b1e815c1f2f519"
  integrity sha1-t02uxJsRSPiMZLaNSbHoFcHy9Rk=

safe-regex@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/safe-regex/download/safe-regex-1.1.0.tgz#40a3669f3b077d1e943d44629e157dd48023bf2e"
  integrity sha1-QKNmnzsHfR6UPURinhV91IAjvy4=
  dependencies:
    ret "~0.1.10"

"safer-buffer@>= 2.1.2 < 3", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sane@^4.0.3:
  version "4.1.0"
  resolved "http://registry.npm.qima-inc.com/sane/download/sane-4.1.0.tgz#ed881fd922733a6c461bc189dc2b6c006f3ffded"
  integrity sha1-7Ygf2SJzOmxGG8GJ3CtsAG8//e0=
  dependencies:
    "@cnakazawa/watch" "^1.0.3"
    anymatch "^2.0.0"
    capture-exit "^2.0.0"
    exec-sh "^0.3.2"
    execa "^1.0.0"
    fb-watchman "^2.0.0"
    micromatch "^3.1.4"
    minimist "^1.1.1"
    walker "~1.0.5"

sass-loader@^8.0.0:
  version "8.0.0"
  resolved "http://registry.npm.qima-inc.com/sass-loader/download/sass-loader-8.0.0.tgz#e7b07a3e357f965e6b03dd45b016b0a9746af797"
  integrity sha1-57B6PjV/ll5rA91FsBawqXRq95c=
  dependencies:
    clone-deep "^4.0.1"
    loader-utils "^1.2.3"
    neo-async "^2.6.1"
    schema-utils "^2.1.0"
    semver "^6.3.0"

sass@^1.22.5:
  version "1.22.10"
  resolved "http://registry.npm.qima-inc.com/sass/download/sass-1.22.10.tgz#b9f01440352ba0be5d99fa64a2040b035cc6e5ff"
  integrity sha1-ufAUQDUroL5dmfpkogQLA1zG5f8=
  dependencies:
    chokidar ">=2.0.0 <4.0.0"

sax@~1.2.4:
  version "1.2.4"
  resolved "http://registry.npm.qima-inc.com/sax/download/sax-1.2.4.tgz#2816234e2378bddc4e5354fab5caa895df7100d9"
  integrity sha1-KBYjTiN4vdxOU1T6tcqold9xANk=

saxes@^3.1.9:
  version "3.1.11"
  resolved "http://registry.npm.qima-inc.com/saxes/download/saxes-3.1.11.tgz#d59d1fd332ec92ad98a2e0b2ee644702384b1c5b"
  integrity sha1-1Z0f0zLskq2YouCy7mRHAjhLHFs=
  dependencies:
    xmlchars "^2.1.1"

schema-utils@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/schema-utils/download/schema-utils-1.0.0.tgz#0b79a93204d7b600d4b2850d1f66c2a34951c770"
  integrity sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=
  dependencies:
    ajv "^6.1.0"
    ajv-errors "^1.0.0"
    ajv-keywords "^3.1.0"

schema-utils@^2.0.0, schema-utils@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/schema-utils/download/schema-utils-2.2.0.tgz#48a065ce219e0cacf4631473159037b2c1ae82da"
  integrity sha1-SKBlziGeDKz0YxRzFZA3ssGugto=
  dependencies:
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"

semver-compare@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/semver-compare/download/semver-compare-1.0.0.tgz#0dee216a1c941ab37e9efb1788f6afc5ff5537fc"
  integrity sha1-De4hahyUGrN+nvsXiPavxf9VN/w=

"semver@2 || 3 || 4 || 5", semver@^5.0.1, semver@^5.3.0, semver@^5.4.1, semver@^5.5.0, semver@^5.5.1, semver@^5.6.0:
  version "5.7.1"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-5.7.1.tgz#a954f931aeba508d307bbf069eff0c01c96116f7"
  integrity sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=

semver@6.x, semver@^6.0.0, semver@^6.1.2, semver@^6.3.0:
  version "6.3.0"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-6.3.0.tgz#ee0a64c8af5e8ceea67687b133761e1becbd1d3d"
  integrity sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=

semver@^7.1.1:
  version "7.1.3"
  resolved "http://registry.npm.qima-inc.com/semver/download/semver-7.1.3.tgz#e4345ce73071c53f336445cfc19efb1c311df2a6"
  integrity sha1-5DRc5zBxxT8zZEXPwZ77HDEd8qY=

send@0.17.1:
  version "0.17.1"
  resolved "http://registry.npm.qima-inc.com/send/download/send-0.17.1.tgz#c1d8b059f7900f7466dd4938bdc44e11ddb376c8"
  integrity sha1-wdiwWfeQD3Rm3Uk4vcROEd2zdsg=
  dependencies:
    debug "2.6.9"
    depd "~1.1.2"
    destroy "~1.0.4"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.7.2"
    mime "1.6.0"
    ms "2.1.1"
    on-finished "~2.3.0"
    range-parser "~1.2.1"
    statuses "~1.5.0"

serialize-javascript@^1.7.0:
  version "1.9.1"
  resolved "http://registry.npm.qima-inc.com/serialize-javascript/download/serialize-javascript-1.9.1.tgz#cfc200aef77b600c47da9bb8149c943e798c2fdb"
  integrity sha1-z8IArvd7YAxH2pu4FJyUPnmML9s=

serialize-javascript@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.qima-inc.com/serialize-javascript/download/serialize-javascript-2.1.2.tgz#ecec53b0e0317bdc95ef76ab7074b7384785fa61"
  integrity sha1-7OxTsOAxe9yV73arcHS3OEeF+mE=

serve-static@1.14.1:
  version "1.14.1"
  resolved "http://registry.npm.qima-inc.com/serve-static/download/serve-static-1.14.1.tgz#666e636dc4f010f7ef29970a88a674320898b2f9"
  integrity sha1-Zm5jbcTwEPfvKZcKiKZ0MgiYsvk=
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.17.1"

set-blocking@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"
  integrity sha1-BF+XgtARrppoA93TgrJDkrPYkPc=

set-value@^2.0.0, set-value@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/set-value/download/set-value-2.0.1.tgz#a18d40530e6f07de4228c7defe4227af8cad005b"
  integrity sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=
  dependencies:
    extend-shallow "^2.0.1"
    is-extendable "^0.1.1"
    is-plain-object "^2.0.3"
    split-string "^3.0.1"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"
  integrity sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"
  integrity sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=

setprototypeof@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/setprototypeof/download/setprototypeof-1.1.1.tgz#7e95acb24aa92f5885e0abef5ba131330d4ae683"
  integrity sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM=

sha.js@^2.4.0, sha.js@^2.4.8:
  version "2.4.11"
  resolved "http://registry.npm.qima-inc.com/sha.js/download/sha.js-2.4.11.tgz#37a5cf0b81ecbc6943de109ba2960d1b26584ae7"
  integrity sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=
  dependencies:
    inherits "^2.0.1"
    safe-buffer "^5.0.1"

shallow-clone@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/shallow-clone/download/shallow-clone-3.0.1.tgz#8f2981ad92531f55035b01fb230769a40e02efa3"
  integrity sha1-jymBrZJTH1UDWwH7IwdppA4C76M=
  dependencies:
    kind-of "^6.0.2"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  integrity sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=
  dependencies:
    shebang-regex "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"
  integrity sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shellwords@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/shellwords/download/shellwords-0.1.1.tgz#d6b9181c1a48d397324c84871efbcfc73fc0654b"
  integrity sha1-1rkYHBpI05cyTISHHvvPxz/AZUs=

signal-exit@^3.0.0, signal-exit@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"
  integrity sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=

signale@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.qima-inc.com/signale/download/signale-1.4.0.tgz#c4be58302fb0262ac00fc3d886a7c113759042f1"
  integrity sha1-xL5YMC+wJirAD8PYhqfBE3WQQvE=
  dependencies:
    chalk "^2.3.2"
    figures "^2.0.0"
    pkg-conf "^2.1.0"

simple-git@^1.85.0:
  version "1.126.0"
  resolved "http://registry.npm.qima-inc.com/simple-git/download/simple-git-1.126.0.tgz#0c345372275139c8433b8277f4b3e155092aa434"
  integrity sha1-DDRTcidROchDO4J39LPhVQkqpDQ=
  dependencies:
    debug "^4.0.1"

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "http://registry.npm.qima-inc.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

sisteransi@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/sisteransi/download/sisteransi-1.0.4.tgz#386713f1ef688c7c0304dc4c0632898941cad2e3"
  integrity sha1-OGcT8e9ojHwDBNxMBjKJiUHK0uM=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"
  integrity sha1-7b+JA/ZvfOL46v1s7tZeJkyDGzU=

slice-ansi@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/slice-ansi/download/slice-ansi-2.1.0.tgz#cacd7693461a637a5788d92a7dd4fba068e81636"
  integrity sha1-ys12k0YaY3pXiNkqfdT7oGjoFjY=
  dependencies:
    ansi-styles "^3.2.0"
    astral-regex "^1.0.0"
    is-fullwidth-code-point "^2.0.0"

snapdragon-node@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-node/download/snapdragon-node-2.1.1.tgz#6c175f86ff14bdb0724563e8f3c1b021a286853b"
  integrity sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=
  dependencies:
    define-property "^1.0.0"
    isobject "^3.0.0"
    snapdragon-util "^3.0.1"

snapdragon-util@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/snapdragon-util/download/snapdragon-util-3.0.1.tgz#f956479486f2acd79700693f6f7b805e45ab56e2"
  integrity sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=
  dependencies:
    kind-of "^3.2.0"

snapdragon@^0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.qima-inc.com/snapdragon/download/snapdragon-0.8.2.tgz#64922e7c565b0e14204ba1aa7d6964278d25182d"
  integrity sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=
  dependencies:
    base "^0.11.1"
    debug "^2.2.0"
    define-property "^0.2.5"
    extend-shallow "^2.0.1"
    map-cache "^0.2.2"
    source-map "^0.5.6"
    source-map-resolve "^0.5.0"
    use "^3.1.0"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/sort-keys/download/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  integrity sha1-RBttTTRnmPG05J6JIK37oOVD+a0=
  dependencies:
    is-plain-obj "^1.0.0"

source-list-map@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.qima-inc.com/source-list-map/download/source-list-map-2.0.1.tgz#3993bd873bfc48479cca9ea3a547835c7c154b34"
  integrity sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=

source-map-resolve@^0.5.0:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/source-map-resolve/download/source-map-resolve-0.5.2.tgz#72e2cc34095543e43b2c62b2c4c10d4a9054f259"
  integrity sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=
  dependencies:
    atob "^2.1.1"
    decode-uri-component "^0.2.0"
    resolve-url "^0.2.1"
    source-map-url "^0.4.0"
    urix "^0.1.0"

source-map-support@^0.5.6:
  version "0.5.16"
  resolved "http://registry.npm.qima-inc.com/source-map-support/download/source-map-support-0.5.16.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npm.taobao.org%2Fsource-map-support%2Fdownload%2Fsource-map-support-0.5.16.tgz#0ae069e7fe3ba7538c64c98515e35339eac5a042"
  integrity sha1-CuBp5/47p1OMZMmFFeNTOerFoEI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-support@~0.5.12:
  version "0.5.13"
  resolved "http://registry.npm.qima-inc.com/source-map-support/download/source-map-support-0.5.13.tgz#31b24a9c2e73c2de85066c0feb7d44767ed52932"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map-url@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.qima-inc.com/source-map-url/download/source-map-url-0.4.0.tgz#3e935d7ddd73631b97659956d55128e87b5084a3"
  integrity sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6:
  version "0.5.7"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@^0.7.3:
  version "0.7.3"
  resolved "http://registry.npm.qima-inc.com/source-map/download/source-map-0.7.3.tgz#5302f8169031735226544092e64981f751750383"
  integrity sha1-UwL4FpAxc1ImVECS5kmB91F1A4M=

spdx-correct@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/spdx-correct/download/spdx-correct-3.1.0.tgz#fb83e504445268f154b074e218c87c003cd31df4"
  integrity sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz#2ea450aee74f2a89bfb94519c07fcd6f41322977"
  integrity sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=

spdx-expression-parse@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz#99e119b7a5da00e05491c9fa338b7904823b41d0"
  integrity sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.5"
  resolved "http://registry.npm.qima-inc.com/spdx-license-ids/download/spdx-license-ids-3.0.5.tgz#3694b5804567a458d3c8045842a6358632f62654"
  integrity sha1-NpS1gEVnpFjTyARYQqY1hjL2JlQ=

speed-measure-webpack-plugin@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/speed-measure-webpack-plugin/download/speed-measure-webpack-plugin-1.3.1.tgz#69840a5cdc08b4638697dac7db037f595d7f36a0"
  integrity sha1-aYQKXNwItGOGl9rH2wN/WV1/NqA=
  dependencies:
    chalk "^2.0.1"

split-string@^3.0.1, split-string@^3.0.2:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/split-string/download/split-string-3.1.0.tgz#7cb09dda3a86585705c64b39a6466038682e8fe2"
  integrity sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=
  dependencies:
    extend-shallow "^3.0.0"

split@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/split/download/split-1.0.1.tgz#605bd9be303aa59fb35f9229fbea0ddec9ea07d9"
  integrity sha1-YFvZvjA6pZ+zX5Ip++oN3snqB9k=
  dependencies:
    through "2"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.16.1"
  resolved "http://registry.npm.qima-inc.com/sshpk/download/sshpk-1.16.1.tgz#fb661c0bef29b39db40769ee39fa70093d6f6877"
  integrity sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssri@^6.0.1:
  version "6.0.1"
  resolved "http://registry.npm.qima-inc.com/ssri/download/ssri-6.0.1.tgz#2a3c41b28dd45b62b63676ecb74001265ae9edd8"
  integrity sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=
  dependencies:
    figgy-pudding "^3.5.1"

stable@^0.1.8:
  version "0.1.8"
  resolved "http://registry.npm.qima-inc.com/stable/download/stable-0.1.8.tgz#836eb3c8382fe2936feaf544631017ce7d47a3cf"
  integrity sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=

stack-utils@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/stack-utils/download/stack-utils-1.0.2.tgz#33eba3897788558bebfc2db059dc158ec36cebb8"
  integrity sha1-M+ujiXeIVYvr/C2wWdwVjsNs67g=

staged-git-files@1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/staged-git-files/download/staged-git-files-1.1.2.tgz#4326d33886dc9ecfa29a6193bf511ba90a46454b"
  integrity sha1-QybTOIbcns+immGTv1EbqQpGRUs=

static-extend@^0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.qima-inc.com/static-extend/download/static-extend-0.1.2.tgz#60809c39cbff55337226fd5e0b520f341f1fb5c6"
  integrity sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=
  dependencies:
    define-property "^0.2.5"
    object-copy "^0.1.0"

"statuses@>= 1.4.0 < 2", "statuses@>= 1.5.0 < 2", statuses@^1.0.0, statuses@^1.3.1, statuses@^1.5.0, statuses@~1.5.0:
  version "1.5.0"
  resolved "http://registry.npm.qima-inc.com/statuses/download/statuses-1.5.0.tgz#161c7dac177659fd9811f43771fa99381478628c"
  integrity sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=

std-env@^1.1.0:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/std-env/download/std-env-1.3.1.tgz#4e1758412439e9ece1d437b1b098551911aa44ee"
  integrity sha1-ThdYQSQ56ezh1DexsJhVGRGqRO4=
  dependencies:
    is-ci "^1.1.0"

stealthy-require@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/stealthy-require/download/stealthy-require-1.1.1.tgz#35b09875b4ff49f26a777e509b3090a3226bf24b"
  integrity sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=

stream-browserify@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/stream-browserify/download/stream-browserify-2.0.2.tgz#87521d38a44aa7ee91ce1cd2a47df0cb49dd660b"
  integrity sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-each@^1.1.0:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/stream-each/download/stream-each-1.2.3.tgz#ebe27a0c389b04fbcc233642952e10731afa9bae"
  integrity sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=
  dependencies:
    end-of-stream "^1.1.0"
    stream-shift "^1.0.0"

stream-http@^2.7.2:
  version "2.8.3"
  resolved "http://registry.npm.qima-inc.com/stream-http/download/stream-http-2.8.3.tgz#b2d242469288a5a27ec4fe8933acf623de6514fc"
  integrity sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.3.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/stream-shift/download/stream-shift-1.0.0.tgz#d5c752825e5367e786f78e18e445ea223a155952"
  integrity sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI=

streamifier@0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.qima-inc.com/streamifier/download/streamifier-0.1.1.tgz#97e98d8fa4d105d62a2691d1dc07e820db8dfc4f"
  integrity sha1-l+mNj6TRBdYqJpHR3AfoINuN/E8=

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"
  integrity sha1-J5siXfHVgrH1TmWt3UNS4Y+qBxM=

string-argv@^0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.qima-inc.com/string-argv/download/string-argv-0.0.2.tgz#dac30408690c21f3c3630a3ff3a05877bdcbd736"
  integrity sha1-2sMECGkMIfPDYwo/86BYd73L1zY=

string-length@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/string-length/download/string-length-3.1.0.tgz#107ef8c23456e187a8abd4a61162ff4ac6e25837"
  integrity sha1-EH74wjRW4Yeoq9SmEWL/SsbiWDc=
  dependencies:
    astral-regex "^1.0.0"
    strip-ansi "^5.2.0"

string-width@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  integrity sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-3.1.0.tgz#22767be21b62af1081574306f69ac51b62203961"
  integrity sha1-InZ74htirxCBV0MG9prFG2IgOWE=
  dependencies:
    emoji-regex "^7.0.1"
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^5.1.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.qima-inc.com/string-width/download/string-width-4.2.0.tgz#952182c46cc7b2c313d1596e623992bd163b72b5"
  integrity sha1-lSGCxGzHssMT0VluYjmSvRY7crU=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.0"

string.prototype.trimleft@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trimleft/download/string.prototype.trimleft-2.0.0.tgz#68b6aa8e162c6a80e76e3a8a0c2e747186e271ff"
  integrity sha1-aLaqjhYsaoDnbjqKDC50cYbicf8=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.0.2"

string.prototype.trimright@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/string.prototype.trimright/download/string.prototype.trimright-2.0.0.tgz#ab4a56d802a01fbe7293e11e84f24dc8164661dd"
  integrity sha1-q0pW2AKgH75yk+EehPJNyBZGYd0=
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.0.2"

string_decoder@^1.0.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/string_decoder/download/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

stringify-object@^3.2.2:
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/stringify-object/download/stringify-object-3.3.0.tgz#703065aefca19300d3ce88af4f5b3956d7556629"
  integrity sha1-cDBlrvyhkwDTzoivT1s5VtdVZik=
  dependencies:
    get-own-enumerable-property-symbols "^3.0.0"
    is-obj "^1.0.1"
    is-regexp "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.0.0, strip-ansi@^5.1.0, strip-ansi@^5.2.0:
  version "5.2.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-ansi/download/strip-ansi-6.0.0.tgz#0b1571dd7669ccd4f3e06e14ef1eed26225ae532"
  integrity sha1-CxVx3XZpzNTz4G4U7x7tJiJa5TI=
  dependencies:
    ansi-regex "^5.0.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-bom/download/strip-bom-4.0.0.tgz#9c3505c1db45bcedca3d9cf7a16f5c5aa3901878"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-eof@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-eof/download/strip-eof-1.0.0.tgz#bb43ff5598a6eb05d89b59fcd129c983313606bf"
  integrity sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/strip-final-newline/download/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-json-comments@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/strip-json-comments/download/strip-json-comments-3.0.1.tgz#85713975a91fb87bf1b305cca77395e40d2a64a7"
  integrity sha1-hXE5dakfuHvxswXMp3OV5A0qZKc=

stylehacks@^4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.qima-inc.com/stylehacks/download/stylehacks-4.0.3.tgz#6718fcaf4d1e07d8a1318690881e8d96726a71d5"
  integrity sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=
  dependencies:
    browserslist "^4.0.0"
    postcss "^7.0.0"
    postcss-selector-parser "^3.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^6.1.0:
  version "6.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-6.1.0.tgz#0764abc69c63d5ac842dd4867e8d025e880df8f3"
  integrity sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-color/download/supports-color-7.1.0.tgz#68e32591df73e25ad1c4b49108a2ec507962bfd1"
  integrity sha1-aOMlkd9z4lrRxLSRCKLsUHliv9E=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/supports-hyperlinks/download/supports-hyperlinks-2.1.0.tgz#f663df252af5f37c5d49bbd7eeefa9e0b9e59e47"
  integrity sha1-9mPfJSr183xdSbvX7u+p4Lnlnkc=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

svgo@^1.0.0:
  version "1.3.0"
  resolved "http://registry.npm.qima-inc.com/svgo/download/svgo-1.3.0.tgz#bae51ba95ded9a33a36b7c46ce9c359ae9154313"
  integrity sha1-uuUbqV3tmjOja3xGzpw1mukVQxM=
  dependencies:
    chalk "^2.4.1"
    coa "^2.0.2"
    css-select "^2.0.0"
    css-select-base-adapter "^0.1.1"
    css-tree "1.0.0-alpha.33"
    csso "^3.5.1"
    js-yaml "^3.13.1"
    mkdirp "~0.5.1"
    object.values "^1.1.0"
    sax "~1.2.4"
    stable "^0.1.8"
    unquote "~1.1.1"
    util.promisify "~1.0.0"

symbol-observable@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/symbol-observable/download/symbol-observable-1.2.0.tgz#c22688aed4eab3cdc2dfeacbb561660560a00804"
  integrity sha1-wiaIrtTqs83C3+rLtWFmBWCgCAQ=

symbol-tree@^3.2.2:
  version "3.2.4"
  resolved "http://registry.npm.qima-inc.com/symbol-tree/download/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

synchronous-promise@^2.0.6:
  version "2.0.10"
  resolved "http://registry.npm.qima-inc.com/synchronous-promise/download/synchronous-promise-2.0.10.tgz#e64c6fd3afd25f423963353043f4a68ebd397fd8"
  integrity sha1-5kxv06/SX0I5YzUwQ/Smjr05f9g=

table@^5.2.3:
  version "5.4.6"
  resolved "http://registry.npm.qima-inc.com/table/download/table-5.4.6.tgz#1292d19500ce3f86053b05f0e8e7e4a3bb21079e"
  integrity sha1-EpLRlQDOP4YFOwXw6Ofko7shB54=
  dependencies:
    ajv "^6.10.2"
    lodash "^4.17.14"
    slice-ansi "^2.1.0"
    string-width "^3.0.0"

tapable@^1.0.0, tapable@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.qima-inc.com/tapable/download/tapable-1.1.3.tgz#a1fccc06b58db61fd7a45da2da44f5f3a3e67ba2"
  integrity sha1-ofzMBrWNth/XpF2i2kT186Pme6I=

terminal-link@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/terminal-link/download/terminal-link-2.1.1.tgz#14a64a27ab3c0df933ea546fba55f2d078edc994"
  integrity sha1-FKZKJ6s8Dfkz6lRvulXy0HjtyZQ=
  dependencies:
    ansi-escapes "^4.2.1"
    supports-hyperlinks "^2.0.0"

terser-webpack-plugin@^1.3.0:
  version "1.4.1"
  resolved "http://registry.npm.qima-inc.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.1.tgz#61b18e40eaee5be97e771cdbb10ed1280888c2b4"
  integrity sha1-YbGOQOruW+l+dxzbsQ7RKAiIwrQ=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^1.7.0"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser-webpack-plugin@^1.4.3:
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/terser-webpack-plugin/download/terser-webpack-plugin-1.4.3.tgz#5ecaf2dbdc5fb99745fd06791f46fc9ddb1c9a7c"
  integrity sha1-Xsry29xfuZdF/QZ5H0b8ndscmnw=
  dependencies:
    cacache "^12.0.2"
    find-cache-dir "^2.1.0"
    is-wsl "^1.1.0"
    schema-utils "^1.0.0"
    serialize-javascript "^2.1.2"
    source-map "^0.6.1"
    terser "^4.1.2"
    webpack-sources "^1.4.0"
    worker-farm "^1.7.0"

terser@^4.1.2:
  version "4.3.1"
  resolved "http://registry.npm.qima-inc.com/terser/download/terser-4.3.1.tgz#09820bcb3398299c4b48d9a86aefc65127d0ed65"
  integrity sha1-CYILyzOYKZxLSNmoau/GUSfQ7WU=
  dependencies:
    commander "^2.20.0"
    source-map "~0.6.1"
    source-map-support "~0.5.12"

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "http://registry.npm.qima-inc.com/test-exclude/download/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.qima-inc.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.qima-inc.com/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.0"
  resolved "http://registry.npm.qima-inc.com/thenify/download/thenify-3.3.0.tgz#e69e38a1babe969b0108207978b9f62b88604839"
  integrity sha1-5p44obq+lpsBCCB5eLn2K4hgSDk=
  dependencies:
    any-promise "^1.0.0"

throat@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.qima-inc.com/throat/download/throat-5.0.0.tgz#c5199235803aad18754a667d659b5e72ce16764b"
  integrity sha1-xRmSNYA6rRh1SmZ9ZZtecs4Wdks=

through2@^2.0.0:
  version "2.0.5"
  resolved "http://registry.npm.qima-inc.com/through2/download/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

through@2, through@^2.3.4, through@^2.3.6, through@~2.3:
  version "2.3.8"
  resolved "http://registry.npm.qima-inc.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

timers-browserify@^2.0.4:
  version "2.0.11"
  resolved "http://registry.npm.qima-inc.com/timers-browserify/download/timers-browserify-2.0.11.tgz#800b1f3eee272e5bc53ee465a04d0e804c31211f"
  integrity sha1-gAsfPu4nLlvFPuRloE0OgEwxIR8=
  dependencies:
    setimmediate "^1.0.4"

timsort@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/timsort/download/timsort-0.3.0.tgz#405411a8e7e6339fe64db9a234de11dc31e02bd4"
  integrity sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://registry.npm.qima-inc.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.x:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/tmpl/download/tmpl-1.0.4.tgz#23640dd7b42d00433911140820e5cf440e521dd1"
  integrity sha1-I2QN17QtAEM5ERQIIOXPRA5SHdE=

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"
  integrity sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=

to-fast-properties@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/to-fast-properties/download/to-fast-properties-2.0.0.tgz#dc5e698cbd079265bc73e0377681a4e4e83f616e"
  integrity sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=

to-object-path@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.qima-inc.com/to-object-path/download/to-object-path-0.3.0.tgz#297588b7b0e7e0ac08e04e672f85c1f4999e17af"
  integrity sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=
  dependencies:
    kind-of "^3.0.2"

to-regex-range@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-2.1.1.tgz#7c80c17b9dfebe599e27367e0d4dd5590141db38"
  integrity sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=
  dependencies:
    is-number "^3.0.0"
    repeat-string "^1.6.1"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.qima-inc.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-regex@^3.0.1, to-regex@^3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.qima-inc.com/to-regex/download/to-regex-3.0.2.tgz#13cfdd9b336552f30b51f33a8ae1b42a7a7599ce"
  integrity sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=
  dependencies:
    define-property "^2.0.2"
    extend-shallow "^3.0.2"
    regex-not "^1.0.2"
    safe-regex "^1.1.0"

toidentifier@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/toidentifier/download/toidentifier-1.0.0.tgz#7e1be3470f1e77948bc43d94a3c8f4d7752ba553"
  integrity sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=

toposort@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/toposort/download/toposort-2.0.2.tgz#ae21768175d1559d48bef35420b2f4962f09c330"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

tough-cookie@^2.3.3, tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://registry.npm.qima-inc.com/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tough-cookie@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/tough-cookie/download/tough-cookie-3.0.1.tgz#9df4f57e739c26930a018184887f4adb7dca73b2"
  integrity sha1-nfT1fnOcJpMKAYGEiH9K233Kc7I=
  dependencies:
    ip-regex "^2.1.0"
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/tr46/download/tr46-1.0.1.tgz#a8b13fd6bfd2489519674ccde55ba3693b706d09"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/trim-right/download/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"
  integrity sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=

tryer@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/tryer/download/tryer-1.0.1.tgz#f2c85406800b9b0f74c9f7465b81eaad241252f8"
  integrity sha1-8shUBoALmw90yfdGW4HqrSQSUvg=

ts-jest@^25.1.0:
  version "25.3.1"
  resolved "http://registry.npm.qima-inc.com/ts-jest/download/ts-jest-25.3.1.tgz#58e2ed3506e4e4487c0b9b532846a5cade9656ba"
  integrity sha1-WOLtNQbk5Eh8C5tTKEalyt6WVro=
  dependencies:
    bs-logger "0.x"
    buffer-from "1.x"
    fast-json-stable-stringify "2.x"
    json5 "2.x"
    lodash.memoize "4.x"
    make-error "1.x"
    micromatch "4.x"
    mkdirp "1.x"
    resolve "1.x"
    semver "6.x"
    yargs-parser "18.x"

ts-loader@^6.0.4:
  version "6.0.4"
  resolved "http://registry.npm.qima-inc.com/ts-loader/download/ts-loader-6.0.4.tgz#bc331ad91a887a60632d94c9f79448666f2c4b63"
  integrity sha1-vDMa2RqIemBjLZTJ95RIZm8sS2M=
  dependencies:
    chalk "^2.3.0"
    enhanced-resolve "^4.0.0"
    loader-utils "^1.0.2"
    micromatch "^4.0.0"
    semver "^6.0.0"

tslib@^1.9.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/tslib/download/tslib-1.10.0.tgz#c3c19f95973fb0a62973fb09d90d961ee43e5c8a"
  integrity sha1-w8GflZc/sKYpc/sJ2Q2WHuQ+XIo=

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.qima-inc.com/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"
  integrity sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=

tunnel-agent@0.6.0, tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://registry.npm.qima-inc.com/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.qima-inc.com/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  integrity sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=
  dependencies:
    prelude-ls "~1.1.2"

type-detect@4.0.8:
  version "4.0.8"
  resolved "http://registry.npm.qima-inc.com/type-detect/download/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.11.0.tgz#97abf0872310fed88a5c466b25681576145e33f1"
  integrity sha1-l6vwhyMQ/tiKXEZrJWgVdhReM/E=

type-fest@^0.5.0:
  version "0.5.2"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.5.2.tgz#d6ef42a0356c6cd45f49485c3b6281fc148e48a2"
  integrity sha1-1u9CoDVsbNRfSUhcO2KB/BSOSKI=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.qima-inc.com/type-fest/download/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-is@^1.6.16, type-is@~1.6.17, type-is@~1.6.18:
  version "1.6.18"
  resolved "http://registry.npm.qima-inc.com/type-is/download/type-is-1.6.18.tgz#4e552cd05df09467dcbc4ef739de89f2cf37c131"
  integrity sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  resolved "http://registry.npm.qima-inc.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz#a97ee7a9ff42691b9f783ff1bc5112fe3fca9080"
  integrity sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.qima-inc.com/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

underscore@1.10.2:
  version "1.10.2"
  resolved "http://registry.npm.qima-inc.com/underscore/download/underscore-1.10.2.tgz#73d6aa3668f3188e4adb0f1943bd12cfd7efaaaf"
  integrity sha1-c9aqNmjzGI5K2w8ZQ70Sz9fvqq8=

unicode-canonical-property-names-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz#2619800c4c825800efdd8343af7dd9933cbe2818"
  integrity sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=

unicode-match-property-ecmascript@^1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.qima-inc.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz#8ed2a32569961bce9227d09cd3ffbb8fed5f020c"
  integrity sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=
  dependencies:
    unicode-canonical-property-names-ecmascript "^1.0.4"
    unicode-property-aliases-ecmascript "^1.0.4"

unicode-match-property-value-ecmascript@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.1.0.tgz#5b4b426e08d13a80365e0d657ac7a6c1ec46a277"
  integrity sha1-W0tCbgjROoA2Xg1lesemwexGonc=

unicode-property-aliases-ecmascript@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.0.5.tgz#a9cc6cc7ce63a0a3023fc99e341b94431d405a57"
  integrity sha1-qcxsx85joKMCP8meNBuUQx1AWlc=

union-value@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/union-value/download/union-value-1.0.1.tgz#0b6fe7b835aecda61c6ea4d4f02c14221e109847"
  integrity sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=
  dependencies:
    arr-union "^3.1.0"
    get-value "^2.0.6"
    is-extendable "^0.1.1"
    set-value "^2.0.1"

uniq@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/uniq/download/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"
  integrity sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=

uniqs@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/uniqs/download/uniqs-2.0.0.tgz#ffede4b36b25290696e6e165d4a59edb998e6b02"
  integrity sha1-/+3ks2slKQaW5uFl1KWe25mOawI=

unique-filename@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/unique-filename/download/unique-filename-1.1.1.tgz#1d69769369ada0583103a1e6ae87681b56573230"
  integrity sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/unique-slug/download/unique-slug-2.0.2.tgz#baabce91083fc64e945b0f3ad613e264f7cd4e6c"
  integrity sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=
  dependencies:
    imurmurhash "^0.1.4"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"
  integrity sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=

unquote@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/unquote/download/unquote-1.1.1.tgz#8fded7324ec6e88a0ff8b905e7c098cdc086d544"
  integrity sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=

unset-value@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/unset-value/download/unset-value-1.0.0.tgz#8376873f7d2335179ffb1e6fc3a8ed0dfc8ab559"
  integrity sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=
  dependencies:
    has-value "^0.3.1"
    isobject "^3.0.0"

upath@^1.1.1:
  version "1.2.0"
  resolved "http://registry.npm.qima-inc.com/upath/download/upath-1.2.0.tgz#8f66dbcd55a883acdae4408af8b035a5044c1894"
  integrity sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=

upyun@3.3.4:
  version "3.3.4"
  resolved "http://registry.npm.qima-inc.com/upyun/download/upyun-3.3.4.tgz#9edc43c399f48d49a8215ff1da127bd663339573"
  integrity sha1-ntxDw5n0jUmoIV/x2hJ71mMzlXM=
  dependencies:
    axios "^0.16.1"
    base-64 "^0.1.0"
    form-data "^2.1.4"
    hmacsha1 "^1.0.0"
    md5 "^2.2.1"
    mime-types "^2.1.15"

uri-js@^4.2.2:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/uri-js/download/uri-js-4.2.2.tgz#94c540e1ff772956e2299507c010aea6c8838eb0"
  integrity sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=
  dependencies:
    punycode "^2.1.0"

urix@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.qima-inc.com/urix/download/urix-0.1.0.tgz#da937f7a62e21fec1fd18d49b35c2935067a6c72"
  integrity sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=

url@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.qima-inc.com/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  integrity sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

urllib@2.22.0:
  version "2.22.0"
  resolved "http://registry.npm.qima-inc.com/urllib/download/urllib-2.22.0.tgz#2965dc4ae127a6fb695b7db27d3184f17d82cb42"
  integrity sha1-KWXcSuEnpvtpW32yfTGE8X2Cy0I=
  dependencies:
    any-promise "^1.3.0"
    content-type "^1.0.2"
    debug "^2.6.0"
    default-user-agent "^1.0.0"
    digest-header "^0.0.1"
    ee-first "~1.1.1"
    humanize-ms "^1.2.0"
    iconv-lite "^0.4.15"
    qs "^6.4.0"
    statuses "^1.3.1"

use@^3.1.0:
  version "3.1.1"
  resolved "http://registry.npm.qima-inc.com/use/download/use-3.1.1.tgz#d50c8cac79a19fbc20f2911f56eb973f4e10070f"
  integrity sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=

utf8@3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/utf8/download/utf8-3.0.0.tgz#f052eed1364d696e769ef058b183df88c87f69d1"
  integrity sha1-8FLu0TZNaW52nvBYsYPfiMh/adE=

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util.promisify@^1.0.0, util.promisify@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/util.promisify/download/util.promisify-1.0.0.tgz#440f7165a459c9a16dc145eb8e72f35687097030"
  integrity sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=
  dependencies:
    define-properties "^1.1.2"
    object.getownpropertydescriptors "^2.0.3"

util@0.10.3:
  version "0.10.3"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  integrity sha1-evsa/lCAUkZInj23/g7TeTNqwPk=
  dependencies:
    inherits "2.0.1"

util@^0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.qima-inc.com/util/download/util-0.11.1.tgz#3236733720ec64bb27f6e26f421aaa2e1b588d61"
  integrity sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=
  dependencies:
    inherits "2.0.3"

utility@0.1.11:
  version "0.1.11"
  resolved "http://registry.npm.qima-inc.com/utility/download/utility-0.1.11.tgz#fde60cf9b4e4751947a0cf5d104ce29367226715"
  integrity sha1-/eYM+bTkdRlHoM9dEEzik2ciZxU=
  dependencies:
    address ">=0.0.1"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/utils-merge/download/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"
  integrity sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=

uuid@^3.3.2:
  version "3.3.3"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-3.3.3.tgz#4568f0216e78760ee1dbf3a4d2cf53e224112866"
  integrity sha1-RWjwIW54dg7h2/Ok0s9T4iQRKGY=

uuid@^3.3.3:
  version "3.4.0"
  resolved "http://registry.npm.qima-inc.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

v8-compile-cache@^2.0.3:
  version "2.1.0"
  resolved "http://registry.npm.qima-inc.com/v8-compile-cache/download/v8-compile-cache-2.1.0.tgz#e14de37b31a6d194f5690d67efc4e7f6fc6ab30e"
  integrity sha1-4U3jezGm0ZT1aQ1n78Tn9vxqsw4=

v8-to-istanbul@^4.0.1:
  version "4.1.2"
  resolved "http://registry.npm.qima-inc.com/v8-to-istanbul/download/v8-to-istanbul-4.1.2.tgz#387d173be5383dbec209d21af033dcb892e3ac82"
  integrity sha1-OH0XO+U4Pb7CCdIa8DPcuJLjrII=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^1.6.0"
    source-map "^0.7.3"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "http://registry.npm.qima-inc.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@^1.1.2, vary@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/vary/download/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"
  integrity sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=

vendors@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/vendors/download/vendors-1.0.3.tgz#a6467781abd366217c050f8202e7e50cc9eef8c0"
  integrity sha1-pkZ3gavTZiF8BQ+CAuflDMnu+MA=

verror@1.10.0:
  version "1.10.0"
  resolved "http://registry.npm.qima-inc.com/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vm-browserify@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.qima-inc.com/vm-browserify/download/vm-browserify-1.1.0.tgz#bd76d6a23323e2ca8ffa12028dc04559c75f9019"
  integrity sha1-vXbWojMj4sqP+hICjcBFWcdfkBk=

w3c-hr-time@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/w3c-hr-time/download/w3c-hr-time-1.0.1.tgz#82ac2bff63d950ea9e3189a58a65625fedf19045"
  integrity sha1-gqwr/2PZUOqeMYmlimViX+3xkEU=
  dependencies:
    browser-process-hrtime "^0.1.2"

w3c-xmlserializer@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.qima-inc.com/w3c-xmlserializer/download/w3c-xmlserializer-1.1.2.tgz#30485ca7d70a6fd052420a3d12fd90e6339ce794"
  integrity sha1-MEhcp9cKb9BSQgo9Ev2Q5jOc55Q=
  dependencies:
    domexception "^1.0.1"
    webidl-conversions "^4.0.2"
    xml-name-validator "^3.0.0"

walker@^1.0.7, walker@~1.0.5:
  version "1.0.7"
  resolved "http://registry.npm.qima-inc.com/walker/download/walker-1.0.7.tgz#2f7f9b8fd10d677262b18a884e28d19618e028fb"
  integrity sha1-L3+bj9ENZ3JisYqITijRlhjgKPs=
  dependencies:
    makeerror "1.0.x"

watchpack-chokidar2@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/watchpack-chokidar2/download/watchpack-chokidar2-2.0.0.tgz#9948a1866cbbd6cb824dea13a7ed691f6c8ddff0"
  integrity sha1-mUihhmy71suCTeoTp+1pH2yN3/A=
  dependencies:
    chokidar "^2.1.8"

watchpack@^1.6.1:
  version "1.7.2"
  resolved "http://registry.npm.qima-inc.com/watchpack/download/watchpack-1.7.2.tgz#c02e4d4d49913c3e7e122c3325365af9d331e9aa"
  integrity sha1-wC5NTUmRPD5+EiwzJTZa+dMx6ao=
  dependencies:
    graceful-fs "^4.1.2"
    neo-async "^2.5.0"
  optionalDependencies:
    chokidar "^3.4.0"
    watchpack-chokidar2 "^2.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz#a855980b1f0b6b359ba1d5d9fb39ae941faa63ad"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

webpack-bundle-analyzer@^3.3.2:
  version "3.4.1"
  resolved "http://registry.npm.qima-inc.com/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.4.1.tgz#430544c7ba1631baccf673475ca8300cb74a3c47"
  integrity sha1-QwVEx7oWMbrM9nNHXKgwDLdKPEc=
  dependencies:
    acorn "^6.0.7"
    acorn-walk "^6.1.1"
    bfj "^6.1.1"
    chalk "^2.4.1"
    commander "^2.18.0"
    ejs "^2.6.1"
    express "^4.16.3"
    filesize "^3.6.1"
    gzip-size "^5.0.0"
    lodash "^4.17.15"
    mkdirp "^0.5.1"
    opener "^1.5.1"
    ws "^6.0.0"

webpack-merge@^4.2.1:
  version "4.2.2"
  resolved "http://registry.npm.qima-inc.com/webpack-merge/download/webpack-merge-4.2.2.tgz#a27c52ea783d1398afd2087f547d7b9d2f43634d"
  integrity sha1-onxS6ng9E5iv0gh/VH17nS9DY00=
  dependencies:
    lodash "^4.17.15"

webpack-sources@^1.1.0, webpack-sources@^1.4.0, webpack-sources@^1.4.1:
  version "1.4.3"
  resolved "http://registry.npm.qima-inc.com/webpack-sources/download/webpack-sources-1.4.3.tgz#eedd8ec0b928fbf1cbfe994e22d2d890f330a933"
  integrity sha1-7t2OwLko+/HL/plOItLYkPMwqTM=
  dependencies:
    source-list-map "^2.0.0"
    source-map "~0.6.1"

webpack@^4.43.0:
  version "4.43.0"
  resolved "http://registry.npm.qima-inc.com/webpack/download/webpack-4.43.0.tgz#c48547b11d563224c561dad1172c8aa0b8a678e6"
  integrity sha1-xIVHsR1WMiTFYdrRFyyKoLimeOY=
  dependencies:
    "@webassemblyjs/ast" "1.9.0"
    "@webassemblyjs/helper-module-context" "1.9.0"
    "@webassemblyjs/wasm-edit" "1.9.0"
    "@webassemblyjs/wasm-parser" "1.9.0"
    acorn "^6.4.1"
    ajv "^6.10.2"
    ajv-keywords "^3.4.1"
    chrome-trace-event "^1.0.2"
    enhanced-resolve "^4.1.0"
    eslint-scope "^4.0.3"
    json-parse-better-errors "^1.0.2"
    loader-runner "^2.4.0"
    loader-utils "^1.2.3"
    memory-fs "^0.4.1"
    micromatch "^3.1.10"
    mkdirp "^0.5.3"
    neo-async "^2.6.1"
    node-libs-browser "^2.2.1"
    schema-utils "^1.0.0"
    tapable "^1.1.3"
    terser-webpack-plugin "^1.4.3"
    watchpack "^1.6.1"
    webpack-sources "^1.4.1"

whatwg-encoding@^1.0.1, whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.qima-inc.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz#5abacf777c32166a51d085d6b4f3e7d27113ddb0"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-mimetype@^2.2.0, whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz#3d4b1e0312d2079879f826aff18dbeeca5960fbf"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.qima-inc.com/whatwg-url/download/whatwg-url-7.1.0.tgz#c2c492f1eca612988efd3d2266be1b9fc6170d06"
  integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-module@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.qima-inc.com/which-module/download/which-module-2.0.0.tgz#d9ef07dce77b9902b8a3a8fa4b31c3e3f7e6e87a"
  integrity sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=

which@^1.2.10, which@^1.2.9, which@^1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.qima-inc.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1, which@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.qima-inc.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

win-release@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.qima-inc.com/win-release/download/win-release-1.1.1.tgz#5fa55e02be7ca934edfc12665632e849b72e5209"
  integrity sha1-X6VeAr58qTTt/BJmVjLoSbcuUgk=
  dependencies:
    semver "^5.0.1"

with-open-file@^0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.qima-inc.com/with-open-file/download/with-open-file-0.1.6.tgz#0bc178ecab75f6baac8ae11c85e07445d690ea50"
  integrity sha1-C8F47Kt19rqsiuEcheB0RdaQ6lA=
  dependencies:
    p-finally "^1.0.0"
    p-try "^2.1.0"
    pify "^4.0.1"

word-wrap@~1.2.3:
  version "1.2.3"
  resolved "http://registry.npm.qima-inc.com/word-wrap/download/word-wrap-1.2.3.tgz#610636f6b1f703891bd34771ccb17fb93b47079c"
  integrity sha1-YQY29rH3A4kb00dxzLF/uTtHB5w=

wordwrap@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.qima-inc.com/wordwrap/download/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"
  integrity sha1-J1hIEIkUVqQXHI0CJkQa3pDLyus=

worker-farm@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.qima-inc.com/worker-farm/download/worker-farm-1.7.0.tgz#26a94c5391bbca926152002f69b84a4bf772e5a8"
  integrity sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=
  dependencies:
    errno "~0.1.7"

wrap-ansi@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-3.0.1.tgz#288a04d87eda5c286e060dfe8f135ce8d007f8ba"
  integrity sha1-KIoE2H7aXChuBg3+jxNc6NAH+Lo=
  dependencies:
    string-width "^2.1.1"
    strip-ansi "^4.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "http://registry.npm.qima-inc.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrappy@1:
  version "1.0.2"
  resolved "http://registry.npm.qima-inc.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^3.0.0:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz#56bd5c5a5c70481cd19c571bd39ab965a5de56e8"
  integrity sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

write@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.qima-inc.com/write/download/write-1.0.3.tgz#0800e14523b923a387e415123c865616aae0f5c3"
  integrity sha1-CADhRSO5I6OH5BUSPIZWFqrg9cM=
  dependencies:
    mkdirp "^0.5.1"

ws@^6.0.0:
  version "6.2.1"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-6.2.1.tgz#442fdf0a47ed64f59b6a5d8ff130f4748ed524fb"
  integrity sha1-RC/fCkftZPWbal2P8TD0dI7VJPs=
  dependencies:
    async-limiter "~1.0.0"

ws@^7.0.0:
  version "7.2.3"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-7.2.3.tgz#a5411e1fb04d5ed0efee76d26d5c46d830c39b46"
  integrity sha1-pUEeH7BNXtDv7nbSbVxG2DDDm0Y=

ws@^7.1.0:
  version "7.1.2"
  resolved "http://registry.npm.qima-inc.com/ws/download/ws-7.1.2.tgz#c672d1629de8bb27a9699eb599be47aeeedd8f73"
  integrity sha1-xnLRYp3ouyepaZ61mb5Hru7dj3M=
  dependencies:
    async-limiter "^1.0.0"

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.qima-inc.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz#6ae73e06de4d8c6e47f9fb181f78d648ad457c6a"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xml@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.qima-inc.com/xml/download/xml-1.0.1.tgz#78ba72020029c5bc87b8a81a3cfcd74b4a2fc1e5"
  integrity sha1-eLpyAgApxbyHuKgaPPzXS0ovweU=

xmlchars@^2.1.1:
  version "2.2.0"
  resolved "http://registry.npm.qima-inc.com/xmlchars/download/xmlchars-2.2.0.tgz#060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=

xtend@^4.0.0, xtend@~4.0.1:
  version "4.0.2"
  resolved "http://registry.npm.qima-inc.com/xtend/download/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.qima-inc.com/y18n/download/y18n-4.0.0.tgz#95ef94f85ecc81d007c264e190a120f0a3c8566b"
  integrity sha1-le+U+F7MgdAHwmThkKEg8KPIVms=

yallist@^3.0.2:
  version "3.0.3"
  resolved "http://registry.npm.qima-inc.com/yallist/download/yallist-3.0.3.tgz#b4b049e314be545e3ce802236d6cd22cd91c3de9"
  integrity sha1-tLBJ4xS+VF486AIjbWzSLNkcPek=

yargs-parser@18.x:
  version "18.1.2"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-18.1.2.tgz#2f482bea2136dbde0861683abea7756d30b504f1"
  integrity sha1-L0gr6iE2294IYWg6vqd1bTC1BPE=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^16.1.0:
  version "16.1.0"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-16.1.0.tgz#73747d53ae187e7b8dbe333f95714c76ea00ecf1"
  integrity sha1-c3R9U64YfnuNvjM/lXFMduoA7PE=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs-parser@^18.1.1:
  version "18.1.1"
  resolved "http://registry.npm.qima-inc.com/yargs-parser/download/yargs-parser-18.1.1.tgz#bf7407b915427fc760fcbbccc6c82b4f0ffcbd37"
  integrity sha1-v3QHuRVCf8dg/LvMxsgrTw/8vTc=
  dependencies:
    camelcase "^5.0.0"
    decamelize "^1.2.0"

yargs@^15.0.0:
  version "15.3.1"
  resolved "http://registry.npm.qima-inc.com/yargs/download/yargs-15.3.1.tgz#9505b472763963e54afe60148ad27a330818e98b"
  integrity sha1-lQW0cnY5Y+VK/mAUitJ6MwgY6Ys=
  dependencies:
    cliui "^6.0.0"
    decamelize "^1.2.0"
    find-up "^4.1.0"
    get-caller-file "^2.0.1"
    require-directory "^2.1.1"
    require-main-filename "^2.0.0"
    set-blocking "^2.0.0"
    string-width "^4.2.0"
    which-module "^2.0.0"
    y18n "^4.0.0"
    yargs-parser "^18.1.1"

ylru@^1.2.0:
  version "1.2.1"
  resolved "http://registry.npm.qima-inc.com/ylru/download/ylru-1.2.1.tgz#f576b63341547989c1de7ba288760923b27fe84f"
  integrity sha1-9Xa2M0FUeYnB3nuiiHYJI7J/6E8=

yup@^0.27.0:
  version "0.27.0"
  resolved "http://registry.npm.qima-inc.com/yup/download/yup-0.27.0.tgz#f8cb198c8e7dd2124beddc2457571329096b06e7"
  integrity sha1-+MsZjI590hJL7dwkV1cTKQlrBuc=
  dependencies:
    "@babel/runtime" "^7.0.0"
    fn-name "~2.0.1"
    lodash "^4.17.11"
    property-expr "^1.5.0"
    synchronous-promise "^2.0.6"
    toposort "^2.0.2"
