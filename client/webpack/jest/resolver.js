// https://github.com/facebook/jest/issues/2702#issuecomment-338071583

// jest not support package.json module field
// may support latter see this: https://github.com/facebook/jest/issues/9430
// temp resolve
const resolve = require('enhanced-resolve');

function defaultResolver(path, options) {
  const resolveSync = resolve.create.sync({
    extensions: options.extensions,
    modules: options.moduleDirectory,
    mainFields: ['module', 'main'],
  });
  const res = resolveSync(options.basedir, path);
  if (res) {
    return res;
  }

  // 如果 enhanced-resolve 找不到在尝试一次 jest resolve
  return options.defaultResolver(path, options);
}

module.exports = defaultResolver;
