const { removeBabelPluginOrPresetConfig } = require('../utils');
const babelConfig = require('../../rules/babel-options')();

let plugins = [require.resolve('./css-module-import-plugin'), ...(babelConfig.plugins || [])];
plugins = plugins.concat([
  // babel-plugin-lodash 和 babel-plugin-istanbul 使用的时候顺序问题
  require.resolve('babel-plugin-lodash'),
]);

// 单测阶段这些插件不需要了
plugins = removeBabelPluginOrPresetConfig(plugins, 'import-check-babel-plugin');
plugins = removeBabelPluginOrPresetConfig(plugins, 'react-hot-loader/babel');

// @youzan/babel-preset-wsc-pc 在测试阶段先忽略掉
// 问题点 1. babel-plugin-import + 解构 和 istanbul 一起用会导致 compile 的代码无法运行 require 被错误的删除的
// 问题点 2. babel-plugin-zent 和 preset-env babel-plugin-zent 的 require 校验会失败 (开启 esmoudle 依然不行，还有不能替换的定义这些)
const presets = removeBabelPluginOrPresetConfig(babelConfig.presets, '@youzan/babel-preset-wsc-pc');

module.exports = {
  babelrc: false,
  presets,
  plugins,
};
