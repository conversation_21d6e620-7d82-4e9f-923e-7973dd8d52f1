const babelJest = require('babel-jest');
const babelConfig = require('./babel-config');

const amdTransformer = babelJest.createTransformer({
  plugins: [
    // 解析 @youzan/yz-aes amd 模块
    require.resolve('babel-plugin-transform-amd-to-commonjs'),
  ],
});

const esTransformer = babelJest.createTransformer({
  plugins: [require.resolve('@babel/plugin-transform-modules-commonjs')],
});

const projectFileTransformer = babelJest.createTransformer(babelConfig);

module.exports = {
  // 除了 process 还有 缓存相关设置等，直接当做项目内容处理
  ...projectFileTransformer,
  process(...args) {
    const [content, filename] = args;
    if (/node_modules/.test(filename)) {
      // amd 模块
      if (filename.indexOf('@youzan/yz-aes') !== -1 || /define\(/.test(content)) {
        return amdTransformer.process(...args);
      } else if (/(import|export)\s/.test(content)) {
        return esTransformer.process(...args);
      }

      return content;
    } else {
      return projectFileTransformer.process(...args);
    }
  },
};
