// 将 import * as s from './style.m.scss' 调整为 import s from './style.m.scss'
// 1. css 通过 moduleMapper 指向 identity-obj-proxy
// 2. import * as s from 'identity-obj-proxy' 使用 import * as 的时候由于 identity-obj-proxy 是输出一个 Proxy 对象，访问属性都是 undefined
// 3. 调整为 import s from 'identity-obj-proxy'
module.exports = () => {
  return {
    visitor: {
      ImportDeclaration(path) {
        const node = path.node;
        const specifiers = node.specifiers;
        const source = node.source;

        if (/\.m\.scss$/.test(source.value)) {
          if (specifiers.length === 1 && specifiers[0].type === 'ImportNamespaceSpecifier') {
            specifiers[0].type = 'ImportDefaultSpecifier';
          }
        }
      },
    },
  };
};
