const path = require('path');
const smartMerge = require('webpack-merge');
const babelConfig = require('./transform/babel-config');
const { transformWebpackAlias, readDirFilePaths, getAppRoot } = require('./utils');
const { getAppWebpackProductionConfig } = require('../utils/helper');
const webpackProdConfig = smartMerge(
  require('../config/base.prod.config'),
  getAppWebpackProductionConfig()
);

module.exports = {
  rootDir: getAppRoot(),
  resolver: path.resolve(__dirname, './resolver.js'),
  setupFiles: readDirFilePaths(path.resolve(__dirname, './setup')),
  transform: {
    '\\.m?jsx?$': require.resolve('./transform/transformjs'),
    // jest process & ts-jest process
    // https://kulshekhar.github.io/ts-jest/tech/process/
    // 使用 preset 配置的时候依旧会从 rootDir 目录查询模块，需要 client 仓库内安装
    '\\.tsx?$': require.resolve('ts-jest'),
  },
  globals: {
    // // https://kulshekhar.github.io/ts-jest/user/config/babelConfig
    'ts-jest': {
      babelConfig,
    },
  },
  transformIgnorePatterns: [
    // jest 默认的配置是 ['/node_modules/'] 下的内容都不在 transform，但是由于部分模块没有打包，或者要走 esmodule 的形式 还是需要转一些东西的
    // 方案 1 是按需打包 commit b25c2bca695265c5c02c8756d2faddd7c98dbf1b
    //    好处：按需引入打包，速度更快
    //    不足：使用方需要感知那个包引入导致用例失败，然后再将对应包添加到配置
    // 方案 2 所有模块都打包，过基本的 babel 插件
    //    好处：理想情况下使用方不感知那些包需要 transform
    //    不足：1. 增加单测运行时间  2. 遇到无法解析的包需要升级工具

    // 这边是采用的方案 2 的形式，babel/runtime 下面的内容不能在 transform 了，再 transform 就会找不到内容了
    // eslint-disable-next-line
    '/@babel\/runtime/'
  ],
  testMatch: ['**/?*.spec.(j|t)s?(x)'],
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json'],
  setupFilesAfterEnv: readDirFilePaths(path.resolve(__dirname, 'setupAfterEnv')),
  moduleNameMapper: {
    '^.+(\\.m)?\\.s?css$': require.resolve('identity-obj-proxy'),
    // Webpack alias
    ...transformWebpackAlias(webpackProdConfig.resolve.alias || {}),
    // 直接使用 esm 的时候由于实际运行是 commonjs 的环境，esm 的代码可能会被再次转码
    // 从而导致 stackoverflow
    '@babel/runtime/helpers/esm/(.*)': '@babel/runtime/helpers/$1',
  },
  testPathIgnorePatterns: ['/node_modules/'],
  coveragePathIgnorePatterns: ['/node_modules/', '/__mock__/'],
  coverageDirectory: path.resolve(__dirname, '../../../coverage/'),
  // https://istanbul.js.org/docs/advanced/alternative-reporters/
  // cobertura 是给 ops 平台统计使用的, html 是查看详情版本, text-summary 是命令行执行输出结果
  // ops 相关内容可以找 @元华 @余霞 确认
  coverageReporters: ['cobertura', 'html', 'text-summary'],
  // 测试描述文件，用于 ops 平台判断当前测试的 通过/失败 情况
  reporters: [
    'default',
    [
      require.resolve('jest-junit'),
      {
        outputDirectory: path.resolve(__dirname, '../../../'),
        outputName: 'results.xml',
      },
    ],
  ],

  verbose: true,
  timers: 'fake',
};
