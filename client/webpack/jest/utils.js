const fs = require('fs');
const path = require('path');
const { options: JestCliOptions } = require('jest-cli/build/cli/args');
const log = require('../utils/log');

/**
 * 将 webpack alias 设置转为 jest moduleNameMapper 可识别的形式
 * @param {object} webpackAlias webpack alias 设置
 * @returns {object}
 */
const transformWebpackAlias = (webpackAlias = {}) => {
  const moduleNameMapper = {};
  for (const key in webpackAlias) {
    if (key) {
      moduleNameMapper[`^${key}(.*)$`] = `${webpackAlias[key]}$1`;
    }
  }
  return moduleNameMapper;
};

/**
 * 将 babel plugin/preset 转为统一的 数组 形式
 * @param {string | array} plugin plugin、preset 配置
 * @returns {array}
 */
const getBabelPluginOrPreset = plugin => {
  if (Array.isArray(plugin)) {
    return plugin;
  }
  if (typeof plugin === 'string') {
    return [plugin, {}];
  }

  log.warn('Jest 配置插件错误 plugin config:', plugin);
  return null;
};

/**
 * 忽略指定 babel plugin/preset
 * @param {array} pluginsOrPresets presets 配置或 plugins 配置
 * @param {string} targetName 目标 plugin/preset 名称
 */
const removeBabelPluginOrPresetConfig = (pluginsOrPresets, targetName, config = {}) => {
  const plugins = pluginsOrPresets.map(getBabelPluginOrPreset);
  return plugins.filter(plugin => {
    const [pluginName = ''] = plugin;
    return !pluginName.includes(targetName);
  });
};

/**
 * 获取 client 目录
 */
const getAppRoot = () => path.join(__dirname, '../../');

/**
 * 获取 projects，用于支持只测试指定目录下的内容
 * @param {string} entry 入口，client/xxx 中的 xxx，支持多个目录，以 空格 分割
 * @returns {array} projects
 */
const getRoots = (entry = '') => {
  const appPath = getAppRoot();
  if (!entry || typeof entry !== 'string') {
    return [appPath];
  }
  return entry.split(',').map(entryPath => {
    return path.join(appPath, entryPath);
  });
};

/**
 * 给定路径获取 jest.config.js
 * @param {string} path 获取 jest.config.js 配置的路径
 * @returns {object}
 */
const getJestConfig = pathStr => {
  if (pathStr.endsWith('jest.config.js')) {
    return readConfig(pathStr);
  }

  return readConfig(path.join(pathStr, 'jest.config.js'));
};

/**
 * @param {string} path 文件路径
 * @param {object} defaultRes 默认结果
 */
const readConfig = (pathStr = '', defaultRes = {}) => {
  let res = defaultRes;
  const exist = fs.existsSync(pathStr);

  if (!exist) {
    log.warn(`file ${pathStr} not exist`);
    return res;
  }

  try {
    res = require(pathStr);
  } catch (err) {
    log.error(`readConfig: ${pathStr} failed, please check is file exist`, err);
  }

  return res;
};

/**
 * alias 为入参变量的缩写，jest.runCLI 的入参变量名不是缩写形式，所以需要转成完整参数名
 * JestCliOptions 其中一个 key testNamePattern 对应的示例: { alias: 't', type: 'string' }
 * 示例：将 -c 转为 --config，-o 转为 --onlyChanged
 * @param {object} configs cli 配置对象
 * @returns {object} runCLI 可识别的完整参数
 */
const transformJestAliasConfig = (configs = {}) => {
  const res = { ...configs };
  Object.keys(JestCliOptions).forEach(optKey => {
    const { alias } = JestCliOptions[optKey] || {};
    if (alias && res[alias]) {
      res[optKey] = res[alias];
      delete res[alias];
    }
  });
  return res;
};

/**
 * 读取一个目录下的所有文件信息
 * @param {string} dirPath 读取路径
 */
const readDirFilePaths = dirPath => {
  if (!dirPath) return [];
  const exist = fs.existsSync(dirPath);
  if (exist) {
    const filePaths = fs.readdirSync(dirPath);
    return filePaths.map(filePath => {
      return path.join(dirPath, filePath);
    });
  }
  return [];
};

/**
 * 删除文件
 * @param {string}} path 文件路径
 */
const removeFile = path => {
  if (!fs.existsSync(path)) {
    return true;
  }
  fs.unlinkSync(path);
  return true;
};

module.exports = {
  transformWebpackAlias,
  getBabelPluginOrPreset,
  removeBabelPluginOrPresetConfig,
  getRoots,
  getAppRoot,
  getJestConfig,
  readDirFilePaths,
  transformJestAliasConfig,

  readConfig,
  removeFile,
};
