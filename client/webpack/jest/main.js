const jest = require('jest');
const yParser = require('yargs-parser');
const merge = require('webpack-merge');
const utils = require('./utils');
const defaultJestConfig = require('./jest.config');
const log = require('../utils/log');

function main(originCliArgs = {}) {
  const entry = originCliArgs.entry || '';
  delete originCliArgs.entry;

  const appPath = utils.getAppRoot();
  const appJestConfig = utils.getJestConfig(appPath);
  const roots = utils.getRoots(entry);
  const runCLIArgs = utils.transformJestAliasConfig(originCliArgs);

  const runtimeJestConfig = merge.smart(
    defaultJestConfig,
    { rootDir: appPath, roots },
    appJestConfig
  );

  return jest
    .runCLI(
      {
        // jest-config/index.ts readConfig config 只能支持 JSON 字符串
        // 否则从 appPath 读取 jest.config.js 同时设置 appPath
        config: JSON.stringify(runtimeJestConfig),
        ...runCLIArgs,
      },
      [appPath]
    )
    .then(result => {
      const { results } = result;
      if (results.success) {
        log.success('all test passed');
      } else {
        throw new Error('test failed, check step logs for more info');
      }
    });
}

const args = yParser(process.argv.slice(2), {
  alias: {
    watch: ['w'],
  },
});

main({
  ...args,
}).catch(err => {
  log.error(err);
  process.exit(1);
});
