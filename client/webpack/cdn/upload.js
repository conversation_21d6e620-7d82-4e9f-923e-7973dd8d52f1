const path = require('path');
const glob = require('glob');
const cdn = require('@youzan/superman-cdn');
const logSymbols = require('log-symbols');
const helper = require('../utils/helper');
const log = require('../utils/log');

function run() {
  const localAssetDir = helper.getOutputPath();
  const cdnAssetDir = helper.getAppName();

  glob(
    '**/*',
    {
      cwd: localAssetDir,
      nodir: true,
      ignore: ['**/*.map', '**/*.json'],
    },
    (err, assets) => {
      if (err) {
        log.panic(`${logSymbols.error} Failed to glob assets for upload ${err}`);
      }

      const filesToUpload = assets.map(f => {
        const cdnPath = path.join(cdnAssetDir, f);
        const localPath = path.join(localAssetDir, f);
        return {
          cdnPath: path.dirname(cdnPath),
          localPath,
        };
      });

      cdn
        .uploadOrigin({
          fileList: filesToUpload,
        })
        .catch(err => {
          log.panic(`${logSymbols.error} CDN 上传失败\n${JSON.stringify(err, null, 2)}`);
        });
    }
  );
}

run();
