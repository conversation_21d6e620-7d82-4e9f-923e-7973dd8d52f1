const merge = require('webpack-merge');
const helper = require('../utils/helper');

/**
 * babel-loader sets `caller: { supportsStaticESM: true, supportsDynamicImport: true }` by default.
 * It affects all presets and plugins, they will skip compilation of ES modules into CommonJS modules.
 *
 * https://github.com/babel/babel-loader/blob/15df92fafd58ec53ba88efa22de7b2cee5e65fcc/src/injectCaller.js#L9-L13
 *
 * If you use this file outside babel-loader, make sure you do the same.
 */
function getBabelOptions() {
  const appConfig = helper.getAppBabelRC();

  const plugins = [
    [require.resolve('@babel/plugin-proposal-decorators'), { decoratorsBeforeExport: true }],
    require.resolve('@babel/plugin-proposal-class-properties'),
    require.resolve('@babel/plugin-proposal-export-default-from'),
    require.resolve('@babel/plugin-syntax-dynamic-import'),
    require.resolve('@babel/plugin-proposal-export-namespace-from'),
    [
      require.resolve('@babel/plugin-transform-runtime'),
      {
        corejs: false,
        helpers: true,
        regenerator: true,
        useESModules: true,
      },
    ],
    require.resolve('@babel/plugin-proposal-nullish-coalescing-operator'),
    require.resolve('@babel/plugin-proposal-optional-chaining'),
    require.resolve('../plugin/import-check-babel-plugin'),
  ];

  // react-hot-loader 是否加载 production 模式的代码判断逻辑有点问题
  // 遇到 dll 没有开启 HMR 的会加载 production 的代码
  if (!helper.isDll() && helper.isDev()) {
    plugins.push(require.resolve('react-hot-loader/babel'));
  }

  return merge(
    {
      presets: [
        require.resolve('@babel/preset-env'),
        require.resolve('@babel/preset-react'),
        [
          require.resolve('@youzan/babel-preset-wsc-pc'),
          {
            style: true,
            useESModules: true,
          },
        ],
      ],
      plugins,
      babelrc: false,

      // The following are babel-loader specific options
      cacheDirectory: helper.getBabelLoaderCacheDir(),
      cacheCompression: false,
    },
    appConfig
  );
}

module.exports = getBabelOptions;
