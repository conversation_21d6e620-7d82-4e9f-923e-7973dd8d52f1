const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { isDev } = require('../utils/helper');
const cache = require('./cache');

function getCssRule() {
  const cssLoader = {
    loader: require.resolve('css-loader'),
    options: {
      sourceMap: isDev(),
      importLoaders: 0,
    },
  };
  const cacheLoader = cache.getCacheLoader();
  const loaders = [getMiniCssExtractPluginLoader(), cacheLoader, cssLoader];

  return {
    test: /\.css$/,
    use: loaders,
  };
}

function getMiniCssExtractPluginLoader() {
  return {
    loader: MiniCssExtractPlugin.loader,
    options: {
      hmr: isDev(),

      // if hmr does not work, this is a forceful method.
      reloadAll: true,
    },
  };
}

module.exports = {
  getCssRule,
  getMiniCssExtractPluginLoader,
};
