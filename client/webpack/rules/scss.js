const path = require('path');
const cache = require('./cache');
const { isDev } = require('../utils/helper');
const { getMiniCssExtractPluginLoader } = require('./css');

function getScssRule(cssModuleMode) {
  const sassLoader = {
    loader: require.resolve('sass-loader'),
    options: {
      sassOptions: {
        includePaths: [
          // If you want to @import css inside node_modules, just prepend a `~`
          // e.g. ~package/lib/index.css
          path.resolve(__dirname, '../../sass'),
          path.resolve(__dirname, '../../shared/sass'),
        ],
      },
      sourceMap: isDev(),
    },
  };

  const cacheLoader = cache.getCacheLoader();

  const cssLoader = {
    loader: require.resolve('css-loader'),
    options: {
      modules: cssModuleMode
        ? {
            mode: cssModuleMode,
            localIdentName: isDev() ? '[local]--[hash:base64:4]' : '[hash:base64:4]',
          }
        : false,
      sourceMap: isDev(),
      importLoaders: 1,
      localsConvention: 'asIs',
    },
  };

  const loaders = [getMiniCssExtractPluginLoader(), cacheLoader, cssLoader, sassLoader];

  let test;
  if (cssModuleMode === 'local') {
    // .m.scss
    test = mod => mod.endsWith('.m.scss') && !mod.endsWith('.global.m.scss');
  } else if (cssModuleMode === 'global') {
    // .global.m.scss
    // 这个后缀是为了兼容 css-loader 2.0 之前的版本
    // css-loader 2.0 之前的版本 css module 无法关闭
    test = mod => mod.endsWith('.global.m.scss');
  } else {
    // .scss 不支持 css module
    test = mod => mod.endsWith('.scss') && !mod.endsWith('.m.scss');
  }

  return {
    test,
    use: loaders,
  };
}

module.exports = {
  getScssRule: () => getScssRule(false),
  getScssModuleRule: () => getScssRule('local'),
  getScssModuleGlobalRule: () => getScssRule('global'),
};
