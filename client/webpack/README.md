## 原则

- 支持自定义 dll 拆分
- 支持 React 开发，JavaScript 以及 TypeScript 都支持
- 样式支持 SASS 以及 CSS module
- 能不用 PostCSS 尽量不要用
- 还未正式发布的 ES 提案原则上不支持，除了个别基本稳定并且需求大的

## 模版项目

新项目或者老项目需要升级请参考[这个仓库](http://gitlab.qima-inc.com/fe-templates/wsc-pc-project-template)。
模版仓库的 Makefile 里提供了项目级别的开发命令，运行 make 查看所有可用命令。

如果遇到缺少包的错误，请跟模版仓库比对一下，package.json 的依赖是否已经更新。

如果遇到缺少 TypeScript 配置的报错，请检查 clients/tsconfig.json 以及 client/tslint.json 是否存在。

## 必要配置

- 前端代码的根目录 package.json 的 name 必须项目唯一，比如 wsc-pc-ump 仓库的 name 就是 wsc-pc-ump
- 项目全局代码(global.js/global.css) 放在前端代码 pages/global 里面维护
- .browserslistrc 放在项目前端代码根目录

## 需要自己安装的包

- `react-hot-loader` 必须安装在项目前端代码的根目录中
- `typescript` 需要安装在项目根目录

## 关于 CSS Modules 的支持

如果需要使用 CSS Modules 请使用 `.m.scss` 后缀，`.css` 以及 `.scss` 不支持 CSS Modules。

## 可选配置

以下配置文件均放在前端代码根目录下

- dll.config.js
- webpack.config.dev.js
- webpack.config.prod.js
- postcss.config.js
- .babelrc
- jest.config.js

## 使用

#### 开发模式

- `scripts/dev [-e a] [-e b]`，`-e` 用来指定入口，直接传 pages 下的子目录即可，无需完整路径；如果需要同时打包多个入口传入多个 `-e` 即可，也可以传一个 glob 进来，glob 注意用单引号扩起来，否则会被 shell 展开，例如 `scripts/dev -e showcase -e 'dashboard/a**'`
- `scripts/dev --no-dll` 不打 DLL，速度会快一些，如果确认没有改 DLL 的代码可以用这个
- `scripts/dev` 全量打包，很慢，不建议使用。

#### 生产环境

- `scripts/build`

#### 打包结果分析

传入相对 `static` 目录的 `stats.json` 文件路径即可，省略 `stats.json` 后缀。

- `scripts/analyze local/app`
- `scripts/analyze build/vendor`


#### 单元测试

- `scripts/test` 启动单测，默认检查所有 client 目录下 `.spec.[tj]sx?` 文件
- `scripts/test --entry=pages/a,pages/b,components` 指定目录单测，注意与 `dev` 命令的传参差异；
- `scripts/test [--no-cache]` 除 entry 外还支持其他 [jest cli 参数](https://jestjs.io/docs/en/cli)

> 当需要修改默认 jest 配置的时候可以在 `client` 目录下新增 `jest.config.js` 具体配置参考 [jest config](https://jestjs.io/docs/en/configuration)


## 前端目录结构

```text
- clients
  - pages(页面入口)
    - global
      - main.js (import './style.scss')
      - style.scss
  - fns(公用函数)
  - components(公用组件)
  - constants(全局常量)
  - sass(只有mixin)
  - shared (@wsc-fe-pc-shared)
  - webpack (@wsc-fe-pc-build)
  - design-components (可选 @wsc-fe-pc-design-components)
```

## version 文件路径规则

- wsc-pc-[name]/yy/xxx.js
- wsc-pc-[name]/yy/xxx.css

## 缓存

打包时 `dev` 和 `build` 的缓存目录是分开的，`scripts/clean-cache` 脚本可以用来清空缓存。

## 自定义配置

主要的三个依赖：babel 和 webpack 都支持合并用户自定义配置，创建对应文件即可。并不是所有内置的配置都可以被安全覆盖的，使用时后果自负。

- babel: `client/.babelrc`
- webpack: `client/webpack.config.dev.js` 以及 `client/webpack.config.prod.js`

## 自定义 dll 配置

打包过程中会自动将 React 全家桶打入 `vendor.js`，部分 zent 组件、ajax 包以及 `zan-shuai` 会自动打入 `base.js` 中。

dll 支持自定义，在 client 目录下放一个 `dll.config.js`。

每个仓库都有自己的特点，像数据仓库大概率每个页面都会用到 `echarts` 来绘图，这时候可以把 `echarts` 相关的包放到 vendor 中，以减少每个页面中的重复代码，配置 `extraVendorPackages: ['echarts-for-react']` 即可。

```
module.exports = {
  // 判断一个包是否是有赞的公用包，默认 @youzan/ 和 zan- 开头的都是
  isBasePackage(pkg) {},

  // 判断一个包是否来自第三方，默认 !isBasePackage
  isVendorPackage(pkg) {},

  // 需要在 base 和 vendor 两个 dll 中忽略的包
  exclude(pkg) {},

  // 额外的需要放进 base 的包
  extraBasePackages: [],

  // 额外的需要放进 vendor 的包
  extraVendorPackages: [],

  // 这两个用于强制覆盖 vendor 和 base 中的包
  // 通常使用 extraBasePackages 和 extraVendorPackages 足够了
  overrideVendorPackages: [],
  overrideBasePackages: [],

  // 自定义 dll，数量任意
  // 注意 dll 之间不要有相同的依赖，相同的依赖会被重复打
  extraEntry: {
    echarts: ['echarts'],
    foobar: ['foobar']
  },

  // 这里注意：dll 是逐个按顺序打的，并非一次性一起生成的
  // dll 的打包顺序，数字越小优先级越高，优先级高的先打，并且会作为之后打的 dll 的 dll-reference 依赖
  // 数字从 101 开始
  // base 是 100，vendor 是 0
  // 通过控制数字大小可以调整 dll 的打包顺序
  // 下面这个例子里，foobar 这个 dll 是可以依赖 echarts 的，
  // 但是反过来 echarts 这个 dll 不能依赖 foobar，如果顺序写错了 foobar 会在两个 dll 中都出现
  orders: {
    echarts: 101,
    foobar: 102
  }
};
```

## 环境变量

如果使用 `scripts` 目录下的脚本执行打包的话会默认设置几个环境变量。

- `process.env.NODE_ENV`: production | development
- `process.env.BUILD_TYPE`: 如果是打 DLL 值是 dll
