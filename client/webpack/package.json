{"name": "zan-build", "description": "build scripts for youzan PC projects", "version": "2.3.0", "license": "MIT", "scripts": {"release": "./scripts/release.sh"}, "dependencies": {"@babel/core": "^7.5.4", "@babel/plugin-proposal-class-properties": "^7.5.0", "@babel/plugin-proposal-decorators": "^7.4.4", "@babel/plugin-proposal-export-default-from": "^7.5.2", "@babel/plugin-proposal-export-namespace-from": "^7.5.2", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.7.4", "@babel/plugin-proposal-optional-chaining": "^7.7.4", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.9.0", "@babel/plugin-transform-runtime": "^7.5.0", "@babel/preset-env": "^7.5.4", "@babel/preset-react": "^7.0.0", "@youzan/babel-preset-wsc-pc": "2.0.7", "@youzan/es-guard": "2.0.0", "@youzan/superman-cdn": "3.0.0", "@youzan/webpack-plugin-serve": "^0.12.3", "acorn": "^7.0.0", "acorn-walk": "^7.0.0", "autoprefixer": "^9.6.1", "babel-jest": "^25.0.0", "babel-loader": "^8.0.6", "babel-plugin-transform-amd-to-commonjs": "^1.4.0", "cache-loader": "^4.0.1", "case-sensitive-paths-webpack-plugin": "^2.2.0", "chalk": "^2.4.2", "compare-versions": "^3.5.0", "cross-env": "^5.2.0", "css-loader": "^3.0.0", "cssnano": "^4.1.10", "cssnano-preset-default": "^4.0.7", "enhanced-resolve": "^4.1.0", "fibers": "^4.0.1", "glob": "^7.1.4", "identity-obj-proxy": "^3.0.0", "inquirer": "^6.5.0", "jest": "^25.1.0", "jest-cli": "^25.1.0", "jest-junit": "^10.0.0", "lodash": "^4.17.15", "log-symbols": "^3.0.0", "mini-css-extract-plugin": "^0.8.0", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "node-source-walk": "^4.2.0", "optimize-css-assets-webpack-plugin": "^5.0.3", "ora": "^3.4.0", "rimraf": "^2.6.3", "sass": "^1.22.5", "sass-loader": "^8.0.0", "terser-webpack-plugin": "^1.3.0", "ts-jest": "^25.1.0", "ts-loader": "^6.0.4", "v8-compile-cache": "^2.0.3", "webpack": "^4.43.0", "webpack-bundle-analyzer": "^3.3.2", "webpack-merge": "^4.2.1", "yargs-parser": "^16.1.0", "@youzan/sentry-sourcemap-uploader": "^1.0.6"}, "devDependencies": {"babel-eslint": "^10.0.2", "eslint": "^6.3.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^6.2.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-react": "^7.14.3", "husky": "^1.3.0", "lint-staged": "^8.1.0", "prettier": "^1.14.2", "speed-measure-webpack-plugin": "^1.3.1"}}